﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Framework.Data.Domains
{
    public partial class TaxCategories : _BaseDomain
    {
        public TaxCategories()
        {
            TaxRate = new HashSet<TaxRate>();
        }
        public required string Code { get; set; }
        public required string Name { get; set; }
        public string? Description { get; set; }
        public virtual ICollection<TaxRate> TaxRate { get; set; }
    }
}
