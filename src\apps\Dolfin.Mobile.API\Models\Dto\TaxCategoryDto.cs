﻿using Dolfin.Framework.Data.Domains;

namespace Dolfin.Mobile.API.Models.Dto
{
    public class TaxCategoryDto
    {
        public TaxCategoryDto()
        {
            //TaxRate = new HashSet<TaxRateDto>();
        }
        public Guid Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public virtual ICollection<TaxRateDto> TaxRate { get; set; }
    }
}
