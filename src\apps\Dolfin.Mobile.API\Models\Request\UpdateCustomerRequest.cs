﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Dolfin.Utility.Enum.Enums;

namespace Dolfin.Framework.Data.Domains
{
    public partial class UpdateCustomerRequest
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string? Description { get; set; }
        public bool IsTaxExempt { get; set; }
        public bool IsPICEditable { get; set; }
        public string DefaultPIC { get; set; }
        public string ReferralCode { get; set; }
        public string AccountCode { get; set; }
        public string? Remark { get; set; }
        public Guid? IdentityTypeId { get; set; }
		public string? IdentityNo { get; set; }
		public string? FullName { get; set; }
		public string? Email { get; set; }
		public string? TinNo { get; set; }
		public TinVerifyStatusEnum TinVerifyStatus { get; set; } = TinVerifyStatusEnum.Unverified;
    }
}
