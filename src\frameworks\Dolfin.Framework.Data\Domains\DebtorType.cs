﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Framework.Data.Domains
{
    public partial class DebtorType : _BaseDomain
    {
        public DebtorType()
        {
            Customer = new HashSet<Customer>();
        }
        public required string Code { get; set; }
        public required string Name { get; set; }
        public virtual ICollection<Customer> Customer { get; }
    }
}
