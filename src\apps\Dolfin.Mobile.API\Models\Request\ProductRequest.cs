﻿using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using static Dolfin.Utility.Enum.Enums;

namespace Dolfin.Mobile.API.Models.Request
{
    public class ProductRequest
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string? Description { get; set; }
        public bool IsTaxExempt { get; set; }
        public bool IsTaxExcl { get; set; }
        public Guid? CustomSalesTaxNo { get; set; }
        public Guid? CustomServiceTaxNo { get; set; }
        public string Sku { get; set; }
        public decimal? Weight { get; set; }
        public decimal? Length { get; set; }
        public decimal? Width { get; set; }
        public decimal? Height { get; set; }
        public string AccountCode { get; set; }
        public DateTime? AvailableStartAt { get; set; }
        public DateTime? AvailableEndAt { get; set; }
        public int? DisplayOrder { get; set; }
        public bool Published { get; set; }
        public Guid? CompanyId { get; set; }
        public Guid ProductCategoryId { get; set; }
        public Guid ProductCostMethodId { get; set; }
        public Guid CurrencyId { get; set; }
        public List<ProductUOMInputRequest> ProductUOMInputRequest { get; set; }
    }

    public class ProductUOMInputRequest
    {
        [JsonIgnore]
        public string? Code { get; set; }

        [JsonIgnore]
        public string? Name { get; set; }

        [JsonIgnore]
        public decimal FractionTotal { get; set; }

        [Required(ErrorMessage = "Fraction is required")]
        [Range(1.00, double.MaxValue, ErrorMessage = "Fraction must be at least 1")]
        public decimal Fraction { get; set; }

        [Required(ErrorMessage = "Barcode is required")]
        public string Barcode { get; set; }

        public bool IsMainUom { get; set; }
        public bool IsPriceFollowUomMainId { get; set; }
        public decimal? Cost { get; set; }
        public decimal? PreviousCost { get; set; }
        public decimal? OrderMinQty { get; set; }
        public decimal? OrderMaxQty { get; set; }
        public bool PriceEditable { get; set; }
        public decimal? MinEditPrice { get; set; }
        public decimal? MaxEditPrice { get; set; }

        [Required(ErrorMessage = "UOM Primary ID is required")]
        public Guid UomPrimaryId { get; set; }

        public Guid? UomSecondaryId { get; set; }

        [JsonIgnore]
        public Guid ProductId { get; set; }

        public ProductPriceInputRequest ProductPriceInputRequest { get; set; }
    }

    public class ProductPriceInputRequest
    {
        [Range(1.00, double.MaxValue, ErrorMessage = "FractionQty must be at least 1")]
        public decimal? FractionQty { get; set; } = 1.00m;
        [Required(ErrorMessage = "Price is required")]
        [Range(0.00, double.MaxValue, ErrorMessage = "Price must be greater than 0")]
        public decimal Price { get; set; }

        // EffectiveAt is not required, will be set to UTC now by default
        public DateTime? EffectiveAt { get; set; }

        public string? Remark { get; set; }
    }
}
