﻿using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Mobile.API.Models.Dto
{
    public class ApplicationUserDto
    {
        public bool IsAllowEditable { get; set; }
        public DateTime PasswordExpireAt { get; set; }
        public required string PhoneNo1 { get; set; }
        public string? PhoneNo2 { get; set; }
        public string? FaxNo1 { get; set; }
        public string? FaxNo2 { get; set; }
        public string? SerialNo { get; set; }
        public DateTime LastLoginAt { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public Guid CreatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public Guid? UpdatedBy { get; set; }
    }
}
