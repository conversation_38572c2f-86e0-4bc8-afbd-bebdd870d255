using Dolfin.Framework.Data.Entity;
using Dolfin.Framework.Repository.Implementations;

namespace Dolfin.Mobile.API.Repositories
{
    /// <summary>
    /// Unit of Work implementation for the API project
    /// </summary>
    public class UnitOfWork : UnitOfWork<DolfinDbContext>
    {
        /// <summary>
        /// Constructor
        /// </summary>
        public UnitOfWork(
            DolfinDbContext context,
            IServiceProvider serviceProvider)
            : base(context, serviceProvider)
        {
        }
    }
}