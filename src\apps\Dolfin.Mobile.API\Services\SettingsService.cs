﻿using AutoMapper;
using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Entity;
using Dolfin.Mobile.API.Models.Request;
using Dolfin.Utility.Enum;
using Dolfin.Utility.Models;
using Dolfin.Utility.Utils;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using static Dolfin.Utility.Constant.Constant;
using static Dolfin.Utility.Enum.Enums;

namespace Dolfin.Mobile.API.Services
{
    public class SettingsService : BaseComponent<Settings>, ISettingsService
    {
        private StandardMessage _standardMessage;
        private IMapper _mapper;

        public SettingsService(DbContextOptions<DolfinDbContext> dbContextOptions, IMapper mapper) : base(dbContextOptions)
        {
            _standardMessage = new StandardMessage();
            _mapper = mapper;
        }

        public async Task<List<Settings>> GetSettings(bool? isActive)
        {
            var query = GetDbContext().Set<Settings>().AsQueryable();

            if (isActive != null) 
            {
                query = query.Where(x => x.IsActive == isActive);
            }

            return await query.ToListAsync();
        }


        public async Task<BaseResponse<ResultId>> InsertAddress(AddressCompanyRequest reqBody, Guid? userId = null, DolfinDbContext dbContextRollback = null)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };
            try
            {
                var newAddress = _mapper.Map<Address>(reqBody);
                newAddress.IsActive = true;
                newAddress.CreatedAt = DateTime.UtcNow;
                newAddress.CreatedBy = userId ?? AdminConfiguration.SystemUser;

                var address = await CreateAsync<Address>(newAddress, dbContextRollback);
                result.Result = new ResultId { Id = address.Id };
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }
    }
}
