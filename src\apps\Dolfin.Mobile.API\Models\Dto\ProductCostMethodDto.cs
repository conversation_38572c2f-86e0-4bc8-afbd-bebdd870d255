﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Mobile.API.Models.Dto
{
    public partial class ProductCostMethodDto
    {
        public ProductCostMethodDto()
        {
            //Product = new HashSet<Product>();
        }
        public Guid Id { get; set; }
        public required string Code { get; set; }
        public required string Name { get; set; }
        public string? Description { get; set; }
        //public virtual ICollection<Product> Product { get; }

    }
}
