﻿using Dolfin.Mobile.API.Models.Request;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using static Dolfin.Utility.Enum.Enums;

namespace Dolfin.Framework.Data.Domains
{
    public partial class CustomerRequest
    {
        public string? Code { get; set; }
        public required string Name { get; set; }
        public string? Description { get; set; }
        public bool IsTaxExempt { get; set; }
        public bool IsPICEditable { get; set; }
        public required string DefaultPIC { get; set; }
        [JsonIgnore]
        public string? ReferralCode { get; set; }
        public required string AccountCode { get; set; }
        public string? Remark { get; set; }
        public Guid? IdentityTypeId { get; set; }
		public string? IdentityNo { get; set; }
		public string? FullName { get; set; }
		public string? Email { get; set; }
		public string? TinNo { get; set; }
		public TinVerifyStatusEnum TinVerifyStatus { get; set; } = TinVerifyStatusEnum.Unverified;
		public Guid DebtorTypeId { get; set; }
        public Guid? ReferrerId { get; set; }
        //public Guid AddressId { get; set; }
        public Guid CurrencyId { get; set; }
        public Guid? AccountGroupId { get; set; }
        public Guid? CompanyId { get; set; }
        public AddressCompanyRequest? Address { get; set; }

    }
}
