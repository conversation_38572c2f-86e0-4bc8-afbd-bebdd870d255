using Dolfin.Mobile.API.Models;
using Dolfin.Mobile.API.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Dolfin.Mobile.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class SettingsController : ControllerBase
    {
        private readonly ILogger<SettingsController> _logger;
        private readonly ISettingsService _settingsService;
        public SettingsController(ILogger<SettingsController> logger, ISettingsService settingsService)
        {
            _logger = logger;
            _settingsService = settingsService;
        }

        [HttpGet]
        public async Task<IActionResult> GetSettings([FromQuery] bool? isActive = null)
        {
            var data = await _settingsService.GetSettings(isActive);
            return Ok(data);
        }
    }
}
