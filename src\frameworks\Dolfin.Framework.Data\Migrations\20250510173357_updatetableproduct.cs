﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Dolfin.Framework.Data.Migrations
{
    /// <inheritdoc />
    public partial class updatetableproduct : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Transaction_Term",
                table: "Transaction");

            migrationBuilder.DropColumn(
                name: "StockMethod",
                table: "InventoryProduct");

            migrationBuilder.RenameColumn(
                name: "FractionTotalQuantity",
                table: "TransactionItem",
                newName: "FractionQuantity");

            migrationBuilder.AlterColumn<Guid>(
                name: "TermId",
                table: "Transaction",
                type: "uuid",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AddColumn<string>(
                name: "TermDay",
                table: "Transaction",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<decimal>(
                name: "FractionQty",
                table: "ProductPrice",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<Guid>(
                name: "CustomerId1",
                table: "EInvoice",
                type: "uuid",
                nullable: true);

            migrationBuilder.UpdateData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("69ed35dc-8775-4641-9f1d-26d9d91e5f4b"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 10, 17, 33, 53, 375, DateTimeKind.Utc).AddTicks(5999));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("11fd5e73-51fa-428a-a919-960ee860b00f"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 10, 17, 33, 53, 375, DateTimeKind.Utc).AddTicks(6168));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("5d22874b-5834-40a2-81be-bf5100eb845d"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 10, 17, 33, 53, 375, DateTimeKind.Utc).AddTicks(6160));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("c219bf33-28d0-4595-a25b-8018bd87e67a"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 10, 17, 33, 53, 375, DateTimeKind.Utc).AddTicks(6153));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("e57e65d0-85a9-49c8-8463-3424a42e1f01"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 10, 17, 33, 53, 375, DateTimeKind.Utc).AddTicks(6165));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 10, 17, 33, 53, 375, DateTimeKind.Utc).AddTicks(6096));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("81ba4893-d891-4e3c-a5e7-ba13c697bf97"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 10, 17, 33, 53, 375, DateTimeKind.Utc).AddTicks(6103));

            migrationBuilder.CreateIndex(
                name: "IX_EInvoice_CompanyId",
                table: "EInvoice",
                column: "CompanyId");

            migrationBuilder.CreateIndex(
                name: "IX_EInvoice_CustomerId1",
                table: "EInvoice",
                column: "CustomerId1");

            migrationBuilder.AddForeignKey(
                name: "FK_EInvoice_Company_CompanyId",
                table: "EInvoice",
                column: "CompanyId",
                principalTable: "Company",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_EInvoice_Customer_CustomerId1",
                table: "EInvoice",
                column: "CustomerId1",
                principalTable: "Customer",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Transaction_Term",
                table: "Transaction",
                column: "TermId",
                principalTable: "Term",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_EInvoice_Company_CompanyId",
                table: "EInvoice");

            migrationBuilder.DropForeignKey(
                name: "FK_EInvoice_Customer_CustomerId1",
                table: "EInvoice");

            migrationBuilder.DropForeignKey(
                name: "FK_Transaction_Term",
                table: "Transaction");

            migrationBuilder.DropIndex(
                name: "IX_EInvoice_CompanyId",
                table: "EInvoice");

            migrationBuilder.DropIndex(
                name: "IX_EInvoice_CustomerId1",
                table: "EInvoice");

            migrationBuilder.DropColumn(
                name: "TermDay",
                table: "Transaction");

            migrationBuilder.DropColumn(
                name: "FractionQty",
                table: "ProductPrice");

            migrationBuilder.DropColumn(
                name: "CustomerId1",
                table: "EInvoice");

            migrationBuilder.RenameColumn(
                name: "FractionQuantity",
                table: "TransactionItem",
                newName: "FractionTotalQuantity");

            migrationBuilder.AlterColumn<Guid>(
                name: "TermId",
                table: "Transaction",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uuid",
                oldNullable: true);

            migrationBuilder.AddColumn<int>(
                name: "StockMethod",
                table: "InventoryProduct",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.UpdateData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("69ed35dc-8775-4641-9f1d-26d9d91e5f4b"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 6, 14, 45, 1, 710, DateTimeKind.Utc).AddTicks(1615));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("11fd5e73-51fa-428a-a919-960ee860b00f"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 6, 14, 45, 1, 710, DateTimeKind.Utc).AddTicks(1724));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("5d22874b-5834-40a2-81be-bf5100eb845d"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 6, 14, 45, 1, 710, DateTimeKind.Utc).AddTicks(1717));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("c219bf33-28d0-4595-a25b-8018bd87e67a"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 6, 14, 45, 1, 710, DateTimeKind.Utc).AddTicks(1715));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("e57e65d0-85a9-49c8-8463-3424a42e1f01"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 6, 14, 45, 1, 710, DateTimeKind.Utc).AddTicks(1722));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 6, 14, 45, 1, 710, DateTimeKind.Utc).AddTicks(1672));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("81ba4893-d891-4e3c-a5e7-ba13c697bf97"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 6, 14, 45, 1, 710, DateTimeKind.Utc).AddTicks(1678));

            migrationBuilder.AddForeignKey(
                name: "FK_Transaction_Term",
                table: "Transaction",
                column: "TermId",
                principalTable: "Term",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
