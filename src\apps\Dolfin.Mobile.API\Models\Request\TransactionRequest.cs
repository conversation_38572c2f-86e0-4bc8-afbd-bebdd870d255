﻿using Dolfin.Framework.Data.Domains.CustomIdentity;
using Dolfin.Framework.Data.Domains;
using System.Text.Json.Serialization;

namespace Dolfin.Mobile.API.Models.Request
{
    public class TransactionRequest
    {
        public required string TrxNo { get; set; }
        public DateTime TrxDatetime { get; set; }
        public required string CurrencyCode { get; set; }
        public decimal ExchangeRate { get; set; }
        public decimal TotalRoundingAdjustmentAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal TotalDiscount { get; set; }
        public decimal TotalExclTax { get; set; }
        public decimal TotalInclTax { get; set; }
        public int? TotalSalesTaxAmount { get; set; }
        public int? TotalServiceTaxAmount { get; set; }
        public Guid? SalesTaxNoId { get; set; }
        public Guid? ServiceTaxNoId { get; set; }
        public decimal TotalTaxAmount { get; set; }
        public decimal TotalPayableAmount { get; set; }
        public required string PIC { get; set; }
        public string? SignatureUBL21 { get; set; }
        public int? EInvoiceSyncRetryCount { get; set; }
        public string? EInvoiceSyncPayload { get; set; }
        public string? EInvoiceSyncResponse { get; set; }
        public DateTime? EInvoiceSyncAt { get; set; }
        public string TermDay { get; set; }
        public Guid? TermId { get; set; }
        public Guid? AccountGroupId { get; set; }
        public Guid TransactionTypeId { get; set; }
        public Guid BranchId { get; set; }
        public Guid CustomerId { get; set; }
        public string UserId { get; set; }
        public Guid ShippingAddressId { get; set; }
        public Guid BillingAddressId { get; set; }
        public Guid TransactionStatusId { get; set; }
        public List<TransactionItemRequest> TransactionItemRequest { get; set; }
    }

    public class TransactionItemRequest
    {
        [JsonIgnore]
        public Guid BranchId { get; set; }
        public string ProductUOMPrimaryMCode { get; set; }
        public string ProductUOMSecondaryMCode { get; set; }
        public decimal TransactionProductPrice { get; set; }
        public decimal FractionTotal { get; set; }
        public decimal FractionQuantity { get; set; }
        public decimal Quantity { get; set; }
        public decimal Discount { get; set; }
        public decimal SalesTaxAmount { get; set; }
        public decimal ServiceTaxAmount { get; set; }
        public Guid? SalesTaxNoId { get; set; }
        public Guid? ServiceTaxNoId { get; set; }
        public decimal TaxExclAmount { get; set; }
        public decimal TaxInclAmount { get; set; }
        [JsonIgnore]
        public decimal OriUnitProductCost { get; set; }
        public decimal UnitAmount { get; set; }
        [JsonIgnore]
        public decimal TotalUnitAmountWOTax { get; set; }
        public decimal TotalUnitAmount { get; set; }
        [JsonIgnore]
        public decimal SubTotalAmount { get; set; }
        public decimal AdjAmount { get; set; }
        public Guid ProductPriceId { get; set; }
        public Guid CustomerId { get; set; }
        public Guid ProductUOMId { get; set; }
        [JsonIgnore]
        public Guid? TrxId { get; set; }
        public Guid ProductId { get; set; }

    }
}
