using Dolfin.Framework.Repository.Common;
using Dolfin.Framework.Repository.Implementations;
using Dolfin.Framework.Repository.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Dolfin.Framework.Repository.Extensions
{
    /// <summary>
    /// Extension methods for registering repository services
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// Add repository framework services to the service collection
        /// </summary>
        /// <typeparam name="TContext">DbContext type</typeparam>
        /// <param name="services">The service collection</param>
        /// <param name="configuration">The configuration</param>
        /// <returns>The service collection</returns>
        public static IServiceCollection AddRepositoryFramework<TContext>(
            this IServiceCollection services, 
            IConfiguration configuration) 
            where TContext : DbContext
        {
            // Configure cache settings
            services.Configure<CacheSettings>(configuration.GetSection("CacheSettings"));
            
            // Register cache service
            services.AddScoped<ICacheService, CacheService>();
            
            // Register unit of work
            services.AddScoped<IUnitOfWork, UnitOfWork<TContext>>();
            
            return services;
        }
    }
}
