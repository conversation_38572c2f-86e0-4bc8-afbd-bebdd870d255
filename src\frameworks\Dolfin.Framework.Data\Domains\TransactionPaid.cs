﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Framework.Data.Domains
{
    public partial class TransactionPaid : _BaseDomain
    {
        public decimal PaidAmount { get; set; }
        public DateTime PaidAt { get; set; }
        public Guid PaymentTypeId { get; set; }
        public Guid TrxId { get; set; }
        public string Remark { get; set; }
        public virtual PaymentType? PaymentType { get; set; }
        public virtual Transaction? Transaction { get; set; }
    }
}
