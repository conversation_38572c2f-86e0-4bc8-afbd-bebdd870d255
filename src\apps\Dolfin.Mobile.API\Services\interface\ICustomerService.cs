﻿using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Entity;
using Dolfin.Utility.Models;

namespace Dolfin.Mobile.API.Services
{
    public interface ICustomerService
    {
        Task<BaseResponse<PagedList<Customer>>> GetCustomerList(Pagination pagination = null, CommonFilterList filterList = null, Guid? companyId = null);
        Task<BaseResponse<Customer>> GetCustomerByGuid(Guid customerId);
        Task<BaseResponse<ResultId>> InsertCustomer(CustomerRequest reqBody, DolfinDbContext dbContextRollback = null);
        Task<BaseResponse<ResultId>> UpdateCustomer(UpdateCustomerRequest reqBody);
        Task<NoResultResponse> DeleteCustomer(Guid id);
        Task<string> GenerateUniqueReferralCodeAsync();
        Task<bool> IsPhoneNumberExistsAsync(string phoneNo, Guid companyId, DolfinDbContext dbContextRollback = null);

        // Address and Term methods
        Task<BaseResponse<PagedList<Term>>> GetTermList(Pagination pagination = null);
        Task<BaseResponse<Term>> GetTermByGuid(Guid termId);
        Task<BaseResponse<Address>> GetAddressByGuid(Guid addressId);
    }
}
