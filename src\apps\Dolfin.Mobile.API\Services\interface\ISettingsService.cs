﻿using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Entity;
using Dolfin.Mobile.API.Models.Request;
using Dolfin.Utility.Models;

namespace Dolfin.Mobile.API.Services
{
    public interface ISettingsService
    {
        Task<List<Settings>> GetSettings(bool? isActive);
        Task<BaseResponse<ResultId>> InsertAddress(AddressCompanyRequest reqBody, Guid? userId = null, DolfinDbContext dbContextRollback = null);
    }
}
