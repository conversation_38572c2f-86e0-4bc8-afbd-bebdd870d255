﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Dolfin.Framework.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddCompanyRoundAdjNProductPrice : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ProductUOM_ProductPrice",
                table: "ProductUOM");

            migrationBuilder.DropIndex(
                name: "IX_ProductUOM_ProductPriceId",
                table: "ProductUOM");

            migrationBuilder.DropColumn(
                name: "ProductPriceId",
                table: "ProductUOM");

            migrationBuilder.AddColumn<string>(
                name: "ProductUOMPrimaryMCode",
                table: "TransactionItem",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ProductUOMSecondaryMCode",
                table: "TransactionItem",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<decimal>(
                name: "TransactionProductPrice",
                table: "TransactionItem",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<Guid>(
                name: "ProductUOMId",
                table: "ProductPrice",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AddColumn<bool>(
                name: "IsRoundingAdjustment",
                table: "Company",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.UpdateData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("69ed35dc-8775-4641-9f1d-26d9d91e5f4b"),
                column: "CreatedAt",
                value: new DateTime(2025, 3, 29, 10, 44, 18, 163, DateTimeKind.Utc).AddTicks(587));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("11fd5e73-51fa-428a-a919-960ee860b00f"),
                column: "CreatedAt",
                value: new DateTime(2025, 3, 29, 10, 44, 18, 163, DateTimeKind.Utc).AddTicks(699));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("5d22874b-5834-40a2-81be-bf5100eb845d"),
                column: "CreatedAt",
                value: new DateTime(2025, 3, 29, 10, 44, 18, 163, DateTimeKind.Utc).AddTicks(693));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("c219bf33-28d0-4595-a25b-8018bd87e67a"),
                column: "CreatedAt",
                value: new DateTime(2025, 3, 29, 10, 44, 18, 163, DateTimeKind.Utc).AddTicks(689));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("e57e65d0-85a9-49c8-8463-3424a42e1f01"),
                column: "CreatedAt",
                value: new DateTime(2025, 3, 29, 10, 44, 18, 163, DateTimeKind.Utc).AddTicks(696));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"),
                column: "CreatedAt",
                value: new DateTime(2025, 3, 29, 10, 44, 18, 163, DateTimeKind.Utc).AddTicks(641));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("81ba4893-d891-4e3c-a5e7-ba13c697bf97"),
                column: "CreatedAt",
                value: new DateTime(2025, 3, 29, 10, 44, 18, 163, DateTimeKind.Utc).AddTicks(648));

            migrationBuilder.CreateIndex(
                name: "IX_ProductPrice_ProductUOMId",
                table: "ProductPrice",
                column: "ProductUOMId");

            migrationBuilder.AddForeignKey(
                name: "FK_ProductPrice_ProductUOM",
                table: "ProductPrice",
                column: "ProductUOMId",
                principalTable: "ProductUOM",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ProductPrice_ProductUOM",
                table: "ProductPrice");

            migrationBuilder.DropIndex(
                name: "IX_ProductPrice_ProductUOMId",
                table: "ProductPrice");

            migrationBuilder.DropColumn(
                name: "ProductUOMPrimaryMCode",
                table: "TransactionItem");

            migrationBuilder.DropColumn(
                name: "ProductUOMSecondaryMCode",
                table: "TransactionItem");

            migrationBuilder.DropColumn(
                name: "TransactionProductPrice",
                table: "TransactionItem");

            migrationBuilder.DropColumn(
                name: "ProductUOMId",
                table: "ProductPrice");

            migrationBuilder.DropColumn(
                name: "IsRoundingAdjustment",
                table: "Company");

            migrationBuilder.AddColumn<Guid>(
                name: "ProductPriceId",
                table: "ProductUOM",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.UpdateData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("69ed35dc-8775-4641-9f1d-26d9d91e5f4b"),
                column: "CreatedAt",
                value: new DateTime(2025, 1, 26, 8, 12, 39, 192, DateTimeKind.Utc).AddTicks(4916));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("11fd5e73-51fa-428a-a919-960ee860b00f"),
                column: "CreatedAt",
                value: new DateTime(2025, 1, 26, 8, 12, 39, 192, DateTimeKind.Utc).AddTicks(5023));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("5d22874b-5834-40a2-81be-bf5100eb845d"),
                column: "CreatedAt",
                value: new DateTime(2025, 1, 26, 8, 12, 39, 192, DateTimeKind.Utc).AddTicks(5018));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("c219bf33-28d0-4595-a25b-8018bd87e67a"),
                column: "CreatedAt",
                value: new DateTime(2025, 1, 26, 8, 12, 39, 192, DateTimeKind.Utc).AddTicks(5016));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("e57e65d0-85a9-49c8-8463-3424a42e1f01"),
                column: "CreatedAt",
                value: new DateTime(2025, 1, 26, 8, 12, 39, 192, DateTimeKind.Utc).AddTicks(5021));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"),
                column: "CreatedAt",
                value: new DateTime(2025, 1, 26, 8, 12, 39, 192, DateTimeKind.Utc).AddTicks(4966));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("81ba4893-d891-4e3c-a5e7-ba13c697bf97"),
                column: "CreatedAt",
                value: new DateTime(2025, 1, 26, 8, 12, 39, 192, DateTimeKind.Utc).AddTicks(4973));

            migrationBuilder.CreateIndex(
                name: "IX_ProductUOM_ProductPriceId",
                table: "ProductUOM",
                column: "ProductPriceId");

            migrationBuilder.AddForeignKey(
                name: "FK_ProductUOM_ProductPrice",
                table: "ProductUOM",
                column: "ProductPriceId",
                principalTable: "ProductPrice",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
