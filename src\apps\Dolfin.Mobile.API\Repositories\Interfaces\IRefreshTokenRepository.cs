using Dolfin.Framework.Data.Domains;
using System.Threading.Tasks;

namespace Dolfin.Mobile.API.Repositories.Interfaces
{
    /// <summary>
    /// Repository for managing refresh tokens
    /// </summary>
    public interface IRefreshTokenRepository
    {
        /// <summary>
        /// Get a refresh token by its token string
        /// </summary>
        /// <param name="token">The token string</param>
        /// <returns>The refresh token or null if not found</returns>
        Task<RefreshToken> GetByTokenAsync(string token);

        /// <summary>
        /// Add a new refresh token
        /// </summary>
        /// <param name="refreshToken">The refresh token to add</param>
        /// <returns>A task representing the asynchronous operation</returns>
        Task AddAsync(RefreshToken refreshToken);

        /// <summary>
        /// Update an existing refresh token
        /// </summary>
        /// <param name="refreshToken">The refresh token to update</param>
        /// <returns>A task representing the asynchronous operation</returns>
        Task UpdateAsync(RefreshToken refreshToken);

        /// <summary>
        /// Revoke a refresh token
        /// </summary>
        /// <param name="token">The token string</param>
        /// <param name="reason">Optional reason for revocation</param>
        /// <returns>True if the token was revoked, false otherwise</returns>
        Task<bool> RevokeAsync(string token, string reason = null);

        /// <summary>
        /// Revoke all refresh tokens for a user
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="reason">Optional reason for revocation</param>
        /// <returns>The number of tokens revoked</returns>
        Task<int> RevokeAllUserTokensAsync(string userId, string reason = null);
    }
}
