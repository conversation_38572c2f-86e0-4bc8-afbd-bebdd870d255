﻿using Dolfin.Framework.Data.Domains.CustomIdentity;
using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Mobile.API.Models.Dto
{
    public partial class SubscriptionDto
    {
        public SubscriptionDto()
        {
        }
        public required string Id { get; set; }
        public required string Code { get; set; }
        public required string Name { get; set; }
        public required string Description { get; set; }
        public required int Months { get; set; }
        public int? DisplayOrder { get; set; }
        public bool Published { get; set; }
    }
}
