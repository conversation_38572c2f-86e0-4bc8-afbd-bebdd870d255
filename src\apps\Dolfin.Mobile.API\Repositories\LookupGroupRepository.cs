using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Entity;
using Dolfin.Framework.Repository.Implementations;
using Dolfin.Framework.Repository.Interfaces;
using Dolfin.Mobile.API.Constants;
using Dolfin.Mobile.API.Repositories.Interfaces;
using Dolfin.Utility.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Dolfin.Mobile.API.Repositories
{
    /// <summary>
    /// Repository implementation for lookup group operations
    /// </summary>
    public class LookupGroupRepository : BaseRepository<LookupGroup>, ILookupGroupRepository
    {
        /// <summary>
        /// Constructor
        /// </summary>
        public LookupGroupRepository(
            DolfinDbContext context,
            ICacheService cacheService,
            ILogger<LookupGroupRepository> logger)
            : base(context, cacheService, logger)
        {
        }

        /// <inheritdoc />
        public async Task<LookupGroup> GetByCodeAsync(string code, Guid? companyId = null)
        {
            string cacheKey = CacheKeys.Lookup.GetLookupGroupByCodeKey(code);

            return await GetOrCreateAsync<LookupGroup>(
                cacheKey,
                async () =>
                {
                    _logger.LogInformation("Loading lookup group with code {Code} from database", code);
                    var query = _dbSet.AsQueryable();

                    if (companyId.HasValue)
                    {
                        query = query.Where(lg => lg.CompanyId == companyId || lg.CompanyId == null);
                    }

                    return await query
                        .Where(lg => lg.Code == code && lg.IsActive)
                        .FirstOrDefaultAsync();
                });
        }

        /// <inheritdoc />
        public async Task<PagedList<LookupGroup>> GetAllAsync(Pagination pagination, CommonFilterList filterList, Guid? companyId = null)
        {
            string cacheKey = companyId.HasValue
                ? CacheKeys.Lookup.GetLookupGroupListByCompanyKey(companyId.Value)
                : CacheKeys.Lookup.GetLookupGroupListKey();

            if (pagination == null)
            {
                pagination = new Pagination { PageNumber = 1, PageSize = 10 };
            }

            if (filterList == null)
            {
                filterList = new CommonFilterList();
            }

            // For paged results, we don't use caching to ensure fresh data
            var query = _dbSet.AsQueryable();

            // Apply company filter
            if (companyId.HasValue)
            {
                query = query.Where(lg => lg.CompanyId == companyId || lg.CompanyId == null);
            }

            // Apply active filter
            query = query.Where(lg => lg.IsActive);

            // Get total count
            var totalCount = await query.CountAsync();

            // Apply pagination
            var items = await query
                .Skip((pagination.PageNumber - 1) * pagination.PageSize)
                .Take(pagination.PageSize)
                .ToListAsync();

            return new PagedList<LookupGroup>(items, totalCount, pagination.PageNumber, pagination.PageSize);
        }

        /// <inheritdoc />
        public async Task<LookupGroup> GetWithItemsAsync(Guid lookupGroupId)
        {
            string cacheKey = CacheKeys.Lookup.GetLookupGroupKey(lookupGroupId);

            return await GetOrCreateAsync<LookupGroup>(
                cacheKey,
                async () =>
                {
                    _logger.LogInformation("Loading lookup group {LookupGroupId} with items from database", lookupGroupId);
                    return await _dbSet
                        .Where(lg => lg.Id == lookupGroupId && lg.IsActive)
                        .Include(lg => lg.LookupItems.Where(li => li.IsActive))
                        .FirstOrDefaultAsync();
                });
        }

        /// <inheritdoc />
        public async Task<bool> CodeExistsAsync(string code, Guid? companyId = null, Guid? excludeId = null)
        {
            var query = _dbSet.AsQueryable();

            if (companyId.HasValue)
            {
                query = query.Where(lg => lg.CompanyId == companyId || lg.CompanyId == null);
            }

            if (excludeId.HasValue)
            {
                query = query.Where(lg => lg.Id != excludeId.Value);
            }

            return await query.AnyAsync(lg => lg.Code == code && lg.IsActive);
        }
    }
}
