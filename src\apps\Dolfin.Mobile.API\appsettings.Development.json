{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "SwaggerSettings": {"Enabled": true}, "CorsSettings": {"AllowedOrigins": ["http://localhost:3000", "http://dev.dolfin.my", "https://dev.dolfin.my"]}, "ConnectionStrings": {"DolfinConnectionString": "Host=dolfin.c5i2wc846gga.ap-southeast-1.rds.amazonaws.com; Port=5432; Database=dolfin; Username=developer; Password=********************;"}, "AWSS3": {"BucketName": "dolfinsolution-dev", "AccessKey": "********************", "AccessSecret": "VEtkwZ0tPYJ0F9aBfXEb2gquCzlkf3rZh968Ob7G", "S3BaseUrl": "https://dolfinsolution-dev.s3.ap-southeast-1.amazonaws.com/", "PreSignedMinutes": 60}, "EInvoiceSettings": {"BaseUrl": "https://preprod-api.myinvois.hasil.gov.my", "AuthUrl": "/oauth/token", "InvoiceUrl": "/invoices", "QRUrl": "/qr-code", "ClientId": "fb49c899-5e60-4350-a489-6eef5cc2808b", "ClientSecret": "fcc6dd5b-2b21-4db1-8800-c1eff0c7db83"}, "JwtSettings": {"SecretKey": "tR7vF9yZ2xW4mE6bN1cX5aD0gH8jK3lP7qS9uV2xY4zB", "Issuer": "DolfinAPI", "Audience": "DolfinClients", "ExpirationMinutes": 60, "UseCookieAuthentication": false}, "CacheSettings": {"DefaultAbsoluteExpirationMinutes": 30, "DefaultSlidingExpirationMinutes": 15, "UserCacheExpirationMinutes": 30}, "HangFireScheduler": {"BackgroundJob": {"SendEmailJob": {"BatchSize": 10, "TimeSpanDelay": "03:00"}}, "RecurringJob": {"RecurringSendEmailJob": {"CronExpression": "*/30 * * * * *"}}}}