﻿﻿using Dolfin.Framework.Data.Domains.CustomIdentity;
using Dolfin.Mobile.API.Models;
using Dolfin.Mobile.API.Models.Response;
using Dolfin.Utility.Models;
using Microsoft.AspNetCore.Http;
using System.Security.Claims;

namespace Dolfin.Mobile.API.Services
{
    /// <summary>
    /// Service for JWT token generation and validation
    /// </summary>
    public interface IJwtService
    {
        /// <summary>
        /// Generate a JWT token for a user with specified roles
        /// </summary>
        /// <param name="user">The user to generate the token for</param>
        /// <param name="roles">The roles assigned to the user</param>
        /// <returns>A JWT token string</returns>
        string GenerateToken(ApplicationUser user, IList<string> roles);

        /// <summary>
        /// Generate a JWT token for a user with specified roles and token ID
        /// </summary>
        /// <param name="user">The user to generate the token for</param>
        /// <param name="roles">The roles assigned to the user</param>
        /// <param name="tokenId">A unique identifier for the token</param>
        /// <returns>A JWT token string</returns>
        string GenerateToken(ApplicationUser user, IList<string> roles, string tokenId);

        /// <summary>
        /// Generate token claims for a user with specified roles and token ID
        /// </summary>
        /// <param name="user">The user to generate the token for</param>
        /// <param name="roles">The roles assigned to the user</param>
        /// <param name="tokenId">A unique identifier for the token</param>
        /// <returns>A list of claims</returns>
        List<Claim> GenerateTokenClaims(ApplicationUser user, IList<string> roles, string tokenId);

        /// <summary>
        /// Generate token claims for a user with specified roles and token ID asynchronously
        /// </summary>
        /// <param name="user">The user to generate the token for</param>
        /// <param name="roles">The roles assigned to the user</param>
        /// <param name="tokenId">A unique identifier for the token</param>
        /// <returns>A list of claims</returns>
        Task<List<Claim>> GenerateTokenClaimsAsync(ApplicationUser user, IList<string> roles, string tokenId);

        /// <summary>
        /// Generate a JWT token for a user with specified roles and token ID asynchronously
        /// </summary>
        /// <param name="user">The user to generate the token for</param>
        /// <param name="roles">The roles assigned to the user</param>
        /// <param name="tokenId">A unique identifier for the token</param>
        /// <returns>A JWT token string</returns>
        Task<string> GenerateTokenAsync(ApplicationUser user, IList<string> roles, string tokenId);

        /// <summary>
        /// Validate a JWT token and extract the claims principal
        /// </summary>
        /// <param name="token">The JWT token to validate</param>
        /// <returns>A claims principal if the token is valid, null otherwise</returns>
        ClaimsPrincipal ValidateToken(string token);

        /// <summary>
        /// Gets the token expiration time in minutes from the JWT settings
        /// </summary>
        /// <returns>The expiration time in minutes</returns>
        int GetExpirationMinutes();

        /// <summary>
        /// Generates access and refresh tokens for a user and creates a standardized response
        /// </summary>
        /// <param name="user">The user to generate tokens for</param>
        /// <param name="roles">The user's roles</param>
        /// <param name="displayMessage">The message to display in the response</param>
        /// <returns>An AuthResultDto containing the tokens and user information</returns>
        Task<AuthResultDto> GenerateTokensAndCreateResponseAsync(ApplicationUser user, IList<string> roles, string displayMessage);

        /// <summary>
        /// Gets token cookie configuration for both access and refresh tokens
        /// </summary>
        /// <param name="isHttps">Whether the request is over HTTPS</param>
        /// <param name="accessToken">The access token</param>
        /// <param name="refreshToken">The refresh token</param>
        /// <returns>A tuple containing cookie options for access token and refresh token</returns>
        (CookieOptions AccessTokenOptions, CookieOptions RefreshTokenOptions) GetTokenCookieOptions(bool isHttps, string accessToken, string refreshToken);
    }
}
