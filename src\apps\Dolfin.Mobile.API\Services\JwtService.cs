﻿using AutoMapper;
using Dolfin.Framework.Data.Domains.CustomIdentity;
using Dolfin.Mobile.API.Models;
using Dolfin.Mobile.API.Models.Dto;
using Dolfin.Mobile.API.Models.Response;
using Dolfin.Mobile.API.Repositories.Interfaces;
using Dolfin.Utility.Enum;
using Dolfin.Utility.Models;
using Dolfin.Utility.Utils;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace Dolfin.Mobile.API.Services
{
    public class JwtService : IJwtService
    {
        private readonly JwtSettings _jwtSettings;
        private readonly ILogger<JwtService> _logger;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly StandardMessage _standardMessage;
        private readonly IMapper _mapper;
        private readonly IRefreshTokenRepository _refreshTokenRepository;
        private readonly IPermissionService _permissionService;

        public JwtService(
            IOptions<JwtSettings> jwtSettings,
            ILogger<JwtService> logger,
            UserManager<ApplicationUser> userManager,
            IMapper mapper,
            IRefreshTokenRepository refreshTokenRepository,
            IPermissionService permissionService)
        {
            _jwtSettings = jwtSettings.Value;
            _logger = logger;
            _userManager = userManager;
            _standardMessage = new StandardMessage();
            _mapper = mapper;
            _refreshTokenRepository = refreshTokenRepository;
            _permissionService = permissionService;
        }

        public string GenerateToken(ApplicationUser user, IList<string> roles)
        {
            return GenerateToken(user, roles, Guid.NewGuid().ToString());
        }

        public async Task<List<Claim>> GenerateTokenClaimsAsync(ApplicationUser user, IList<string> roles, string tokenId) {
            var claims = new List<Claim>
            {
                new Claim(JwtRegisteredClaimNames.Sub, user.Id),
                new Claim(JwtRegisteredClaimNames.Email, user.Email ?? string.Empty),
                new Claim(JwtRegisteredClaimNames.Jti, tokenId),
                new Claim(ClaimTypes.Name, user.UserName ?? string.Empty),
                new Claim(ClaimTypes.NameIdentifier, user.Id)
            };

            // Add user properties as claims if available
            if (user.PhoneNo1 != null)
                claims.Add(new Claim(ClaimTypes.MobilePhone, user.PhoneNo1));

            if (user.BranchId.HasValue)
                claims.Add(new Claim("branch_id", user.BranchId.Value.ToString()));

            if (user.CompanyId.HasValue)
                claims.Add(new Claim("company_id", user.CompanyId.Value.ToString()));

            // Add roles as claims
            foreach (var role in roles)
            {
                claims.Add(new Claim(ClaimTypes.Role, role));
            }

            // Add user permissions as claims
            try
            {
                var permissions = await _permissionService.GetUserPermissionsAsync(user.Id);
                foreach (var permission in permissions)
                {
                    claims.Add(new Claim("permission", permission));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding permission claims for user {UserId}", user.Id);
            }

            return claims;
        }

        public List<Claim> GenerateTokenClaims(ApplicationUser user, IList<string> roles, string tokenId) {
            // For backward compatibility, call the async version and wait for the result
            return GenerateTokenClaimsAsync(user, roles, tokenId).GetAwaiter().GetResult();
        }

        public async Task<string> GenerateTokenAsync(ApplicationUser user, IList<string> roles, string tokenId)
        {
            try
            {
                _logger.LogInformation("Generating JWT token for user {UserId}", user.Id);

                var claims = await GenerateTokenClaimsAsync(user, roles, tokenId);

                // Log JWT settings
                _logger.LogInformation("JWT Settings - Issuer: {Issuer}, Audience: {Audience}, ExpirationMinutes: {ExpirationMinutes}",
                    _jwtSettings.Issuer, _jwtSettings.Audience, _jwtSettings.ExpirationMinutes);

                var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtSettings.SecretKey));
                var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);
                var expires = DateTime.UtcNow.AddMinutes(_jwtSettings.ExpirationMinutes);

                var token = new JwtSecurityToken(
                    issuer: _jwtSettings.Issuer,
                    audience: _jwtSettings.Audience,
                    claims: claims,
                    notBefore: DateTime.UtcNow,
                    expires: expires,
                    signingCredentials: creds
                );

                var tokenString = new JwtSecurityTokenHandler().WriteToken(token);
                _logger.LogInformation("JWT token generated successfully");

                return tokenString;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating JWT token for user {UserId}", user.Id);
                throw;
            }
        }

        public string GenerateToken(ApplicationUser user, IList<string> roles, string tokenId)
        {
            // For backward compatibility, call the async version and wait for the result
            return GenerateTokenAsync(user, roles, tokenId).GetAwaiter().GetResult();
        }

        public ClaimsPrincipal ValidateToken(string token)
        {
            _logger.LogInformation("Validating JWT token");

            if (string.IsNullOrEmpty(token))
            {
                _logger.LogWarning("Token is null or empty");
                return null;
            }

            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.UTF8.GetBytes(_jwtSettings.SecretKey);

            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuer = true,
                ValidateAudience = true,
                ValidateLifetime = true,
                ValidateIssuerSigningKey = true,
                ValidIssuer = _jwtSettings.Issuer,
                ValidAudience = _jwtSettings.Audience,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ClockSkew = TimeSpan.FromMinutes(5)
            };

            try
            {
                SecurityToken validatedToken;
                var principal = tokenHandler.ValidateToken(token, validationParameters, out validatedToken);
                _logger.LogInformation("Token validated successfully");
                return principal;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Token validation failed");
                return null;
            }
        }

        /// <summary>
        /// Gets the token expiration time in minutes from the JWT settings
        /// </summary>
        /// <returns>The expiration time in minutes</returns>
        public int GetExpirationMinutes()
        {
            return _jwtSettings.ExpirationMinutes;
        }

        /// <summary>
        /// Generates access and refresh tokens for a user and creates a standardized response
        /// </summary>
        /// <param name="user">The user to generate tokens for</param>
        /// <param name="roles">The user's roles</param>
        /// <param name="displayMessage">The message to display in the response</param>
        /// <returns>An AuthResultDto containing the tokens and user information</returns>
        public async Task<AuthResultDto> GenerateTokensAndCreateResponseAsync(ApplicationUser user, IList<string> roles, string displayMessage)
        {
            // Generate new tokens
            var tokenId = Guid.NewGuid().ToString();
            var accessToken = await GenerateTokenAsync(user, roles, tokenId);

            // Generate new refresh token
            var refreshToken = new Dolfin.Framework.Data.Domains.RefreshToken
            {
                Token = Guid.NewGuid().ToString(),
                JwtId = tokenId,
                UserId = user.Id,
                CreationDate = DateTime.UtcNow,
                ExpiryDate = DateTime.UtcNow.AddDays(7), // Refresh token valid for 7 days
                Used = false,
                Invalidated = false
            };

            // Save the new refresh token to the database
            await _refreshTokenRepository.AddAsync(refreshToken);

            // Create user DTO for response
            var userDto = _mapper.Map<UserDto>(user);

            // Create and return the auth result
            return new AuthResultDto
            {
                TokenId = tokenId,
                Token = accessToken,
                RefreshToken = refreshToken.Token,
                ExpiresIn = _jwtSettings.ExpirationMinutes * 60, // Convert minutes to seconds
                User = userDto,
                DisplayMessage = displayMessage
            };
        }

        /// <summary>
        /// Gets token cookie configuration for both access and refresh tokens
        /// </summary>
        /// <param name="isHttps">Whether the request is over HTTPS</param>
        /// <param name="accessToken">The access token</param>
        /// <param name="refreshToken">The refresh token</param>
        /// <returns>A tuple containing cookie options for access token and refresh token</returns>
        public (CookieOptions AccessTokenOptions, CookieOptions RefreshTokenOptions) GetTokenCookieOptions(bool isHttps, string accessToken, string refreshToken)
        {
            _logger.LogInformation("Generating token cookie options");

            // Set JWT token in HTTP-only cookie
            var accessTokenOptions = new CookieOptions
            {
                HttpOnly = true,
                Secure = isHttps, // Only set secure in HTTPS
                SameSite = SameSiteMode.Strict,
                Expires = DateTimeOffset.UtcNow.AddMinutes(_jwtSettings.ExpirationMinutes)
            };

            // Set refresh token in HTTP-only cookie with longer expiration
            var refreshTokenOptions = new CookieOptions
            {
                HttpOnly = true,
                Secure = isHttps,
                SameSite = SameSiteMode.Strict,
                Expires = DateTimeOffset.UtcNow.AddDays(7) // 7 days
            };

            return (accessTokenOptions, refreshTokenOptions);
        }

    }
}
