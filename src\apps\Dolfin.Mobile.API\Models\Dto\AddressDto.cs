﻿using System;
using System.Collections.Generic;
using System.Diagnostics.Metrics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Mobile.API.Models.Dto
{
    public partial class AddressDto
    {
        public AddressDto()
        {
        }
        public required Guid Id { get; set; }
        public required string FirstName { get; set; }
        public required string LastName { get; set; }
        public required string Email { get; set; }
        public string? Company { get; set; }
        public required string Address1 { get; set; }
        public string? Address2 { get; set; }
        public string? Address3 { get; set; }
        public required string PostalCode { get; set; }
        public required string PhoneNo { get; set; }
        public string? FaxNo { get; set; }
        public string? Coordinate { get; set; }
        public Guid CountryId { get; set; }
        public Guid StateProvinceId { get; set; }
        public Guid RegionId { get; set; }
        //public virtual CountryDto? Country { get; set; }
        public virtual StateDto? State { get; set; }
        public virtual RegionDto? Region { get; set; }

    }
}
