using Dolfin.Framework.Repository.Common;
using Dolfin.Framework.Repository.Interfaces;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Threading.Tasks;

namespace Dolfin.Framework.Repository.Implementations
{
    /// <summary>
    /// Implementation of the cache service using IMemoryCache
    /// </summary>
    public class CacheService : ICacheService
    {
        private readonly IMemoryCache _memoryCache;
        private readonly CacheSettings _cacheSettings;
        private readonly ILogger<CacheService> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        public CacheService(
            IMemoryCache memoryCache, 
            IOptions<CacheSettings> cacheSettings, 
            ILogger<CacheService> logger)
        {
            _memoryCache = memoryCache;
            _cacheSettings = cacheSettings.Value;
            _logger = logger;
        }

        /// <inheritdoc />
        public T GetOrCreate<T>(string key, Func<T> factory, int? absoluteExpirationMinutes = null, int? slidingExpirationMinutes = null)
        {
            if (_memoryCache.TryGetValue(key, out T cachedItem))
            {
                _logger.LogDebug("Cache hit for key: {Key}", key);
                return cachedItem;
            }

            _logger.LogDebug("Cache miss for key: {Key}", key);
            T item = factory();

            var cacheEntryOptions = CreateCacheEntryOptions(absoluteExpirationMinutes, slidingExpirationMinutes);
            _memoryCache.Set(key, item, cacheEntryOptions);

            return item;
        }

        /// <inheritdoc />
        public async Task<T> GetOrCreateAsync<T>(string key, Func<Task<T>> factory, int? absoluteExpirationMinutes = null, int? slidingExpirationMinutes = null)
        {
            if (_memoryCache.TryGetValue(key, out T cachedItem))
            {
                _logger.LogDebug("Cache hit for key: {Key}", key);
                return cachedItem;
            }

            _logger.LogDebug("Cache miss for key: {Key}", key);
            T item = await factory();

            var cacheEntryOptions = CreateCacheEntryOptions(absoluteExpirationMinutes, slidingExpirationMinutes);
            _memoryCache.Set(key, item, cacheEntryOptions);

            return item;
        }

        /// <inheritdoc />
        public void Remove(string key)
        {
            _logger.LogDebug("Removing item from cache with key: {Key}", key);
            _memoryCache.Remove(key);
        }

        /// <inheritdoc />
        public bool Exists(string key)
        {
            return _memoryCache.TryGetValue(key, out _);
        }

        private MemoryCacheEntryOptions CreateCacheEntryOptions(int? absoluteExpirationMinutes, int? slidingExpirationMinutes)
        {
            var cacheEntryOptions = new MemoryCacheEntryOptions();

            // Use provided values or fall back to default settings
            if (absoluteExpirationMinutes.HasValue || _cacheSettings.DefaultAbsoluteExpirationMinutes > 0)
            {
                cacheEntryOptions.AbsoluteExpirationRelativeToNow = 
                    TimeSpan.FromMinutes(absoluteExpirationMinutes ?? _cacheSettings.DefaultAbsoluteExpirationMinutes);
            }

            if (slidingExpirationMinutes.HasValue || _cacheSettings.DefaultSlidingExpirationMinutes > 0)
            {
                cacheEntryOptions.SlidingExpiration = 
                    TimeSpan.FromMinutes(slidingExpirationMinutes ?? _cacheSettings.DefaultSlidingExpirationMinutes);
            }

            return cacheEntryOptions;
        }
    }
}
