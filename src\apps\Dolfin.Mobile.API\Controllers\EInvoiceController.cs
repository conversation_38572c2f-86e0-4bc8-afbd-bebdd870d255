﻿
using AutoMapper;
using Dolfin.Framework.Data.Domains;
using Dolfin.Mobile.API.Constants;
using Dolfin.Mobile.API.Infrastructure;
using Dolfin.Mobile.API.Models;
using Dolfin.Mobile.API.Models.Dto;
using Dolfin.Mobile.API.Models.Request;
using Dolfin.Mobile.API.Services;
using Dolfin.Utility.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Reflection;

namespace Dolfin.Mobile.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class EInvoiceController : ControllerCore
    {
        private readonly ILogger<EInvoiceController> _logger;
        private readonly ICompanyService _companyService;
        private readonly IUserService _userService;
        private readonly IEinvoiceService _einvoiceService;
        private IMapper _mapper;
        public EInvoiceController(ILogger<EInvoiceController> logger, ICompanyService companyService, IUserService userService, IEinvoiceService einvoiceService, IMapper mapper)
        {
            _logger = logger;
            _mapper = mapper;
            _companyService = companyService;
            _userService = userService;
            _einvoiceService = einvoiceService;
        }

        [HttpGet("LoginTaxpayerSystem")]
        [RequirePermission(Permissions.EInvoice.View)]
        public async Task<IActionResult> LoginTaxpayerSystem([FromQuery] EInvoiceAuthRequest eInvoiceAuthRequest)
        {
            var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
            var currentUser = getCurrentUser.Item1;
            var companyId = getCurrentUser.Item2;

            //var companyProfile = await _companyService.GetCompanyProfile(companyId);
            var response = await _einvoiceService.LoginTaxpayerSystem(eInvoiceAuthRequest);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<EInvoiceAuthResponse, EInvoiceAuthResponse>(_mapper, response);
        }

        [HttpGet("SearchTin")]
        [RequirePermission(Permissions.EInvoice.View)]
        public async Task<IActionResult> SearchTin([FromQuery] EInvoiceSearchTinRequest eInvoiceSearchTinRequest)
        {
            var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
            var currentUser = getCurrentUser.Item1;
            var companyId = getCurrentUser.Item2;

            //var companyProfile = await _companyService.GetCompanyProfile(companyId);
            var response = await _einvoiceService.SearchTin(eInvoiceSearchTinRequest);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<EInvoiceSearchTinResponse, EInvoiceSearchTinResponse>(_mapper, response);
        }

        [HttpGet("ValidateTin")]
        [RequirePermission(Permissions.EInvoice.View)]
        public async Task<IActionResult> ValidateTin([FromQuery] EInvoiceValidateTinRequest eInvoiceValidateTinRequest)
        {
            var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
            var currentUser = getCurrentUser.Item1;
            var companyId = getCurrentUser.Item2;

            //var companyProfile = await _companyService.GetCompanyProfile(companyId);
            var response = await _einvoiceService.ValidateTin(eInvoiceValidateTinRequest);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<EInvoiceValidateTinResponse, EInvoiceValidateTinResponse>(_mapper, response);
        }

        [HttpGet("GetDocumentTypes")]
        [RequirePermission(Permissions.EInvoice.View)]
        public async Task<IActionResult> GetDocumentTypes()
        {
            var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
            var currentUser = getCurrentUser.Item1;
            var companyId = getCurrentUser.Item2;

            var response = await _einvoiceService.GetAllDocumentTypes();
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<EInvoiceDocumentTypesResponse, EInvoiceDocumentTypesResponse>(_mapper, response);
        }

        [HttpPost("SubmitDocument")]
        [RequirePermission(Permissions.EInvoice.Create)]
        public async Task<IActionResult> SubmitDocument([FromBody] EInvoiceSubmitDocumentRequest request)
        {
            var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
            var currentUser = getCurrentUser.Item1;
            var companyId = getCurrentUser.Item2;

            var response = await _einvoiceService.SubmitDocument(request);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<EInvoiceSubmitDocumentResponse, EInvoiceSubmitDocumentResponse>(_mapper, response);
        }

        [HttpPut("CancelDocument/{documentUUID}")]
        [RequirePermission(Permissions.EInvoice.Cancel)]
        public async Task<IActionResult> CancelDocument(string documentUUID, [FromBody] EInvoiceCancelRequest request)
        {
            // Set the DocumentUUID from the route parameter
            request.DocumentUUID = documentUUID;

            var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
            var currentUser = getCurrentUser.Item1;
            var companyId = getCurrentUser.Item2;

            var response = await _einvoiceService.CancelDocument(request);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<EInvoiceCancelRejectResponse, EInvoiceCancelRejectResponse>(_mapper, response);
        }

        [HttpPut("RejectDocument/{documentUUID}")]
        [RequirePermission(Permissions.EInvoice.Reject)]
        public async Task<IActionResult> RejectDocument(string documentUUID, [FromBody] EInvoiceRejectRequest request)
        {
            // Set the DocumentUUID from the route parameter
            request.DocumentUUID = documentUUID;

            var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
            var currentUser = getCurrentUser.Item1;
            var companyId = getCurrentUser.Item2;

            var response = await _einvoiceService.RejectDocument(request);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<EInvoiceCancelRejectResponse, EInvoiceCancelRejectResponse>(_mapper, response);
        }
    }
}
