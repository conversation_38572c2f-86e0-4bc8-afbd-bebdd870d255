namespace Dolfin.Framework.Repository.Common
{
    /// <summary>
    /// Settings for cache configuration
    /// </summary>
    public class CacheSettings
    {
        /// <summary>
        /// Default absolute expiration time in minutes
        /// </summary>
        public int DefaultAbsoluteExpirationMinutes { get; set; } = 60;

        /// <summary>
        /// Default sliding expiration time in minutes
        /// </summary>
        public int DefaultSlidingExpirationMinutes { get; set; } = 30;

        /// <summary>
        /// User cache expiration time in minutes
        /// </summary>
        public int UserCacheExpirationMinutes { get; set; } = 60;
    }
}
