﻿﻿using AutoMapper;
using Dolfin.Framework.Data.Domains;
using Dolfin.Mobile.API.Infrastructure;
using Dolfin.Mobile.API.Constants;
using Dolfin.Mobile.API.Models.Request;
using Dolfin.Mobile.API.Models.Response;
using Dolfin.Mobile.API.Services;
using Dolfin.Utility.Enum;
using Dolfin.Utility.Models;
using Dolfin.Utility.Utils;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Reflection;
using static Dolfin.Mobile.API.Constants.Constants;
using static Dolfin.Utility.Enum.Enums;
using ValidationException = Dolfin.Utility.Utils.ExceptionHandler.ValidationException;
using Org.BouncyCastle.Asn1.Ocsp;

namespace Dolfin.Mobile.API.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class FileUploadController : ControllerCore
    {
        private readonly StandardMessage _standardMessage;
        private readonly IFileUploadService _fileUploadService;
        private readonly IUserService _userService;
        private readonly ILogger<FileUploadController> _logger;
        private readonly IMapper _mapper;

        public FileUploadController(
            IFileUploadService fileUploadService,
            IUserService userService,
            ILogger<FileUploadController> logger,
            IMapper mapper)
        {
            _standardMessage = new StandardMessage();
            _fileUploadService = fileUploadService;
            _userService = userService;
            _logger = logger;
            _mapper = mapper;
        }

        /// <summary>
        /// Upload a file to Amazon S3
        /// </summary>
        /// <returns>File upload response with details</returns>
        [HttpPost("Upload")]
        public async Task<IActionResult> UploadFile([FromForm] FileUploadRequest request)
        {
            var response = new BaseResponse<ResultId> { IsSuccessful = true };

            try
            {
                // Validate request
                if (request.File == null || request.File.Length == 0)
                {
                    throw new ValidationException("No file was uploaded.");
                }

                if (string.IsNullOrEmpty(request.Module))
                {
                    throw new ValidationException("Module is required.");
                }

                if (!IsConstantNameValid(request.Module))
                {
                    throw new ValidationException($"Invalid module name: {request.Module}. Valid modules are: PRODUCT, PROFILE");
                }

                // Get current user and company ID
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
                var currentUser = getCurrentUser.Item1;
                var companyId = getCurrentUser.Item2;

                if (companyId == null)
                {
                    throw new ValidationException("Company ID is required.");
                }

                // Upload the file
                var uploadResponse = await _fileUploadService.UploadFile(request, companyId.Value);

                if (!uploadResponse.IsSuccessful)
                {
                    throw new Exception(uploadResponse.Exception);
                }

                if (uploadResponse.Result == null)
                {
                    throw new ValidationException("File upload failed.");
                }

                // Set the result ID from the file upload
                response.Result = new ResultId { Id = uploadResponse.Result.Id };
            }
            catch (ValidationException ex)
            {
                // Handle validation errors with BadRequest status
                response = _standardMessage.ErrorMessage<ResultId, ResultId>(
                    response,
                    Enums.StatusCode.BadRequest,
                    exception: ex
                );
                _logger.LogError(ex, $"Validation error in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            catch (Exception ex)
            {
                // Handle other errors with InternalServerError status
                response = _standardMessage.ErrorMessage<ResultId, ResultId>(
                    response,
                    Enums.StatusCode.InternalServerError,
                    exception: ex
                );
                _logger.LogError(ex, $"Error occurred in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }

            return ActionResultResponse<ResultId, ResultId>(_mapper, response);
        }

        /// <summary>
        /// Get a pre-signed URL for a file
        /// </summary>
        /// <param name="fileUrl">The file URL in S3</param>
        /// <returns>Pre-signed URL for temporary access</returns>
        [HttpGet("GetPreSignedUrl")]
        public async Task<IActionResult> GetPreSignedUrl([FromQuery] string fileUrl)
        {
            var response = new BaseResponse<string> { IsSuccessful = true };

            try
            {
                if (string.IsNullOrEmpty(fileUrl))
                {
                    throw new ValidationException("File URL is required.");
                }

                var urlResponse = await _fileUploadService.GetPreSignedUrl(fileUrl);

                if (!urlResponse.IsSuccessful)
                {
                    throw new Exception(urlResponse.Exception);
                }

                response.Result = urlResponse.Result;
            }
            catch (ValidationException ex)
            {
                // Handle validation errors with BadRequest status
                response = _standardMessage.ErrorMessage<string, string>(
                    response,
                    Enums.StatusCode.BadRequest,
                    exception: ex
                );
                _logger.LogError(ex, $"Validation error in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            catch (Exception ex)
            {
                // Handle other errors with InternalServerError status
                response = _standardMessage.ErrorMessage<string, string>(
                    response,
                    Enums.StatusCode.InternalServerError,
                    exception: ex
                );
                _logger.LogError(ex, $"Error occurred in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }

            return ActionResultResponse<string, string>(_mapper, response);
        }

        /// <summary>
        /// Delete a file from Amazon S3
        /// </summary>
        /// <param name="fileUrl">The file URL in S3</param>
        /// <returns>Success or failure response</returns>
        [HttpDelete("Delete")]
        [RequirePermission(Permissions.FileUpload.Delete)]
        public async Task<IActionResult> DeleteFile([FromQuery] string fileUrl)
        {
            var response = new NoResultResponse { IsSuccessful = true };

            try
            {
                if (string.IsNullOrEmpty(fileUrl))
                {
                    throw new ValidationException("File URL is required.");
                }

                // Delete the file
                var deleteResponse = await _fileUploadService.DeleteFile(fileUrl);

                if (!deleteResponse.IsSuccessful)
                {
                    throw new Exception(deleteResponse.Exception);
                }
            }
            catch (ValidationException ex)
            {
                // Handle validation errors with BadRequest status
                response = _standardMessage.ErrorMessage<NoResult, NoResult>(
                    response,
                    Enums.StatusCode.BadRequest,
                    exception: ex
                );
                _logger.LogError(ex, $"Validation error in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            catch (Exception ex)
            {
                // Handle other errors with InternalServerError status
                response = _standardMessage.ErrorMessage<NoResult, NoResult>(
                    response,
                    Enums.StatusCode.InternalServerError,
                    exception: ex
                );
                _logger.LogError(ex, $"Error occurred in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }

            return ActionResultResponse<NoResult, NoResult>(_mapper, response);
        }

        /// <summary>
        /// Get files by reference ID
        /// </summary>
        /// <param name="referenceId">The reference ID (e.g., product ID, customer ID)</param>
        /// <param name="module">Module filter (e.g., PRODUCT, PROFILE)</param>
        /// <returns>List of file metadata</returns>
        [HttpGet("GetByReference/{module}/{referenceId}")]
        public async Task<IActionResult> GetFilesByReferenceId(Guid referenceId, string module)
        {
            var response = new BaseResponse<List<FileUploadResponse>> { IsSuccessful = true };

            try
            {
                if (referenceId == Guid.Empty)
                {
                    throw new ValidationException("Reference ID is required.");
                }

                if (!IsConstantNameValid(module))
                {
                    throw new ValidationException($"Invalid module name: {module}. Valid modules are: PRODUCT, PROFILE.");
                }

                var filesResponse = await _fileUploadService.GetFilesByReferenceId(referenceId, module);

                if (!filesResponse.IsSuccessful)
                {
                    throw new Exception(filesResponse.Exception);
                }

                response.Result = filesResponse.Result;
            }
            catch (ValidationException ex)
            {
                // Handle validation errors with BadRequest status
                response = _standardMessage.ErrorMessage<List<FileUploadResponse>, List<FileUploadResponse>>(
                    response,
                    Enums.StatusCode.BadRequest,
                    exception: ex
                );
                _logger.LogError(ex, $"Validation error in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            catch (Exception ex)
            {
                // Handle other errors with InternalServerError status
                response = _standardMessage.ErrorMessage<List<FileUploadResponse>, List<FileUploadResponse>>(
                    response,
                    Enums.StatusCode.InternalServerError,
                    exception: ex
                );
                _logger.LogError(ex, $"Error occurred in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }

            return ActionResultResponse<List<FileUploadResponse>, List<FileUploadResponse>>(_mapper, response);
        }
    }
}
