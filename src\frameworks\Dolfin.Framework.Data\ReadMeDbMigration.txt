﻿// Code First
1. IMPORTANT: Change your connection string to the correct environment
2. Right click Solution 'Dolfin' and click 'Open in Terminal'
3. Run the below command to generate db migration, replace {name of migration} to your remarks
	dotnet ef migrations add {name of migration} --verbose --project src\frameworks\Dolfin.Framework.Data   --startup-project src\apps\Dolfin.Mobile.API
4. Run the below command to update the migration into database
	dotnet ef database update --verbose --project src\frameworks\Dolfin.Framework.Data   --startup-project src\apps\Dolfin.Mobile.API


// Install entity framework tools
dotnet tool install --global dotnet-ef

// Update entity framework tools
dotnet tool update --global dotnet-ef



// Create developer user for new database
// IMPORTANT: Production user should not have create table permissions
-- Step 1: Create user (skip if already exists)
CREATE USER developer WITH PASSWORD 'YourStrongPasswordHere';

-- Step 2: Grant connection to the database
GRANT CONNECT ON DATABASE dolfin TO developer;

-- Step 3: Grant USAGE and CREATE privileges on the schema
GRANT USAGE ON SCHEMA public TO developer;
GRANT CREATE ON SCHEMA public TO developer;

-- Step 4: Grant privileges on all existing tables and sequences
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO developer;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO developer;

-- Step 5: Automatically grant permissions on new tables/sequences in future
ALTER DEFAULT PRIVILEGES IN SCHEMA public
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO developer;

ALTER DEFAULT PRIVILEGES IN SCHEMA public
GRANT USAGE, SELECT ON SEQUENCES TO developer;

GRANT CREATE ON DATABASE dolfin TO developer;