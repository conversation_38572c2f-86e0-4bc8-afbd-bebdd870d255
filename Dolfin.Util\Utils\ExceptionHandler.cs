﻿﻿using System;

namespace Dolfin.Utility.Utils
{
    /// <summary>
    /// Provides exception handling utilities and custom exception types for the application
    /// </summary>
    public static class ExceptionHandler
    {
        /// <summary>
        /// Custom exception for validation errors that should result in a BadRequest response
        /// </summary>
        public class ValidationException : Exception
        {
            /// <summary>
            /// Creates a new ValidationException with the specified error message
            /// </summary>
            /// <param name="message">The error message that explains the reason for the exception</param>
            public ValidationException(string message) : base(message) { }

            /// <summary>
            /// Creates a new ValidationException with the specified error message and inner exception
            /// </summary>
            /// <param name="message">The error message that explains the reason for the exception</param>
            /// <param name="innerException">The exception that is the cause of the current exception</param>
            public ValidationException(string message, Exception innerException) : base(message, innerException) { }
        }
    }
}
