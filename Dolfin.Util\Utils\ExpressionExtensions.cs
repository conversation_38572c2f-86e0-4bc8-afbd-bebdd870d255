﻿using Dolfin.Utility.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;

namespace Dolfin.Utility.Utils
{
    public static class ExpressionExtensions
    {
        public static Expression<Func<T, bool>> AndAlso<T>(
        this Expression<Func<T, bool>> expr1,
        Expression<Func<T, bool>> expr2)
        {
            var parameter = Expression.Parameter(typeof(T));

            var leftVisitor = new ReplaceExpressionVisitor(expr1.Parameters[0], parameter);
            var left = leftVisitor.Visit(expr1.Body);

            var rightVisitor = new ReplaceExpressionVisitor(expr2.Parameters[0], parameter);
            var right = rightVisitor.Visit(expr2.Body);

            return Expression.Lambda<Func<T, bool>>(
                Expression.AndAlso(left, right), parameter);
        }

        public static Expression<Func<T, bool>> True<T>() => x => true;

        public static Expression<Func<T, bool>> BuildPredicate<T>(Expression<Func<T, bool>> predicate, CommonFilterList filterList)
        {
            // Loop through each filter in the filter list
            foreach (var filter in filterList.FilterList)
            {
                // Get the property info for the filter name
                var property = typeof(T).GetProperty(filter.FilterName, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
                if (property == null) continue;

                // Convert FilterValue to the correct property type
                var convertedValue = Convert.ChangeType(filter.FilterValue, property.PropertyType);

                // Build the expression: x => x.Property == FilterValue
                var parameter = Expression.Parameter(typeof(T), "x");
                var propertyAccess = Expression.Property(parameter, property);
                var constant = Expression.Constant(convertedValue);
                var equal = Expression.Equal(propertyAccess, constant);

                var lambda = Expression.Lambda<Func<T, bool>>(equal, parameter);

                // Combine with the existing predicate using AndAlso
                predicate = predicate.AndAlso(lambda);
            }

            return predicate;
        }

        private class ReplaceExpressionVisitor
            : ExpressionVisitor
        {
            private readonly Expression _oldValue;
            private readonly Expression _newValue;

            public ReplaceExpressionVisitor(Expression oldValue, Expression newValue)
            {
                _oldValue = oldValue;
                _newValue = newValue;
            }

            public override Expression Visit(Expression node)
            {
                if (node == _oldValue)
                    return _newValue;
                return base.Visit(node);
            }
        }
    }
}
