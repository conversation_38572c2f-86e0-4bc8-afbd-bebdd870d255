﻿using System;
using System.Collections.Generic;

namespace  Dolfin.Framework.Data.Domains
{
    public partial class Country : _BaseDomain
    {
        public Country()
        {
            Address = new HashSet<Address>();
            State = new HashSet<State>();
        }

        public required string Name { get; set; }
        public required string TwoLetterIsoCode { get; set; }
        public required string ThreeLetterIsoCode { get; set; }
        public int NumericIsoCode { get; set; }
        public bool Published { get; set; }
        public int? DisplayOrder { get; set; }

        public virtual ICollection<Address> Address { get; set; }
        public virtual ICollection<State> State { get; set; }
    }
}
