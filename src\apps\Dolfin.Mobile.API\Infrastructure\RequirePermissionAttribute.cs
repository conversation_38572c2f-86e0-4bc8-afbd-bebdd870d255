﻿﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Dolfin.Mobile.API.Services;
using Dolfin.Utility.Enum;
using Dolfin.Utility.Models;
using Microsoft.AspNetCore.Mvc.Authorization;

namespace Dolfin.Mobile.API.Infrastructure
{
    /// <summary>
    /// Authorization attribute that validates if the user has the required permission
    /// </summary>
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = true)]
    public class RequirePermissionAttribute : AuthorizeAttribute, IAsyncAuthorizationFilter
    {
        private readonly string[] _permissions;

        /// <summary>
        /// Constructor that takes one or more permission strings
        /// </summary>
        /// <param name="permissions">The permissions required to access the resource</param>
        public RequirePermissionAttribute(params string[] permissions)
        {
            _permissions = permissions ?? Array.Empty<string>();
        }

        /// <summary>
        /// Asynchronously authorizes the request based on the user's permissions
        /// </summary>
        /// <param name="context">The authorization filter context</param>
        public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            // Skip authorization if no permissions are required or if action has AllowAnonymous attribute
            if (_permissions.Length == 0 || context.Filters.Any(f => f is IAllowAnonymousFilter))
            {
                return;
            }

            // Get the permission service from the request services
            var permissionService = context.HttpContext.RequestServices.GetRequiredService<IPermissionService>();

            // Get the current user's claims principal
            var user = context.HttpContext.User;

            if (user.Identity == null || !user.Identity.IsAuthenticated)
            {
                context.Result = new UnauthorizedResult();
                return;
            }

            // Check if the user has any of the required permissions
            var hasPermission = await permissionService.UserHasAnyPermissionAsync(user, _permissions);

            if (!hasPermission)
            {
                var response = new NoResultResponse
                {
                    IsSuccessful = false,
                    StatusCode = (int)Enums.StatusCode.Forbidden,
                    StatusMessage = "You do not have permission to access this resource",
                    Result = new NoResult()
                };

                context.Result = new ObjectResult(response)
                {
                    StatusCode = (int)Enums.StatusCode.Forbidden
                };
            }
        }
    }
}
