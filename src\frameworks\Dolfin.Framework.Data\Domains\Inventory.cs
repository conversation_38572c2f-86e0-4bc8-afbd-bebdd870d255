﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Dolfin.Utility.Enum.Enums;

namespace Dolfin.Framework.Data.Domains
{
    public partial class Inventory : _BaseDomain
    {
        public Inventory()
        {
            InventoryProduct = new HashSet<InventoryProduct>();
        }
        public required string Code { get; set; }
        public required string Name { get; set; }
        public Guid BranchId { get; set; }
        public virtual Branch? Branch { get; set; }
        public virtual ICollection<InventoryProduct> InventoryProduct { get; }
    }
}
