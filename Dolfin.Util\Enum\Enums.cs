using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Utility.Enum
{
    public static class Enums
    {
        public static T GetValueFromDescription<T>(string description, bool normalize = false)
        {
            var type = typeof(T);
            if (!type.IsEnum) throw new InvalidOperationException();
            foreach (var field in type.GetFields())
            {
                var attribute = Attribute.GetCustomAttribute(field,
                    typeof(DescriptionAttribute)) as DescriptionAttribute;
                if (attribute != null)
                {
                    var descriptionAttribute = attribute.Description.ToString();
                    if (normalize)
                    {
                        descriptionAttribute = String.Concat(attribute.Description.Where(c => !Char.IsWhiteSpace(c)));
                    }
                    if (descriptionAttribute.ToUpper() == description.ToUpper())
                        return (T)field.GetValue(null);
                }
                //else
                //{
                //    if (field.Name.ToUpper() == description.ToUpper())
                //        return (T)field.GetValue(null);
                //}
            }
            throw new ArgumentException("Not found.", nameof(description));
            // or return default(T);
        }

        public static T GetValueFromAmbient<T>(Guid ambient)
        {
            var type = typeof(T);
            if (!type.IsEnum) throw new InvalidOperationException();
            foreach (var field in type.GetFields())
            {
                var attribute = Attribute.GetCustomAttribute(field,
                    typeof(AmbientValueAttribute)) as AmbientValueAttribute;
                if (attribute != null)
                {
                    var ambientAttribute = attribute.Value;
                    if (ambientAttribute == null)
                    {
                        throw new ArgumentException("Not found.", nameof(ambient));
                    }
                    if ((Guid)ambientAttribute == ambient)
                        return (T)field.GetValue(null);
                }
                //else
                //{
                //    if (field.Name.ToUpper() == description.ToUpper())
                //        return (T)field.GetValue(null);
                //}
            }
            throw new ArgumentException("Not found.", nameof(ambient));
            // or return default(T);
        }

        public static string GetDescription<T>(this T e) where T : IConvertible
        {
            Type type = e.GetType();
            Array values = System.Enum.GetValues(type);

            foreach (int val in values)
            {
                if (val == e.ToInt32(CultureInfo.InvariantCulture))
                {
                    var memInfo = type.GetMember(type.GetEnumName(val));
                    var descriptionAttribute = memInfo[0]
                        .GetCustomAttributes(typeof(DescriptionAttribute), false)
                        .FirstOrDefault() as DescriptionAttribute;

                    if (descriptionAttribute != null)
                    {
                        return descriptionAttribute.Description;
                    }
                }
            }
            return null; // could also return string.Empty
        }

        // sample: (Guid)UserTypeEnum.SysAdmin.GetAmbientValue();
        public static Guid? GetAmbientValue<T>(this T e) where T : IConvertible
        {
            Type type = e.GetType();
            Array values = System.Enum.GetValues(type);

            foreach (int val in values)
            {
                if (val == e.ToInt32(CultureInfo.InvariantCulture))
                {
                    var memInfo = type.GetMember(type.GetEnumName(val));
                    var ambientValueAttribute = memInfo[0]
                        .GetCustomAttributes(typeof(AmbientValueAttribute), false)
                        .FirstOrDefault() as AmbientValueAttribute;

                    if (ambientValueAttribute != null && ambientValueAttribute.Value != null)
                    {
                        return (Guid)ambientValueAttribute.Value;
                    }
                }
            }
            return null;
        }

        //public static object GetAmbientValue(this Enum enumVal)
        //{
        //    Type type = enumVal.GetType();
        //    MemberInfo[] memInfo = type.GetMember(enumVal.ToString());
        //    object[] attributes = memInfo[0].GetCustomAttributes(typeof(AmbientValueAttribute), false);

        //    if (attributes == null || attributes.Length == 0)
        //        return default;

        //    return ((AmbientValueAttribute)attributes[0]).Value;
        //}

        public enum UserTypeEnum
        {
            [Description("Admin")]
            [AmbientValue(typeof(Guid), "aef630d3-6f0b-4c1f-a17b-c60f8f0408ea")]
            Admin = 1,

            [Description("Super Admin")]
            [AmbientValue(typeof(Guid), "b3ec5d2b-ca0d-45bc-ba86-bc965852922c")]
            SuperAdmin = 2,

            [Description("Super User")]
            [AmbientValue(typeof(Guid), "c0a87fee-d2a5-4c7e-beee-e60c7936f056")]
            SuperUser = 3,

            [Description("User")]
            [AmbientValue(typeof(Guid), "dbd5a6a5-0995-4ab6-822f-559ec36d222e")]
            User = 4
        }

        public enum SubcriptionPlan
        {
            [Description("Plan 1")]
            Subsctiption1 = 100001,

            [Description("Plan 2")]
            Subsctiption2 = 100002,

            [Description("Plan 3")]
            Subsctiption3 = 100003,
        }

        public enum FILE_TYPE
        {
            [Description("files")]
            Files,

            [Description("images")]
            Images,
        }

        public enum PasswordFormatEnum
        {
            Clear = 0,
            Hashed = 1,
            Encrypted = 2
        }

        public static StatusCode GetStatusCode(HttpRequestException httpEx)
        {
            StatusCode statusCode = StatusCode.InternalServerError; // Default

            if (httpEx.StatusCode.HasValue)
            {
                // Try to match the HTTP status code to your StatusCode enum
                switch ((int)httpEx.StatusCode.Value)
                {
                    case 400:
                        statusCode = StatusCode.BadRequest;
                        break;
                    case 401:
                        statusCode = StatusCode.Unauthorized;
                        break;
                    case 403:
                        statusCode = StatusCode.Forbidden;
                        break;
                    case 404:
                        statusCode = StatusCode.NotFound;
                        break;
                    case 409:
                        statusCode = StatusCode.Duplicate;
                        break;
                    default:
                        statusCode = StatusCode.InternalServerError;
                        break;
                }
            }
            return statusCode;
        }

        public static StatusCode GetStatusCode(int? statusValue)
        {
            StatusCode statusCode = StatusCode.InternalServerError; // Default

            // Try to match the HTTP status code to your StatusCode enum
            if (statusValue.HasValue)
                switch (statusValue)
                {
                    case 400:
                        statusCode = StatusCode.BadRequest;
                        break;
                    case 401:
                        statusCode = StatusCode.Unauthorized;
                        break;
                    case 403:
                        statusCode = StatusCode.Forbidden;
                        break;
                    case 404:
                        statusCode = StatusCode.NotFound;
                        break;
                    case 409:
                        statusCode = StatusCode.Duplicate;
                        break;
                    default:
                        statusCode = StatusCode.InternalServerError;
                        break;
                }
            return statusCode;
        }

        public enum StatusCode
        {
            [Description("Success")]
            Success = 200,

            [Description("Bad Request")]
            BadRequest = 400,

            [Description("Unauthorized")]
            Unauthorized = 401,

            [Description("Forbidden")]
            Forbidden = 403,

            [Description("{0} Not Found")]
            NotFound = 404,

            [Description("Duplicate {0} Found")]
            Duplicate = 409,

            [Description("Internal Server Error")]
            InternalServerError = 500
        }

		public enum TinVerifyStatusEnum
		{
			[Description("Unverified")]
			Unverified = 0,  // Default, not yet verified

			[Description("Verified")]
			Verified = 1,  // Successfully verified

			[Description("Invalid Argument")]
			BadArgument = 2, // Invalid Argument (HTTP 400)

			[Description("Not Found")]
			NotFound = 3 // TIN not found or invalid (HTTP 404)
		}

		public enum EInvoiceStatusEnum
		{
			//[Description("Pending")]
			Pending = 0,      // Awaiting submission to government API

			[Description("Submitted")]
			Submitted = 1,    // Sent to the government API

			[Description("Validated")]
			Validated = 2,    // Passed validation checks

			[Description("Invalid")]
			Invalid = 3,    // Validation checks failed

			[Description("Cancelled")]
			Cancelled = 4,     // Cancelled by the API

			[Description("Accepted")]
			Accepted = 5,      // Successfully accepted by the API
		}

		public enum SourceUploadStatusEnum
		{
			[Description("Pending")]
			Pending = 0,

			[Description("Processing")]
			Processing = 1,

			[Description("Completed")]
			Completed = 2,

			[Description("Failed")]
			Failed = 3
		}
	}
}
