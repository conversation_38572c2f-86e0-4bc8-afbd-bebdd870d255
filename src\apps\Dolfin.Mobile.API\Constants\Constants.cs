﻿
using System.Reflection;

namespace Dolfin.Mobile.API.Constants
{
    public class Constants
    {
        public class JOBCONFIG_CODE
        {
            public const string EMAIL = "SCHEDULER_EMAIL";
        }

        public class EMAIL_TEMPLATE
        {
            public const string RESENDCONFIRMATIONEMAIL = "ResendConfirmationEmail";
            public const string RESETPASSWORD = "ResetPassword";
        }

        public class UPLOAD_MODULE
        {
            public const string PRODUCT = "product";
            //public const string GENERAL = "general";
            //public const string CUSTOMER = "customer";
            //public const string TRANSACTION = "transaction";
            public const string PROFILE = "profile";
        }

        public class UPLOAD_DIRECTORY
        {
            public const string PRODUCT = "company/{company-id}/{file-type}/{module-name}/{module-id}/";
            //public const string GENERAL = "company/{company-id}/{file-type}/{module-name}/{module-id}/";
            //public const string CUSTOMER = "company/{company-id}/{file-type}/{module-name}/{module-id}/";
            //public const string TRANSACTION = "company/{company-id}/{file-type}/{module-name}/{module-id}/";
            public const string PROFILE = "company/{company-id}/{file-type}/{module-name}/{module-id}/";
        }

        public class EINVOICE
        {
            public const string LOGINTAXSYSTEM = "/connect/token";
            public const string SEARCHTIN = "/api/v1.0/taxpayer/search/tin?idType={idType}&idValue={idValue}";
            public const string TAXPAYERTIN = "/api/v1.0/taxpayer/validate/{tin}?idType={idType}&idValue={idValue}";
            public const string CANCELREJECTDOC = "/api/v1.0/documents/state/{uuid}/state";
            public const string SUBMITDOCUMENT = "/api/v1.0/documentsubmissions";
            public const string DOCUMENTTYPES = "/api/v1.0/documenttypes";
        }

        public static bool IsConstantNameValid(string name)
        {
            var fields = typeof(UPLOAD_MODULE).GetFields(BindingFlags.Public | BindingFlags.Static | BindingFlags.FlattenHierarchy);

            return fields.Any(fi => fi.Name.Equals(name));
        }

        public static string? GetConstantValueByName(string name)
        {
            var field = typeof(UPLOAD_MODULE).GetField(name, BindingFlags.Public | BindingFlags.Static | BindingFlags.FlattenHierarchy);

            if (field != null && field.IsLiteral && !field.IsInitOnly)
            {
                return (string?)field.GetRawConstantValue();
            }

            return null;  // not found
        }

        public static string? GetConstantName(string value)
        {
            return typeof(UPLOAD_MODULE)
                .GetFields(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.FlattenHierarchy)
                .FirstOrDefault(f => f.IsLiteral && !f.IsInitOnly && f.GetRawConstantValue()?.ToString() == value)
                ?.Name;
        }
    }
}
