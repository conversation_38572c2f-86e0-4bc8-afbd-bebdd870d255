﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;

using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Entity;
using Dolfin.Utility.Enum;
using Dolfin.Utility.Models;
using Dolfin.Utility.Utils;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;

namespace Dolfin.Mobile.API.Services
{
    public abstract class BaseComponent<TEntity> where TEntity : class
    {
        protected List<Expression<Func<TEntity, object>>> _includes = new List<Expression<Func<TEntity, object>>>();
        protected readonly DolfinDbContext? _dbContext;
        protected readonly DbContextOptions<DolfinDbContext> _dbContextOptions;

        public BaseComponent(DolfinDbContext dbContext, DbContextOptions<DolfinDbContext> dbContextOptions)
        {
            _dbContext = dbContext;
            _dbContextOptions = dbContextOptions;
        }

        // Compatibility constructor for existing services
        // This will be used during the transition to scoped DbContext
        public BaseComponent(DbContextOptions<DolfinDbContext> dbContextOptions)
        {
            _dbContext = null;
            _dbContextOptions = dbContextOptions;
        }

        public void DefineInclude(params Expression<Func<TEntity, object>>[] includes)
        {
            if (_includes != null && _includes.Count >= 0)
            {
                _includes.Clear();
            }
            else
            {
                _includes = new List<Expression<Func<TEntity, object>>>();
            }

            foreach (var i in includes)
            {
                _includes.Add(i);
            }
        }

        public void DefineInclude(List<Expression<Func<TEntity, object>>> includes)
        {
            if (_includes != null && _includes.Count >= 0)
            {
                _includes.Clear();
            }
            else
            {
                _includes = new List<Expression<Func<TEntity, object>>>();
            }

            foreach (var i in includes)
            {
                _includes.Add(i);
            }
        }

        public virtual async Task<TEntity> CreateAsync(TEntity entity, DolfinDbContext dbContextRollback = null)
        {
            bool isNewDbContext = dbContextRollback == null;
            DolfinDbContext dbContext;

            try
            {
                dbContext = dbContextRollback ?? GetDbContext();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Failed to initialize database context.", ex);
            }

            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;

                try
                {
                    dbContext.Add<TEntity>(entity);
                    dbContext.SaveChanges();

                    if (currentTransaction == null)
                        await transaction.CommitAsync();

                    return entity;
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    throw ex;
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }
        }

        public virtual async Task<T> CreateAsync<T>(T entity, DolfinDbContext dbContextRollback = null) where T : class
        {
            bool isNewDbContext = dbContextRollback == null;
            DolfinDbContext dbContext;

            try
            {
                dbContext = dbContextRollback ?? GetDbContext();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Failed to initialize database context.", ex);
            }

            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;

                try
                {
                    dbContext.Add<T>(entity);
                    await dbContext.SaveChangesAsync();

                    if (currentTransaction == null)
                        await transaction.CommitAsync();

                    return entity;
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    throw ex;
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }
        }

        public virtual async Task<TEntity> UpdateAsync(TEntity entity, DolfinDbContext dbContextRollback = null)
        {
            bool isNewDbContext = dbContextRollback == null;
            DolfinDbContext dbContext;

            try
            {
                dbContext = dbContextRollback ?? GetDbContext();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Failed to initialize database context.", ex);
            }

            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;

                try
                {
                    // Check if the entity is already being tracked
                    var existingEntity = dbContext.Set<TEntity>().Local.FirstOrDefault(e =>
                        dbContext.Entry(e).Property("Id").CurrentValue.Equals(
                            dbContext.Entry(entity).Property("Id").CurrentValue));

                    if (existingEntity != null)
                    {
                        // If the entity is already tracked, update its values instead of detaching
                        dbContext.Entry(existingEntity).CurrentValues.SetValues(entity);
                    }
                    else
                    {
                        // Only attach if not already tracked
                        dbContext.Entry(entity).State = EntityState.Modified;
                    }
                    dbContext.SaveChanges();

                    if (currentTransaction == null)
                        await transaction.CommitAsync();

                    return entity;
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    throw ex;
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }
        }

        public virtual async Task<T> UpdateAsync<T>(T entity, DolfinDbContext dbContextRollback = null) where T : class
        {

            bool isNewDbContext = dbContextRollback == null;
            DolfinDbContext dbContext;

            try
            {
                dbContext = dbContextRollback ?? GetDbContext();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Failed to initialize database context.", ex);
            }

            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;

                try
                {
                    // Check if the entity is already being tracked
                    var existingEntity = dbContext.Set<T>().Local.FirstOrDefault(e =>
                        dbContext.Entry(e).Property("Id").CurrentValue.Equals(
                            dbContext.Entry(entity).Property("Id").CurrentValue));

                    if (existingEntity != null)
                    {
                        // If the entity is already tracked, detach it first
                        dbContext.Entry(existingEntity).State = EntityState.Detached;
                    }

                    // Now attach and mark as modified
                    dbContext.Entry(entity).State = EntityState.Modified;
                    dbContext.SaveChanges();

                    if (currentTransaction == null)
                        await transaction.CommitAsync();

                    return entity;
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    throw ex;
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }
        }

        public virtual void Delete(TEntity entity, DolfinDbContext dbContextRollback = null)
        {
            using (DolfinDbContext dbContext = dbContextRollback == null ? GetDbContext() : dbContextRollback)
            {
                dbContext.Remove<TEntity>(entity);
                dbContext.SaveChanges();
            }
        }

        public virtual void Delete<T>(T entity, DolfinDbContext dbContextRollback = null) where T : class
        {
            using (DolfinDbContext dbContext = dbContextRollback == null ? GetDbContext() : dbContextRollback)
            {
                dbContext.Remove<T>(entity);
                dbContext.SaveChanges();
            }
        }

        public virtual int CountAll()
        {
            using (DolfinDbContext dbContext = GetDbContext())
            {
                return dbContext.Set<TEntity>().Count();
            }
        }

        public virtual int CountAll<T>() where T : class
        {
            using (DolfinDbContext dbContext = GetDbContext())
            {
                return dbContext.Set<T>().Count();
            }
        }

        public virtual int Count(Expression<Func<TEntity, bool>> predicate)
        {
            using (DolfinDbContext dbContext = GetDbContext())
            {
                return dbContext.Set<TEntity>().Count(predicate);
            }
        }

        public virtual int Count<T>(Expression<Func<T, bool>> predicate) where T : class
        {
            using (DolfinDbContext dbContext = GetDbContext())
            {
                return dbContext.Set<T>().Count(predicate);
            }
        }

        #region Exist Methods
        protected bool Exist(Expression<Func<TEntity, bool>> predicate)
        {
            return Count(predicate) >= 1;
        }

        protected bool Exist<T>(Expression<Func<T, bool>> predicate) where T : class
        {
            return Count(predicate) >= 1;
        }
        #endregion

        #region Query Methods
        protected TEntity QueryOne(Expression<Func<TEntity, bool>> predicate)
        {
            using (DolfinDbContext dbContext = GetDbContext())
            {
                var repo = dbContext.Set<TEntity>();

                var q = repo.AsQueryable();

                q = SetupInclude(q);

                return q.FirstOrDefault(predicate);
            }
        }

        protected T QueryOne<T>(Expression<Func<T, bool>> predicate, List<Expression<Func<T, object>>> includes) where T : class
        {
            using (DolfinDbContext dbContext = GetDbContext())
            {
                var repo = dbContext.Set<T>();

                var q = repo.AsQueryable();

                q = SetupInclude(q, includes);

                return q.FirstOrDefault(predicate);
            }
        }

        protected TEntity QueryOne<TOrderKey>(Expression<Func<TEntity, bool>> predicate, Expression<Func<TEntity, TOrderKey>> orderBy, bool isAscending = true)
        {
            using (DolfinDbContext dbContext = GetDbContext())
            {
                var repo = dbContext.Set<TEntity>();

                var q = repo.Where(predicate);

                q = SetupIncludeNSort(q, orderBy, isAscending);

                return q.FirstOrDefault();
            }
        }

        protected T QueryOne<T, TOrderKey>(Expression<Func<T, bool>> predicate, List<Expression<Func<T, object>>> includes, Expression<Func<T, TOrderKey>> orderBy, bool isAscending = true) where T : class
        {
            using (DolfinDbContext dbContext = GetDbContext())
            {
                var repo = dbContext.Set<T>();

                var q = repo.Where(predicate);

                q = SetupIncludeNSort<T, TOrderKey>(q, includes, orderBy, isAscending);

                return q.FirstOrDefault();
            }
        }

        protected IList<TEntity> Query(Expression<Func<TEntity, bool>> predicate)
        {
            using (DolfinDbContext dbContext = GetDbContext())
            {
                var repo = dbContext.Set<TEntity>();

                var q = repo.Where(predicate);

                q = SetupInclude(q);

                return q.ToList();
            }
        }

        protected IList<T> Query<T>(Expression<Func<T, bool>> predicate, List<Expression<Func<T, object>>> includes = null) where T : class
        {
            using (DolfinDbContext dbContext = GetDbContext())
            {
                var repo = dbContext.Set<T>();

                var q = repo.Where(predicate);

                q = SetupInclude<T>(q, includes);

                return q.ToList();
            }
        }

        protected IList<TEntity> Query<TOrderKey>(Expression<Func<TEntity, bool>> predicate, Expression<Func<TEntity, TOrderKey>> orderBy, bool isAscending = true)
        {
            using (DolfinDbContext dbContext = GetDbContext())
            {
                var repo = dbContext.Set<TEntity>();

                var q = repo.Where(predicate);

                q = SetupIncludeNSort(q, orderBy, isAscending);

                return q.ToList();
            }
        }

        protected IList<T> Query<T, TOrderKey>(Expression<Func<T, bool>> predicate, List<Expression<Func<T, object>>> includes, Expression<Func<T, TOrderKey>> orderBy, bool isAscending = true) where T : class
        {
            using (DolfinDbContext dbContext = GetDbContext())
            {
                var repo = dbContext.Set<T>();

                var q = repo.Where(predicate);

                q = SetupIncludeNSort(q, includes, orderBy, isAscending);

                return q.ToList();
            }
        }

        protected IList<TEntity> Query<TOrderKey>(Expression<Func<TEntity, bool>> predicate, Expression<Func<TEntity, TOrderKey>> orderBy, int beginRowIndex, int maximumRows, bool isAscending = true)
        {
            using (DolfinDbContext dbContext = GetDbContext())
            {
                var repo = dbContext.Set<TEntity>();

                var q = repo.Where(predicate);

                q = SetupIncludeNSort(q, orderBy, isAscending);

                return q.Skip(beginRowIndex).Take(maximumRows).ToList();
            }
        }

        protected IList<T> Query<T, TOrderKey>(Expression<Func<T, bool>> predicate, List<Expression<Func<T, object>>> includes, Expression<Func<T, TOrderKey>> orderBy, int beginRowIndex, int maximumRows, bool isAscending = true) where T : class
        {
            using (DolfinDbContext dbContext = GetDbContext())
            {
                var repo = dbContext.Set<T>();

                var q = repo.Where(predicate);

                q = SetupIncludeNSort(q, includes, orderBy, isAscending);

                return q.Skip(beginRowIndex).Take(maximumRows).ToList();
            }
        }

        protected TEntity QueryOneAsNoTracking(Expression<Func<TEntity, bool>> predicate)
        {
            using (DolfinDbContext dbContext = GetDbContext())
            {
                var repo = dbContext.Set<TEntity>();

                var q = repo.AsQueryable();

                q = SetupInclude(q);

                return q.AsNoTracking().FirstOrDefault(predicate);
            }
        }

        protected T QueryOneAsNoTracking<T>(Expression<Func<T, bool>> predicate, List<Expression<Func<T, object>>> includes) where T : class
        {
            using (DolfinDbContext dbContext = GetDbContext())
            {
                var repo = dbContext.Set<T>();

                var q = repo.AsQueryable();

                q = SetupInclude(q, includes);

                return q.AsNoTracking().FirstOrDefault(predicate);
            }
        }

        protected TEntity QueryOneAsNoTracking<TOrderKey>(Expression<Func<TEntity, bool>> predicate, Expression<Func<TEntity, TOrderKey>> orderBy, bool isAscending = true)
        {
            using (DolfinDbContext dbContext = GetDbContext())
            {
                var repo = dbContext.Set<TEntity>();

                var q = repo.Where(predicate);

                q = SetupIncludeNSort(q, orderBy, isAscending);

                return q.AsNoTracking().FirstOrDefault();
            }
        }

        protected T QueryOneAsNoTracking<T, TOrderKey>(Expression<Func<T, bool>> predicate, List<Expression<Func<T, object>>> includes, Expression<Func<T, TOrderKey>> orderBy, bool isAscending = true) where T : class
        {
            using (DolfinDbContext dbContext = GetDbContext())
            {
                var repo = dbContext.Set<T>();

                var q = repo.Where(predicate);

                q = SetupIncludeNSort(q, includes, orderBy, isAscending);

                return q.AsNoTracking().FirstOrDefault();
            }
        }

        protected IList<TEntity> QueryAsNoTracking(Expression<Func<TEntity, bool>> predicate)
        {
            using (DolfinDbContext dbContext = GetDbContext())
            {
                var repo = dbContext.Set<TEntity>();

                var q = repo.Where(predicate);

                q = SetupInclude(q);

                return q.AsNoTracking().ToList();
            }
        }

        protected IList<T> QueryAsNoTracking<T>(Expression<Func<T, bool>> predicate, List<Expression<Func<T, object>>> includes) where T : class
        {
            using (DolfinDbContext dbContext = GetDbContext())
            {
                var repo = dbContext.Set<T>();

                var q = repo.Where(predicate);

                q = SetupInclude(q, includes);

                return q.AsNoTracking().ToList();
            }
        }

        protected IList<TEntity> QueryAsNoTracking<TOrderKey>(Expression<Func<TEntity, bool>> predicate, Expression<Func<TEntity, TOrderKey>> orderBy, bool isAscending = true)
        {
            using (DolfinDbContext dbContext = GetDbContext())
            {
                var repo = dbContext.Set<TEntity>();

                var q = repo.Where(predicate);

                q = SetupIncludeNSort(q, orderBy, isAscending);

                return q.AsNoTracking().ToList();
            }
        }

        protected IList<T> QueryAsNoTracking<T, TOrderKey>(Expression<Func<T, bool>> predicate, List<Expression<Func<T, object>>> includes, Expression<Func<T, TOrderKey>> orderBy, bool isAscending = true) where T : class
        {
            using (DolfinDbContext dbContext = GetDbContext())
            {
                var repo = dbContext.Set<T>();

                var q = repo.Where(predicate);

                q = SetupIncludeNSort(q, includes, orderBy, isAscending);

                return q.AsNoTracking().ToList();
            }
        }

        protected IList<TEntity> QueryAsNoTracking<TOrderKey>(Expression<Func<TEntity, bool>> predicate, Expression<Func<TEntity, TOrderKey>> orderBy, int beginRowIndex, int maximumRows, bool isAscending = true)
        {
            using (DolfinDbContext dbContext = GetDbContext())
            {
                var repo = dbContext.Set<TEntity>();

                var q = repo.Where(predicate);

                q = SetupIncludeNSort(q, orderBy, isAscending);

                return q.AsNoTracking().Skip(beginRowIndex).Take(maximumRows).ToList();
            }
        }

        protected IList<T> QueryAsNoTracking<T, TOrderKey>(Expression<Func<T, bool>> predicate, List<Expression<Func<T, object>>> includes, Expression<Func<T, TOrderKey>> orderBy, int beginRowIndex, int maximumRows, bool isAscending = true) where T : class
        {
            using (DolfinDbContext dbContext = GetDbContext())
            {
                var repo = dbContext.Set<T>();

                var q = repo.Where(predicate);

                q = SetupIncludeNSort(q, includes, orderBy, isAscending);

                return q.AsNoTracking().Skip(beginRowIndex).Take(maximumRows).ToList();
            }
        }

        #endregion

        #region Protected Method
        protected DolfinDbContext GetDbContext()
        {
            // Return the injected context if available, otherwise create a new one
            // This maintains backward compatibility with existing code
            return _dbContext ?? new DolfinDbContext(_dbContextOptions);
        }

        protected IQueryable<TEntity> SetupIncludeNSort<TOrderKey>(IQueryable<TEntity> queryable, Expression<Func<TEntity, TOrderKey>> orderBy, bool isAscending)
        {
            queryable = SetupInclude(queryable);

            if (isAscending)
            {
                queryable = queryable.OrderBy(orderBy);
            }
            else
            {
                queryable = queryable.OrderByDescending(orderBy);
            }

            return queryable;
        }

        protected IQueryable<T> SetupIncludeNSort<T, TOrderKey>(IQueryable<T> queryable, List<Expression<Func<T, object>>> includes, Expression<Func<T, TOrderKey>> orderBy, bool isAscending) where T : class
        {
            queryable = SetupInclude<T>(queryable, includes);

            if (isAscending)
            {
                queryable = queryable.OrderBy(orderBy);
            }
            else
            {
                queryable = queryable.OrderByDescending(orderBy);
            }

            return queryable;
        }

        protected IQueryable<TEntity> SetupInclude(IQueryable<TEntity> queryable)
        {
            foreach (var i in _includes)
            {
                queryable = queryable.Include(i);
            }

            return queryable;
        }

        protected IQueryable<T> SetupInclude<T>(IQueryable<T> queryable, List<Expression<Func<T, object>>> includes) where T : class
        {
            if (includes != null)
            {
                foreach (var i in includes)
                {
                    queryable = queryable.Include(i);
                }
            }

            return queryable;
        }
        #endregion
    }
}
