﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Dolfin.Framework.Data.Migrations
{
    /// <inheritdoc />
    public partial class paymenttype : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "AccountCode",
                table: "PaymentType",
                type: "text",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.UpdateData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("69ed35dc-8775-4641-9f1d-26d9d91e5f4b"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 17, 7, 56, 34, 800, DateTimeKind.Utc).AddTicks(463));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("11fd5e73-51fa-428a-a919-960ee860b00f"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 17, 7, 56, 34, 800, DateTimeKind.Utc).AddTicks(572));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("5d22874b-5834-40a2-81be-bf5100eb845d"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 17, 7, 56, 34, 800, DateTimeKind.Utc).AddTicks(566));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("c219bf33-28d0-4595-a25b-8018bd87e67a"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 17, 7, 56, 34, 800, DateTimeKind.Utc).AddTicks(563));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("e57e65d0-85a9-49c8-8463-3424a42e1f01"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 17, 7, 56, 34, 800, DateTimeKind.Utc).AddTicks(569));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 17, 7, 56, 34, 800, DateTimeKind.Utc).AddTicks(520));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("81ba4893-d891-4e3c-a5e7-ba13c697bf97"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 17, 7, 56, 34, 800, DateTimeKind.Utc).AddTicks(525));
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "AccountCode",
                table: "PaymentType",
                type: "text",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true);

            migrationBuilder.UpdateData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("69ed35dc-8775-4641-9f1d-26d9d91e5f4b"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 17, 7, 26, 29, 818, DateTimeKind.Utc).AddTicks(2345));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("11fd5e73-51fa-428a-a919-960ee860b00f"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 17, 7, 26, 29, 818, DateTimeKind.Utc).AddTicks(2455));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("5d22874b-5834-40a2-81be-bf5100eb845d"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 17, 7, 26, 29, 818, DateTimeKind.Utc).AddTicks(2450));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("c219bf33-28d0-4595-a25b-8018bd87e67a"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 17, 7, 26, 29, 818, DateTimeKind.Utc).AddTicks(2447));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("e57e65d0-85a9-49c8-8463-3424a42e1f01"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 17, 7, 26, 29, 818, DateTimeKind.Utc).AddTicks(2452));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 17, 7, 26, 29, 818, DateTimeKind.Utc).AddTicks(2402));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("81ba4893-d891-4e3c-a5e7-ba13c697bf97"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 17, 7, 26, 29, 818, DateTimeKind.Utc).AddTicks(2411));
        }
    }
}
