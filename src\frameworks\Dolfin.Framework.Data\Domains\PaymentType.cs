﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Framework.Data.Domains
{
    public partial class PaymentType : _BaseDomain
    {
        public PaymentType()
        {
            TransactionPaid = new HashSet<TransactionPaid>();
        }
        public required string Code { get; set; }
        public required string Name { get; set; }
        public string? AccountCode { get; set; }
        public Guid? AccountGroupId { get; set; }
        public virtual AccountGroup? AccountGroup { get; set; }
        public virtual ICollection<TransactionPaid> TransactionPaid { get; }

    }
}
