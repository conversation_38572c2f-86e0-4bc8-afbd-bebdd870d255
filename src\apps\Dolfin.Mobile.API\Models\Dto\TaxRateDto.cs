﻿using Dolfin.Framework.Data.Domains;

namespace Dolfin.Mobile.API.Models.Dto
{
    public partial class TaxRateDto
    {
        public TaxRateDto()
        {
        }
        public Guid Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public int ChargePercentage { get; set; }
        public Guid TaxCategoryId { get; set; }
        public TaxCategory2Dto? TaxCategory { get; set; }

    }
}
