﻿using System.Net.Mail;
using System.Threading.Tasks;
using Dolfin.Mobile.API.Models;
using Microsoft.AspNetCore.Identity.UI.Services;
using MimeKit;
using MailKit.Net.Smtp;
using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Entity;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;

namespace Dolfin.Mobile.API.Services
{
    public class EmailSender : BaseComponent<EmailAccount>, IEmailSender
    {
        public EmailSender(DbContextOptions<DolfinDbContext> dbContextOptions) : base(dbContextOptions)
        {
        }

        public async Task<EmailTemplate> GetEmailTemplate(string messageTemplateName)
        {
            var q = GetDbContext().Set<EmailTemplate>().AsQueryable()
                    .Include(x => x.EmailAccount)
                    .Where(x => x.Name == messageTemplateName && x.IsActive == true);
            return await q.FirstOrDefaultAsync();
        }

        public async Task<Guid> InsertEmail(EmailTemplate messageTemplate,
            EmailAccount emailAccount/*, IEnumerable<Token> tokens*/,
            string toEmailAddress, string toName,
            string attachmentFilePath = null, string attachmentFileName = null, DateTime? dontSendBeforeDateUtc = null,
            string replyToEmailAddress = null, string replyToName = null,
            string fromEmail = null, string fromName = null, string subject = null, string body = null)
        {
            if (messageTemplate == null)
                throw new ArgumentNullException("messageTemplate");

            if (emailAccount == null)
                throw new ArgumentNullException("emailAccount");

            //retrieve localized message template data
            var bcc = messageTemplate.BccEmailAddresses;
            if (String.IsNullOrEmpty(subject))
                subject = messageTemplate.Subject;
            if (String.IsNullOrEmpty(body))
                body = messageTemplate.Body;

            //Replace subject and body tokens 
            //var subjectReplaced = _tokenizer.Replace(subject, tokens, false);
            //var bodyReplaced = _tokenizer.Replace(body, tokens, true);

            var newEmail = new Email
            {
                Priority = 5,
                FromEmail = !string.IsNullOrEmpty(fromEmail) ? fromEmail : emailAccount.Email,
                FromName = !string.IsNullOrEmpty(fromName) ? fromName : emailAccount.DisplayName,
                ToEmail = toEmailAddress,
                ToName = toName,
                ReplyToEmail = replyToEmailAddress,
                ReplyToName = replyToName,
                Cc = string.Empty,
                Bcc = string.Empty,//bcc,
                Subject = subject,//subjectReplaced,
                Body = body,//bodyReplaced,
                AttachmentFilePath = attachmentFilePath,
                AttachmentFileName = attachmentFileName,
                DontSendBeforeDateUtc = dontSendBeforeDateUtc,
                EmailAccountId = emailAccount.Id,
                CreatedAt = DateTime.UtcNow,
            };

            var email = await CreateAsync(newEmail);

            return email.Id;
        }

        public Task SendEmailAsync(string email, string subject, string htmlMessage)
        {
            // Implement your email sending logic here (e.g., using SMTP or SendGrid).

            //var bcc = String.IsNullOrWhiteSpace(queuedEmail.Bcc)
            //            ? null
            //            : queuedEmail.Bcc.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries);
            //var cc = String.IsNullOrWhiteSpace(queuedEmail.Cc)
            //            ? null
            //            : queuedEmail.Cc.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries);

            var emailAccount = GetEmailAccount().Result;

            EmailMessage emailMessage = new EmailMessage();
            emailMessage.Subject = subject;
            emailMessage.Content = htmlMessage;

            //FROM
            emailMessage.FromAddresses.Add(new EmailAddress()
            {
                Name = emailAccount.DisplayName,
                Address = emailAccount.Email
            });

            //TO
            emailMessage.ToAddresses.Add(new EmailAddress()
            {
                Name = email,
                Address = email
            });

            ////BCC
            //if (bcc != null)
            //{
            //    foreach (var address in bcc.Where(bccValue => !String.IsNullOrWhiteSpace(bccValue)))
            //    {
            //        emailMessage.BCCAddresses.Add(new EmailAddress()
            //        {
            //            Address = address.Trim()
            //        });
            //    }
            //}

            ////CC
            //if (cc != null)
            //{
            //    foreach (var address in cc.Where(bccValue => !String.IsNullOrWhiteSpace(bccValue)))
            //    {
            //        emailMessage.CCAddresses.Add(new EmailAddress()
            //        {
            //            Address = address.Trim()
            //        });
            //    }
            //}

            Send(emailMessage, emailAccount);
            return Task.CompletedTask;
        }

        public void Send(EmailMessage emailMessage, EmailAccount emailAccount)
        {
            var message = new MimeMessage();
            message.To.AddRange(emailMessage.ToAddresses.Select(x => new MailboxAddress(x.Name, x.Address)));
            message.From.AddRange(emailMessage.FromAddresses.Select(x => new MailboxAddress(x.Name, x.Address)));

            if (emailMessage.CCAddresses.Count > 0)
            {
                message.Cc.AddRange(emailMessage.CCAddresses.Select(x => new MailboxAddress(x.Name, x.Address)));
            }

            if (emailMessage.BCCAddresses.Count > 0)
            {
                message.Bcc.AddRange(emailMessage.BCCAddresses.Select(x => new MailboxAddress(x.Name, x.Address)));
            }

            message.Subject = emailMessage.Subject;

            var builder = new BodyBuilder { HtmlBody = emailMessage.Content };

            if (emailMessage.Attachments.Count > 0)
            {
                for (int i = 0; i < emailMessage.Attachments.Count; i++)
                {
                    builder.Attachments.Add(emailMessage.Attachments[i].Filename, emailMessage.Attachments[i].Data);
                }
            }

            message.Body = builder.ToMessageBody();

            try
            {
                SendMimeMessage(message, emailAccount);
            }
            catch (Exception ex)
            {
                //_logger.LogError(ex, "Error when sending email.");
                throw;

            }
        }

        private void SendMimeMessage(MimeMessage message, EmailAccount emailAccount)
        {
            //Be careful that the SmtpClient class is the one from Mailkit not the framework!
            //_logger.LogInformation("## Using SMTP Client ##");
            using (var emailClient = new MailKit.Net.Smtp.SmtpClient())
            {
                //_logger.LogInformation("Connecting to mail server: {0} at port: {1} with ssl enabled: {2}", emailAccount.Host, emailAccount.Port, emailAccount.EnableSsl);
                var sslOption = emailAccount.EnableSsl ? MailKit.Security.SecureSocketOptions.Auto : MailKit.Security.SecureSocketOptions.None;
                emailClient.Connect(emailAccount.Host, emailAccount.Port, sslOption);

                //Remove any OAuth functionality as we won't be using it. 
                //emailClient.AuthenticationMechanisms.Remove("XOAUTH2");
                //_logger.LogInformation("Successfully connected to mail server.");
                //_logger.LogInformation("Authenticating with mail server at mailbox: {0}", emailAccount.Username);
                emailClient.Authenticate(emailAccount.Username, emailAccount.Password);
                //_logger.LogInformation("Successfully authenticated with mail server.");

                emailClient.Send(message);
                //_logger.LogInformation("Successfully sent the email: " + message.Subject);

                emailClient.Disconnect(true);
            }
        }

        private async Task<EmailAccount> GetEmailAccount()
        {
            var query = GetDbContext().Set<EmailAccount>().AsQueryable()
                .Where(x => x.IsDefault);

            return await query.FirstAsync();
        }
    }
}
