﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Dolfin.Framework.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddedInventoryProduct : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Inventory_Product",
                table: "Inventory");

            migrationBuilder.DropForeignKey(
                name: "FK_InventoryItem_Inventory",
                table: "InventoryItem");

            migrationBuilder.DropIndex(
                name: "IX_Inventory_ProductId",
                table: "Inventory");

            migrationBuilder.DeleteData(
                table: "TaxRate",
                keyColumn: "Id",
                keyValue: new Guid("59bb4a30-7cae-47ab-800d-a117e0134ac5"));

            migrationBuilder.DropColumn(
                name: "BalanceQuantity",
                table: "Inventory");

            migrationBuilder.DropColumn(
                name: "ProductId",
                table: "Inventory");

            migrationBuilder.DropColumn(
                name: "SafeQuantity",
                table: "Inventory");

            migrationBuilder.DropColumn(
                name: "StockMethod",
                table: "Inventory");

            migrationBuilder.DropColumn(
                name: "TotalStockQuantity",
                table: "Inventory");

            migrationBuilder.RenameColumn(
                name: "InventoryId",
                table: "InventoryItem",
                newName: "InventoryProductId");

            migrationBuilder.RenameIndex(
                name: "IX_InventoryItem_InventoryId",
                table: "InventoryItem",
                newName: "IX_InventoryItem_InventoryProductId");

            migrationBuilder.CreateTable(
                name: "InventoryProduct",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    StockMethod = table.Column<int>(type: "integer", nullable: false),
                    TotalStockQuantity = table.Column<decimal>(type: "numeric", nullable: false),
                    BalanceQuantity = table.Column<decimal>(type: "numeric", nullable: false),
                    SafeQuantity = table.Column<decimal>(type: "numeric", nullable: false),
                    BranchId = table.Column<Guid>(type: "uuid", nullable: false),
                    ProductId = table.Column<Guid>(type: "uuid", nullable: false),
                    InventoryId = table.Column<Guid>(type: "uuid", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValueSql: "true"),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "now()"),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_InventoryProduct", x => x.Id);
                    table.ForeignKey(
                        name: "FK_InventoryProduct_Branch_BranchId",
                        column: x => x.BranchId,
                        principalTable: "Branch",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_InventoryProduct_Inventory_InventoryId",
                        column: x => x.InventoryId,
                        principalTable: "Inventory",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_InventoryProduct_Product",
                        column: x => x.ProductId,
                        principalTable: "Product",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.UpdateData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("69ed35dc-8775-4641-9f1d-26d9d91e5f4b"),
                column: "CreatedAt",
                value: new DateTime(2024, 12, 2, 13, 25, 57, 393, DateTimeKind.Utc).AddTicks(9825));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("11fd5e73-51fa-428a-a919-960ee860b00f"),
                column: "CreatedAt",
                value: new DateTime(2024, 12, 2, 13, 25, 57, 393, DateTimeKind.Utc).AddTicks(9967));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("5d22874b-5834-40a2-81be-bf5100eb845d"),
                column: "CreatedAt",
                value: new DateTime(2024, 12, 2, 13, 25, 57, 393, DateTimeKind.Utc).AddTicks(9962));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("c219bf33-28d0-4595-a25b-8018bd87e67a"),
                column: "CreatedAt",
                value: new DateTime(2024, 12, 2, 13, 25, 57, 393, DateTimeKind.Utc).AddTicks(9959));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("e57e65d0-85a9-49c8-8463-3424a42e1f01"),
                column: "CreatedAt",
                value: new DateTime(2024, 12, 2, 13, 25, 57, 393, DateTimeKind.Utc).AddTicks(9965));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"),
                column: "CreatedAt",
                value: new DateTime(2024, 12, 2, 13, 25, 57, 393, DateTimeKind.Utc).AddTicks(9876));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("81ba4893-d891-4e3c-a5e7-ba13c697bf97"),
                column: "CreatedAt",
                value: new DateTime(2024, 12, 2, 13, 25, 57, 393, DateTimeKind.Utc).AddTicks(9929));

            migrationBuilder.CreateIndex(
                name: "IX_InventoryProduct_BranchId",
                table: "InventoryProduct",
                column: "BranchId");

            migrationBuilder.CreateIndex(
                name: "IX_InventoryProduct_InventoryId",
                table: "InventoryProduct",
                column: "InventoryId");

            migrationBuilder.CreateIndex(
                name: "IX_InventoryProduct_ProductId",
                table: "InventoryProduct",
                column: "ProductId");

            migrationBuilder.AddForeignKey(
                name: "FK_InventoryItem_InventoryProduct",
                table: "InventoryItem",
                column: "InventoryProductId",
                principalTable: "InventoryProduct",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_InventoryItem_InventoryProduct",
                table: "InventoryItem");

            migrationBuilder.DropTable(
                name: "InventoryProduct");

            migrationBuilder.RenameColumn(
                name: "InventoryProductId",
                table: "InventoryItem",
                newName: "InventoryId");

            migrationBuilder.RenameIndex(
                name: "IX_InventoryItem_InventoryProductId",
                table: "InventoryItem",
                newName: "IX_InventoryItem_InventoryId");

            migrationBuilder.AddColumn<decimal>(
                name: "BalanceQuantity",
                table: "Inventory",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<Guid>(
                name: "ProductId",
                table: "Inventory",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AddColumn<decimal>(
                name: "SafeQuantity",
                table: "Inventory",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<int>(
                name: "StockMethod",
                table: "Inventory",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<decimal>(
                name: "TotalStockQuantity",
                table: "Inventory",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.UpdateData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("69ed35dc-8775-4641-9f1d-26d9d91e5f4b"),
                column: "CreatedAt",
                value: new DateTime(2024, 10, 26, 18, 45, 7, 717, DateTimeKind.Utc).AddTicks(3494));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("11fd5e73-51fa-428a-a919-960ee860b00f"),
                column: "CreatedAt",
                value: new DateTime(2024, 10, 26, 18, 45, 7, 717, DateTimeKind.Utc).AddTicks(3663));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("5d22874b-5834-40a2-81be-bf5100eb845d"),
                column: "CreatedAt",
                value: new DateTime(2024, 10, 26, 18, 45, 7, 717, DateTimeKind.Utc).AddTicks(3657));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("c219bf33-28d0-4595-a25b-8018bd87e67a"),
                column: "CreatedAt",
                value: new DateTime(2024, 10, 26, 18, 45, 7, 717, DateTimeKind.Utc).AddTicks(3655));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("e57e65d0-85a9-49c8-8463-3424a42e1f01"),
                column: "CreatedAt",
                value: new DateTime(2024, 10, 26, 18, 45, 7, 717, DateTimeKind.Utc).AddTicks(3660));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"),
                column: "CreatedAt",
                value: new DateTime(2024, 10, 26, 18, 45, 7, 717, DateTimeKind.Utc).AddTicks(3559));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("81ba4893-d891-4e3c-a5e7-ba13c697bf97"),
                column: "CreatedAt",
                value: new DateTime(2024, 10, 26, 18, 45, 7, 717, DateTimeKind.Utc).AddTicks(3567));

            migrationBuilder.InsertData(
                table: "TaxRate",
                columns: new[] { "Id", "ChargePercentage", "Code", "CreatedAt", "CreatedBy", "Description", "IsActive", "Name", "TaxCategoryId", "UpdatedAt", "UpdatedBy" },
                values: new object[] { new Guid("59bb4a30-7cae-47ab-800d-a117e0134ac5"), 0, "SR", new DateTime(2024, 10, 26, 18, 45, 7, 717, DateTimeKind.Utc).AddTicks(3622), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), null, true, "Sales Rate", new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"), null, null });

            migrationBuilder.CreateIndex(
                name: "IX_Inventory_ProductId",
                table: "Inventory",
                column: "ProductId");

            migrationBuilder.AddForeignKey(
                name: "FK_Inventory_Product",
                table: "Inventory",
                column: "ProductId",
                principalTable: "Product",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_InventoryItem_Inventory",
                table: "InventoryItem",
                column: "InventoryId",
                principalTable: "Inventory",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
