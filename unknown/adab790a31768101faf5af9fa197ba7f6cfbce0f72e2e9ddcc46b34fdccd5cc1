﻿using Microsoft.Extensions.Options;
using Dolfin.Framework.Data.Model;
using Amazon;
using Amazon.S3;
using Amazon.S3.Model;
using DocumentFormat.OpenXml.Wordprocessing;
using System.Xml.Linq;

namespace Dolfin.Framework.Data.Utils
{
    public class AmazonS3Service
    {
        private readonly AWSS3Config _awsS3Config;
        public AmazonS3Service(IOptions<AWSS3Config> awsS3Config)
        {
            _awsS3Config = awsS3Config.Value;
        }

        private static RegionEndpoint bucketRegion = RegionEndpoint.APSoutheast1;

        // We're removing the createBucket method as it requires ListAllMyBuckets permission
        // and isn't necessary for normal operation with an existing bucket

        public async Task<UploadFileModelResponse> UploadObject(UploadFileRequest file)
        {
            try
            {
                if (_awsS3Config.BucketName == null || _awsS3Config.AccessKey == null || _awsS3Config.AccessSecret == null)
                {
                    return new UploadFileModelResponse
                    {
                        Success = false,
                        FileName = file.FileName,
                        Bucket = _awsS3Config.BucketName,
                        FullPath = file.FolderPath + file.FileName
                    };
                }
                // connecting to the client
                var client = new AmazonS3Client(_awsS3Config.AccessKey, _awsS3Config.AccessSecret, bucketRegion);
                // get the file and convert it to the byte[]
                byte[] fileBytes = new Byte[file.File.Length];
                file.File.OpenReadStream().Read(fileBytes, 0, Int32.Parse(file.File.Length.ToString()));

                if (string.IsNullOrEmpty(file.FileName))
                {
                    file.FileName = Path.GetFileNameWithoutExtension(file.File.FileName);
                }

                // create unique file name for prevent the mess
                var fileName = file.FileName + "_" + Guid.NewGuid();
                string ext = Path.GetExtension(file.File.FileName);
                fileName = file.FolderPath + fileName + ext;

                PutObjectResponse response = null;

                using (var stream = new MemoryStream(fileBytes))
                {
                    var request = new PutObjectRequest
                    {
                        BucketName = _awsS3Config.BucketName,
                        Key = fileName,
                        InputStream = stream,
                        ContentType = file.File.ContentType,
                        CannedACL = S3CannedACL.BucketOwnerRead
                    };

                    response = await client.PutObjectAsync(request);
                };

                if (!string.IsNullOrEmpty(file.DeleteFileUrl))
                {
                    DeleteFileRequest deleteRequest = new DeleteFileRequest()
                    {
                        FileUrl = file.DeleteFileUrl
                    };
                    await RemoveObject(deleteRequest);
                }

                if (response.HttpStatusCode == System.Net.HttpStatusCode.OK)
                {
                    // this model is up to you, in my case I have to use it following;
                    return new UploadFileModelResponse
                    {
                        Success = true,
                        FileName = file.FileName,
                        Bucket = _awsS3Config.BucketName,
                        FullPath = fileName,
                        Size = file.File.Length,
                        MimeType = file.File.ContentType
                    };
                }
                else
                {
                    // this model is up to you, in my case I have to use it following;
                    return new UploadFileModelResponse
                    {
                        Success = false,
                        FileName = file.FileName,
                        Bucket = _awsS3Config.BucketName,
                        FullPath = fileName
                    };
                }
            }
            catch (AmazonS3Exception s3Ex)
            {
                // Log specific S3 error details
                Console.WriteLine($"S3 Error: {s3Ex.Message}, Error Code: {s3Ex.ErrorCode}, Request ID: {s3Ex.RequestId}");

                return new UploadFileModelResponse
                {
                    Success = false,
                    FileName = $"S3 Error: {s3Ex.Message}",
                    ErrorDetails = s3Ex.ToString()
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"General Error: {ex.Message}");

                return new UploadFileModelResponse
                {
                    Success = false,
                    FileName = $"Error: {ex.Message}",
                    ErrorDetails = ex.ToString()
                };
            }
        }

        public async Task<bool> isFileExists(string bucket, string filename)
        {
            try
            {
                // Validate inputs
                if (string.IsNullOrEmpty(bucket) || string.IsNullOrEmpty(filename))
                {
                    Console.WriteLine("Invalid input: bucket or filename is null or empty");
                    return false;
                }

                var client = new AmazonS3Client(_awsS3Config.AccessKey, _awsS3Config.AccessSecret, bucketRegion);

                // Use GetObjectMetadataAsync instead of GetObjectAsync to avoid downloading the entire file
                var request = new GetObjectMetadataRequest
                {
                    BucketName = bucket,
                    Key = filename
                };

                var response = await client.GetObjectMetadataAsync(request);
                return true; // If we get here, the file exists
            }
            catch (AmazonS3Exception s3Ex) when (s3Ex.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                // This is expected if the file doesn't exist
                return false;
            }
            catch (AmazonS3Exception s3Ex)
            {
                Console.WriteLine($"S3 Error checking if file exists: {s3Ex.Message}, Error Code: {s3Ex.ErrorCode}, Request ID: {s3Ex.RequestId}");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error checking if file exists: {ex.Message}");
                return false;
            }
        }

        public async Task<DeleteFileResponse> RemoveObject(DeleteFileRequest deleteFileRequest)
        {
            try
            {
                // Validate inputs
                if (string.IsNullOrEmpty(deleteFileRequest.FileUrl))
                {
                    return new DeleteFileResponse
                    {
                        Success = false,
                        FileName = "File URL is null or empty",
                        Bucket = _awsS3Config.BucketName,
                        ErrorDetails = "File URL cannot be null or empty"
                    };
                }

                if (_awsS3Config.BucketName == null || _awsS3Config.AccessKey == null || _awsS3Config.AccessSecret == null)
                {
                    return new DeleteFileResponse
                    {
                        Success = false,
                        FileName = deleteFileRequest.FileUrl,
                        Bucket = _awsS3Config.BucketName,
                        ErrorDetails = "AWS S3 configuration is incomplete. Check BucketName, AccessKey, and AccessSecret settings."
                    };
                }

                // Check if file exists first
                if (await isFileExists(_awsS3Config.BucketName, deleteFileRequest.FileUrl))
                {
                    var client = new AmazonS3Client(_awsS3Config.AccessKey, _awsS3Config.AccessSecret, bucketRegion);

                    var request = new DeleteObjectRequest
                    {
                        BucketName = _awsS3Config.BucketName,
                        Key = deleteFileRequest.FileUrl
                    };

                    var response = await client.DeleteObjectAsync(request);

                    return new DeleteFileResponse
                    {
                        Success = true,
                        FileName = deleteFileRequest.FileUrl,
                        Bucket = _awsS3Config.BucketName
                    };
                }
                else
                {
                    // File doesn't exist, but we'll consider this a success since the end result is the same
                    return new DeleteFileResponse
                    {
                        Success = true,
                        FileName = deleteFileRequest.FileUrl,
                        Bucket = _awsS3Config.BucketName,
                        ErrorDetails = "File does not exist, but operation is considered successful"
                    };
                }
            }
            catch (AmazonS3Exception s3Ex)
            {
                Console.WriteLine($"S3 Error deleting file: {s3Ex.Message}, Error Code: {s3Ex.ErrorCode}, Request ID: {s3Ex.RequestId}");

                return new DeleteFileResponse
                {
                    Success = false,
                    FileName = deleteFileRequest.FileUrl,
                    Bucket = _awsS3Config.BucketName,
                    ErrorDetails = $"S3 Error: {s3Ex.Message}, Error Code: {s3Ex.ErrorCode}"
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error deleting file: {ex.Message}");

                return new DeleteFileResponse
                {
                    Success = false,
                    FileName = deleteFileRequest.FileUrl,
                    Bucket = _awsS3Config.BucketName,
                    ErrorDetails = $"Error: {ex.Message}"
                };
            }
            /*bug //ckf fix later
            if (response.HttpStatusCode == System.Net.HttpStatusCode.OK)
            {
                return new DeleteFileResponse
                {
                    Success = true,
                    FileName = deleteFileRequest.FileName,
                    Bucket = deleteFileRequest.Bucket
                };
            }
            else
            {
                return new DeleteFileResponse
                {
                    Success = false,
                    FileName = deleteFileRequest.FileName,
                    Bucket = deleteFileRequest.Bucket
                };
            }*/
        }

        public static string ConstructDirectory(string directory, string companyId, string fileType, string moduleName, string uniqueId = null)
        {
            return directory.Replace("{company-id}", companyId).Replace("{file-type}", fileType).Replace("{module-name}", moduleName).Replace("{module-id}", uniqueId);
        }

        public string GetPreSignedUrl(string fileUrl)
        {
            string urlString = "";
            try
            {
                // Validate inputs
                if (string.IsNullOrEmpty(fileUrl))
                {
                    throw new ArgumentException("File URL cannot be null or empty");
                }

                if (_awsS3Config.BucketName == null || _awsS3Config.AccessKey == null || _awsS3Config.AccessSecret == null)
                {
                    throw new InvalidOperationException("AWS S3 configuration is incomplete. Check BucketName, AccessKey, and AccessSecret settings.");
                }

                var client = new AmazonS3Client(_awsS3Config.AccessKey, _awsS3Config.AccessSecret, bucketRegion);

                GetPreSignedUrlRequest request = new GetPreSignedUrlRequest
                {
                    BucketName = _awsS3Config.BucketName,
                    Key = fileUrl,
                    Expires = DateTime.UtcNow.AddMinutes(_awsS3Config.PreSignedMinutes)
                };
                urlString = client.GetPreSignedURL(request);

                if (string.IsNullOrEmpty(urlString))
                {
                    throw new Exception("Failed to generate pre-signed URL");
                }
            }
            catch (AmazonS3Exception s3Ex)
            {
                Console.WriteLine($"S3 Error: {s3Ex.Message}, Error Code: {s3Ex.ErrorCode}, Request ID: {s3Ex.RequestId}");
                throw; // Re-throw to be handled by the caller
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error generating pre-signed URL: {ex.Message}");
                throw; // Re-throw to be handled by the caller
            }
            return urlString;
        }

        public async Task<byte[]> GetObjectData(string fileUrl)
        {
            var client = new AmazonS3Client(_awsS3Config.AccessKey, _awsS3Config.AccessSecret, bucketRegion);

            GetObjectRequest request = new GetObjectRequest
            {
                BucketName = _awsS3Config.BucketName,
                Key = fileUrl
            };

            GetObjectResponse response = await client.GetObjectAsync(request);
            MemoryStream memoryStream = new MemoryStream();

            using (Stream responseStream = response.ResponseStream)
            {
                responseStream.CopyTo(memoryStream);
            }

            return memoryStream.ToArray();
        }

        public async Task<MemoryStream> GetObjectDataStream(string fileUrl)
        {
            var client = new AmazonS3Client(_awsS3Config.AccessKey, _awsS3Config.AccessSecret, bucketRegion);

            GetObjectRequest request = new GetObjectRequest
            {
                BucketName = _awsS3Config.BucketName,
                Key = fileUrl
            };

            GetObjectResponse response = await client.GetObjectAsync(request);
            MemoryStream memoryStream = new MemoryStream();

            using (Stream responseStream = response.ResponseStream)
            {
                responseStream.CopyTo(memoryStream);
            }

            return memoryStream;
        }

        public byte[] ReadStream(Stream responseStream)
        {
            byte[] buffer = new byte[16 * 1024];
            using (MemoryStream ms = new MemoryStream())
            {
                int read;
                while ((read = responseStream.Read(buffer, 0, buffer.Length)) > 0)
                {
                    ms.Write(buffer, 0, read);
                }
                responseStream.CopyTo(ms);
                return ms.ToArray();
            }
        }
    }
}
