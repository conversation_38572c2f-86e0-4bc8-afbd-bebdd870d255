﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Dolfin.Framework.Data.Migrations
{
    /// <inheritdoc />
    public partial class subscriptionntransactionamount : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "TotalSalesTaxNo",
                table: "Transaction");

            migrationBuilder.DropColumn(
                name: "TotalServiceTaxNo",
                table: "Transaction");

            migrationBuilder.RenameColumn(
                name: "TotalServiceTaxNo",
                table: "TransactionItem",
                newName: "TotalUnitAmountWOTax");

            migrationBuilder.RenameColumn(
                name: "TotalSalesTaxNo",
                table: "TransactionItem",
                newName: "TotalUnitAmount");

            migrationBuilder.RenameColumn(
                name: "TaxAmount",
                table: "TransactionItem",
                newName: "ServiceTaxRate");

            migrationBuilder.RenameColumn(
                name: "InclTax",
                table: "TransactionItem",
                newName: "ServiceTaxAmount");

            migrationBuilder.RenameColumn(
                name: "ExclTax",
                table: "TransactionItem",
                newName: "SalesTaxRate");

            migrationBuilder.RenameColumn(
                name: "TotalTaxAmount",
                table: "Transaction",
                newName: "TotalServiceTaxAmount");

            migrationBuilder.RenameColumn(
                name: "TotalInclTax",
                table: "Transaction",
                newName: "TotalSalesTaxAmount");

            migrationBuilder.RenameColumn(
                name: "TotalExclTax",
                table: "Transaction",
                newName: "TotalInclTaxAmount");

            migrationBuilder.AddColumn<decimal>(
                name: "ExclTaxAmount",
                table: "TransactionItem",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "InclTaxAmount",
                table: "TransactionItem",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "SalesTaxAmount",
                table: "TransactionItem",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "SalesTaxRate",
                table: "Transaction",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "ServiceTaxRate",
                table: "Transaction",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "TotalAmountWOTax",
                table: "Transaction",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "TotalExclTaxAmount",
                table: "Transaction",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<Guid>(
                name: "SubscriptionId",
                table: "Company",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "Subscription",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Code = table.Column<string>(type: "text", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: false),
                    Months = table.Column<int>(type: "integer", nullable: false),
                    DisplayOrder = table.Column<int>(type: "integer", nullable: true),
                    Published = table.Column<bool>(type: "boolean", nullable: false, defaultValueSql: "false"),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValueSql: "true"),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "now()"),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Subscription", x => x.Id);
                });

            migrationBuilder.UpdateData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("69ed35dc-8775-4641-9f1d-26d9d91e5f4b"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 2, 19, 22, 52, 351, DateTimeKind.Utc).AddTicks(5349));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("11fd5e73-51fa-428a-a919-960ee860b00f"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 2, 19, 22, 52, 351, DateTimeKind.Utc).AddTicks(5468));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("5d22874b-5834-40a2-81be-bf5100eb845d"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 2, 19, 22, 52, 351, DateTimeKind.Utc).AddTicks(5462));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("c219bf33-28d0-4595-a25b-8018bd87e67a"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 2, 19, 22, 52, 351, DateTimeKind.Utc).AddTicks(5460));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("e57e65d0-85a9-49c8-8463-3424a42e1f01"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 2, 19, 22, 52, 351, DateTimeKind.Utc).AddTicks(5465));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 2, 19, 22, 52, 351, DateTimeKind.Utc).AddTicks(5417));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("81ba4893-d891-4e3c-a5e7-ba13c697bf97"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 2, 19, 22, 52, 351, DateTimeKind.Utc).AddTicks(5424));

            migrationBuilder.CreateIndex(
                name: "IX_Company_SubscriptionId",
                table: "Company",
                column: "SubscriptionId");

            migrationBuilder.AddForeignKey(
                name: "FK_Company_Subscription",
                table: "Company",
                column: "SubscriptionId",
                principalTable: "Subscription",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Company_Subscription",
                table: "Company");

            migrationBuilder.DropTable(
                name: "Subscription");

            migrationBuilder.DropIndex(
                name: "IX_Company_SubscriptionId",
                table: "Company");

            migrationBuilder.DropColumn(
                name: "ExclTaxAmount",
                table: "TransactionItem");

            migrationBuilder.DropColumn(
                name: "InclTaxAmount",
                table: "TransactionItem");

            migrationBuilder.DropColumn(
                name: "SalesTaxAmount",
                table: "TransactionItem");

            migrationBuilder.DropColumn(
                name: "SalesTaxRate",
                table: "Transaction");

            migrationBuilder.DropColumn(
                name: "ServiceTaxRate",
                table: "Transaction");

            migrationBuilder.DropColumn(
                name: "TotalAmountWOTax",
                table: "Transaction");

            migrationBuilder.DropColumn(
                name: "TotalExclTaxAmount",
                table: "Transaction");

            migrationBuilder.DropColumn(
                name: "SubscriptionId",
                table: "Company");

            migrationBuilder.RenameColumn(
                name: "TotalUnitAmountWOTax",
                table: "TransactionItem",
                newName: "TotalServiceTaxNo");

            migrationBuilder.RenameColumn(
                name: "TotalUnitAmount",
                table: "TransactionItem",
                newName: "TotalSalesTaxNo");

            migrationBuilder.RenameColumn(
                name: "ServiceTaxRate",
                table: "TransactionItem",
                newName: "TaxAmount");

            migrationBuilder.RenameColumn(
                name: "ServiceTaxAmount",
                table: "TransactionItem",
                newName: "InclTax");

            migrationBuilder.RenameColumn(
                name: "SalesTaxRate",
                table: "TransactionItem",
                newName: "ExclTax");

            migrationBuilder.RenameColumn(
                name: "TotalServiceTaxAmount",
                table: "Transaction",
                newName: "TotalTaxAmount");

            migrationBuilder.RenameColumn(
                name: "TotalSalesTaxAmount",
                table: "Transaction",
                newName: "TotalInclTax");

            migrationBuilder.RenameColumn(
                name: "TotalInclTaxAmount",
                table: "Transaction",
                newName: "TotalExclTax");

            migrationBuilder.AddColumn<int>(
                name: "TotalSalesTaxNo",
                table: "Transaction",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "TotalServiceTaxNo",
                table: "Transaction",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.UpdateData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("69ed35dc-8775-4641-9f1d-26d9d91e5f4b"),
                column: "CreatedAt",
                value: new DateTime(2025, 3, 30, 7, 40, 57, 889, DateTimeKind.Utc).AddTicks(2072));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("11fd5e73-51fa-428a-a919-960ee860b00f"),
                column: "CreatedAt",
                value: new DateTime(2025, 3, 30, 7, 40, 57, 889, DateTimeKind.Utc).AddTicks(2166));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("5d22874b-5834-40a2-81be-bf5100eb845d"),
                column: "CreatedAt",
                value: new DateTime(2025, 3, 30, 7, 40, 57, 889, DateTimeKind.Utc).AddTicks(2161));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("c219bf33-28d0-4595-a25b-8018bd87e67a"),
                column: "CreatedAt",
                value: new DateTime(2025, 3, 30, 7, 40, 57, 889, DateTimeKind.Utc).AddTicks(2159));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("e57e65d0-85a9-49c8-8463-3424a42e1f01"),
                column: "CreatedAt",
                value: new DateTime(2025, 3, 30, 7, 40, 57, 889, DateTimeKind.Utc).AddTicks(2163));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"),
                column: "CreatedAt",
                value: new DateTime(2025, 3, 30, 7, 40, 57, 889, DateTimeKind.Utc).AddTicks(2122));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("81ba4893-d891-4e3c-a5e7-ba13c697bf97"),
                column: "CreatedAt",
                value: new DateTime(2025, 3, 30, 7, 40, 57, 889, DateTimeKind.Utc).AddTicks(2128));
        }
    }
}
