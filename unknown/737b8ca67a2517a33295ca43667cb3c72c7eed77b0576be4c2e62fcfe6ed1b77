﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace Dolfin.Framework.Data.Migrations
{
    /// <inheritdoc />
    public partial class userrole : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AspNetUserRoles_AspNetRoles_RoleId1",
                table: "AspNetUserRoles");

            migrationBuilder.DropIndex(
                name: "IX_AspNetUserRoles_RoleId1",
                table: "AspNetUserRoles");

            migrationBuilder.DeleteData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("eb5eb0a6-12f1-4c1e-8dc6-e4701e67175f"));

            migrationBuilder.DeleteData(
                table: "Settings",
                keyColumn: "Id",
                keyValue: new Guid("db856745-7a43-480d-af8a-48f5cfc04af9"));

            migrationBuilder.DeleteData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("f2eb9858-3b42-4f9f-9560-a4f85aa2fe85"));

            migrationBuilder.DeleteData(
                table: "TaxRate",
                keyColumn: "Id",
                keyValue: new Guid("5e227566-99a8-4718-bb89-36c3ab79d7f1"));

            migrationBuilder.DeleteData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("c31a1012-4e78-4033-85a7-f14c58d4787a"));

            migrationBuilder.DropColumn(
                name: "RoleId1",
                table: "AspNetUserRoles");

            migrationBuilder.InsertData(
                table: "Currency",
                columns: new[] { "Id", "Code", "CreatedAt", "CreatedBy", "ExchangeRate", "IsActive", "Name", "Precision", "Symbol", "UpdatedAt", "UpdatedBy" },
                values: new object[] { new Guid("69ed35dc-8775-4641-9f1d-26d9d91e5f4b"), "MYR", new DateTime(2024, 10, 13, 16, 8, 28, 958, DateTimeKind.Utc).AddTicks(5672), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), 1.0m, true, "Malaysia Ringgit", 2, "RM", null, null });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("aef630d3-6f0b-4c1f-a17b-c60f8f0408ea"),
                column: "CreatedAt",
                value: new DateTime(2024, 10, 13, 16, 8, 28, 958, DateTimeKind.Utc).AddTicks(4342));

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("b3ec5d2b-ca0d-45bc-ba86-bc965852922c"),
                column: "CreatedAt",
                value: new DateTime(2024, 10, 13, 16, 8, 28, 958, DateTimeKind.Utc).AddTicks(5142));

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("c0a87fee-d2a5-4c7e-beee-e60c7936f056"),
                column: "CreatedAt",
                value: new DateTime(2024, 10, 13, 16, 8, 28, 958, DateTimeKind.Utc).AddTicks(4760));

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("dbd5a6a5-0995-4ab6-822f-559ec36d222e"),
                column: "CreatedAt",
                value: new DateTime(2024, 10, 13, 16, 8, 28, 958, DateTimeKind.Utc).AddTicks(5577));

            migrationBuilder.InsertData(
                table: "Settings",
                columns: new[] { "Id", "Code", "CreatedBy", "Description", "Name", "Type", "UpdatedAt", "UpdatedBy", "Value" },
                values: new object[] { new Guid("929c9070-55d2-4b9d-832d-84f6e8bda3cd"), "SYSTEM_NAME", new Guid("00000000-0000-0000-0000-000000000000"), "Name of the system", "System name", "SYSTEM", null, null, "Dolfin Solutions" });

            migrationBuilder.InsertData(
                table: "TaxCategories",
                columns: new[] { "Id", "Code", "CreatedAt", "CreatedBy", "Description", "IsActive", "Name", "UpdatedAt", "UpdatedBy" },
                values: new object[,]
                {
                    { new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"), "SALESTAX", new DateTime(2024, 10, 13, 16, 8, 28, 958, DateTimeKind.Utc).AddTicks(5747), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), null, true, "Sales Tax", null, null },
                    { new Guid("81ba4893-d891-4e3c-a5e7-ba13c697bf97"), "SERVICETAX", new DateTime(2024, 10, 13, 16, 8, 28, 958, DateTimeKind.Utc).AddTicks(5755), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), null, true, "Service Tax", null, null }
                });

            migrationBuilder.InsertData(
                table: "TaxRate",
                columns: new[] { "Id", "ChargePercentage", "Code", "CreatedAt", "CreatedBy", "Description", "IsActive", "Name", "TaxCategoryId", "UpdatedAt", "UpdatedBy" },
                values: new object[] { new Guid("59bb4a30-7cae-47ab-800d-a117e0134ac5"), 0, "SR", new DateTime(2024, 10, 13, 16, 8, 28, 958, DateTimeKind.Utc).AddTicks(5799), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), null, true, "Sales Rate", new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"), null, null });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("69ed35dc-8775-4641-9f1d-26d9d91e5f4b"));

            migrationBuilder.DeleteData(
                table: "Settings",
                keyColumn: "Id",
                keyValue: new Guid("929c9070-55d2-4b9d-832d-84f6e8bda3cd"));

            migrationBuilder.DeleteData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("81ba4893-d891-4e3c-a5e7-ba13c697bf97"));

            migrationBuilder.DeleteData(
                table: "TaxRate",
                keyColumn: "Id",
                keyValue: new Guid("59bb4a30-7cae-47ab-800d-a117e0134ac5"));

            migrationBuilder.DeleteData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"));

            migrationBuilder.AddColumn<string>(
                name: "RoleId1",
                table: "AspNetUserRoles",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.InsertData(
                table: "Currency",
                columns: new[] { "Id", "Code", "CreatedAt", "CreatedBy", "ExchangeRate", "IsActive", "Name", "Precision", "Symbol", "UpdatedAt", "UpdatedBy" },
                values: new object[] { new Guid("eb5eb0a6-12f1-4c1e-8dc6-e4701e67175f"), "MYR", new DateTime(2024, 10, 3, 17, 20, 40, 78, DateTimeKind.Utc).AddTicks(2990), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), 1.0m, true, "Malaysia Ringgit", 2, "RM", null, null });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("aef630d3-6f0b-4c1f-a17b-c60f8f0408ea"),
                column: "CreatedAt",
                value: new DateTime(2024, 10, 3, 17, 20, 40, 78, DateTimeKind.Utc).AddTicks(2339));

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("b3ec5d2b-ca0d-45bc-ba86-bc965852922c"),
                column: "CreatedAt",
                value: new DateTime(2024, 10, 3, 17, 20, 40, 78, DateTimeKind.Utc).AddTicks(2728));

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("c0a87fee-d2a5-4c7e-beee-e60c7936f056"),
                column: "CreatedAt",
                value: new DateTime(2024, 10, 3, 17, 20, 40, 78, DateTimeKind.Utc).AddTicks(2573));

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("dbd5a6a5-0995-4ab6-822f-559ec36d222e"),
                column: "CreatedAt",
                value: new DateTime(2024, 10, 3, 17, 20, 40, 78, DateTimeKind.Utc).AddTicks(2882));

            migrationBuilder.InsertData(
                table: "Settings",
                columns: new[] { "Id", "Code", "CreatedBy", "Description", "Name", "Type", "UpdatedAt", "UpdatedBy", "Value" },
                values: new object[] { new Guid("db856745-7a43-480d-af8a-48f5cfc04af9"), "SYSTEM_NAME", new Guid("00000000-0000-0000-0000-000000000000"), "Name of the system", "System name", "SYSTEM", null, null, "Dolfin Solutions" });

            migrationBuilder.InsertData(
                table: "TaxCategories",
                columns: new[] { "Id", "Code", "CreatedAt", "CreatedBy", "Description", "IsActive", "Name", "UpdatedAt", "UpdatedBy" },
                values: new object[,]
                {
                    { new Guid("c31a1012-4e78-4033-85a7-f14c58d4787a"), "SALESTAX", new DateTime(2024, 10, 3, 17, 20, 40, 78, DateTimeKind.Utc).AddTicks(3065), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), null, true, "Sales Tax", null, null },
                    { new Guid("f2eb9858-3b42-4f9f-9560-a4f85aa2fe85"), "SERVICETAX", new DateTime(2024, 10, 3, 17, 20, 40, 78, DateTimeKind.Utc).AddTicks(3075), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), null, true, "Service Tax", null, null }
                });

            migrationBuilder.InsertData(
                table: "TaxRate",
                columns: new[] { "Id", "ChargePercentage", "Code", "CreatedAt", "CreatedBy", "Description", "IsActive", "Name", "TaxCategoryId", "UpdatedAt", "UpdatedBy" },
                values: new object[] { new Guid("5e227566-99a8-4718-bb89-36c3ab79d7f1"), 0, "SR", new DateTime(2024, 10, 3, 17, 20, 40, 78, DateTimeKind.Utc).AddTicks(3168), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), null, true, "Sales Rate", new Guid("c31a1012-4e78-4033-85a7-f14c58d4787a"), null, null });

            migrationBuilder.CreateIndex(
                name: "IX_AspNetUserRoles_RoleId1",
                table: "AspNetUserRoles",
                column: "RoleId1");

            migrationBuilder.AddForeignKey(
                name: "FK_AspNetUserRoles_AspNetRoles_RoleId1",
                table: "AspNetUserRoles",
                column: "RoleId1",
                principalTable: "AspNetRoles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
