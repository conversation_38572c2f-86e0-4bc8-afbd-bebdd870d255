﻿<Project Sdk="Microsoft.NET.Sdk.Razor">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AddRazorSupportForMvc>true</AddRazorSupportForMvc>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <EmbeddedResource Remove="SQL\Default Value\InsertDefaultProduct.psq;" />
  </ItemGroup>

  <ItemGroup>
    <Compile Include="SQL\Default Value\InsertDefaultProduct.psql" />
  </ItemGroup>

  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Aspire.Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.1" />
    <PackageReference Include="AWSSDK.Core" Version="4.0.0.3" />
    <PackageReference Include="AWSSDK.S3" Version="4.0.0.3" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.7" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.7" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.7">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.6">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.4" />
    <PackageReference Include="OrchardCore.Module.Targets" Version="1.8.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\Dolfin.Util\Dolfin.Utility.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Domains\Extensions\" />
  </ItemGroup>

</Project>
