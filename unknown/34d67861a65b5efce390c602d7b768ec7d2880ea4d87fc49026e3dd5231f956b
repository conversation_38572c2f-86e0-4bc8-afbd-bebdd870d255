﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace Dolfin.Framework.Data.Migrations
{
    /// <inheritdoc />
    public partial class RemoveUserRoleTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Transaction_User_UserId1",
                table: "Transaction");

            migrationBuilder.DropTable(
                name: "RolePermission");

            migrationBuilder.DropTable(
                name: "UserRole");

            migrationBuilder.DropTable(
                name: "Role");

            migrationBuilder.DropTable(
                name: "User");

            migrationBuilder.DropIndex(
                name: "IX_Transaction_UserId1",
                table: "Transaction");

            migrationBuilder.DropColumn(
                name: "UserId1",
                table: "Transaction");

            migrationBuilder.InsertData(
                table: "AspNetRoles",
                columns: new[] { "Id", "ConcurrencyStamp", "CreatedAt", "CreatedBy", "Editable", "IsActive", "Name", "NormalizedName", "PartailEditable", "TargetType", "UpdatedAt", "UpdatedBy" },
                values: new object[,]
                {
                    { "aef630d3-6f0b-4c1f-a17b-c60f8f0408ea", null, new DateTime(2025, 5, 25, 3, 31, 4, 129, DateTimeKind.Utc).AddTicks(9192), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), true, true, "Administrator", "ADMINISTRATOR", false, "APP", null, null },
                    { "b9c5d6a7-8e9f-4b0c-a1d2-e3f4g5h6i7j8", null, new DateTime(2025, 5, 25, 3, 31, 4, 129, DateTimeKind.Utc).AddTicks(9197), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), true, true, "Super User", "SUPER USER", false, "ADMIN", null, null },
                    { "c1d2e3f4-g5h6-i7j8-k9l0-m1n2o3p4q5r6", null, new DateTime(2025, 5, 25, 3, 31, 4, 129, DateTimeKind.Utc).AddTicks(9201), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), true, true, "Super Admin", "SUPER ADMIN", false, "SYSADMIN", null, null },
                    { "d4e5f6g7-h8i9-j0k1-l2m3-n4o5p6q7r8s9", null, new DateTime(2025, 5, 25, 3, 31, 4, 129, DateTimeKind.Utc).AddTicks(9204), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), false, true, "User", "USER", false, "USER", null, null }
                });

            migrationBuilder.InsertData(
                table: "AspNetUsers",
                columns: new[] { "Id", "AccessFailedCount", "BranchId", "CompanyId", "ConcurrencyStamp", "CreatedAt", "CreatedBy", "Email", "EmailConfirmed", "FaxNo1", "FaxNo2", "IsActive", "IsAllowEditable", "LastLoginAt", "LockoutEnabled", "LockoutEnd", "NormalizedEmail", "NormalizedUserName", "PasswordExpireAt", "PasswordHash", "PhoneNo1", "PhoneNo2", "PhoneNumber", "PhoneNumberConfirmed", "SecurityStamp", "SerialNo", "TwoFactorEnabled", "UpdatedAt", "UpdatedBy", "UserName" },
                values: new object[] { "7f3b2a5a-b8cd-4e08-a1b7-11b9e4b8c7a5", 0, null, null, "db135fdf-47bf-47bb-ba9e-67f8db6367f3", new DateTime(2025, 5, 25, 3, 31, 4, 129, DateTimeKind.Utc).AddTicks(9458), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), "<EMAIL>", true, null, null, true, false, new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), false, null, "<EMAIL>", "DOLFINADMIN", new DateTime(2035, 5, 25, 3, 31, 4, 129, DateTimeKind.Utc).AddTicks(9439), null, "01116200503", null, "01116200503", true, "a51a19a7-6c7b-4eb6-b1ce-d48e1b032d14", null, false, null, null, "dolfinadmin" });

            migrationBuilder.InsertData(
                table: "AspNetUserRoles",
                columns: new[] { "RoleId", "UserId" },
                values: new object[] { "aef630d3-6f0b-4c1f-a17b-c60f8f0408ea", "7f3b2a5a-b8cd-4e08-a1b7-11b9e4b8c7a5" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "AspNetRoles",
                keyColumn: "Id",
                keyValue: "b9c5d6a7-8e9f-4b0c-a1d2-e3f4g5h6i7j8");

            migrationBuilder.DeleteData(
                table: "AspNetRoles",
                keyColumn: "Id",
                keyValue: "c1d2e3f4-g5h6-i7j8-k9l0-m1n2o3p4q5r6");

            migrationBuilder.DeleteData(
                table: "AspNetRoles",
                keyColumn: "Id",
                keyValue: "d4e5f6g7-h8i9-j0k1-l2m3-n4o5p6q7r8s9");

            migrationBuilder.DeleteData(
                table: "AspNetUserRoles",
                keyColumns: new[] { "RoleId", "UserId" },
                keyValues: new object[] { "aef630d3-6f0b-4c1f-a17b-c60f8f0408ea", "7f3b2a5a-b8cd-4e08-a1b7-11b9e4b8c7a5" });

            migrationBuilder.DeleteData(
                table: "AspNetRoles",
                keyColumn: "Id",
                keyValue: "aef630d3-6f0b-4c1f-a17b-c60f8f0408ea");

            migrationBuilder.DeleteData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "7f3b2a5a-b8cd-4e08-a1b7-11b9e4b8c7a5");

            migrationBuilder.AddColumn<Guid>(
                name: "UserId1",
                table: "Transaction",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "Role",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Code = table.Column<string>(type: "text", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "now()"),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    Editable = table.Column<bool>(type: "boolean", nullable: false, defaultValueSql: "false"),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValueSql: "true"),
                    Name = table.Column<string>(type: "text", nullable: false),
                    PartailEditable = table.Column<bool>(type: "boolean", nullable: false),
                    TargetType = table.Column<string>(type: "text", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Role", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "User",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    BranchId = table.Column<Guid>(type: "uuid", nullable: true),
                    CompanyId = table.Column<Guid>(type: "uuid", nullable: true),
                    AccessFailedCount = table.Column<int>(type: "integer", nullable: false),
                    Code = table.Column<string>(type: "text", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "now()"),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    Email = table.Column<string>(type: "text", nullable: false),
                    FaxNo1 = table.Column<string>(type: "text", nullable: true),
                    FaxNo2 = table.Column<string>(type: "text", nullable: true),
                    FullName = table.Column<string>(type: "text", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValueSql: "true"),
                    LastLoginAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Locked = table.Column<bool>(type: "boolean", nullable: false, defaultValueSql: "false"),
                    LockedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LockedEnd = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Password = table.Column<string>(type: "text", nullable: false),
                    PasswordExpireAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    PasswordSalt = table.Column<string>(type: "text", nullable: false),
                    PhoneNo1 = table.Column<string>(type: "text", nullable: false),
                    PhoneNo2 = table.Column<string>(type: "text", nullable: true),
                    SerialNo = table.Column<string>(type: "text", nullable: true),
                    TempPassword = table.Column<string>(type: "text", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    Username = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_User", x => x.Id);
                    table.ForeignKey(
                        name: "FK_User_Branch_BranchId",
                        column: x => x.BranchId,
                        principalTable: "Branch",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_User_Company_CompanyId",
                        column: x => x.CompanyId,
                        principalTable: "Company",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "RolePermission",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    RoleId = table.Column<Guid>(type: "uuid", nullable: false),
                    Code = table.Column<string>(type: "text", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "now()"),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValueSql: "true"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RolePermission", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RolePermission_Role",
                        column: x => x.RoleId,
                        principalTable: "Role",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "UserRole",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    RoleId = table.Column<Guid>(type: "uuid", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "now()"),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValueSql: "true"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserRole", x => x.Id);
                    table.ForeignKey(
                        name: "FK_UserRole_Role",
                        column: x => x.RoleId,
                        principalTable: "Role",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_UserRole_User",
                        column: x => x.UserId,
                        principalTable: "User",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Transaction_UserId1",
                table: "Transaction",
                column: "UserId1");

            migrationBuilder.CreateIndex(
                name: "IX_RolePermission_RoleId",
                table: "RolePermission",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "IX_User_BranchId",
                table: "User",
                column: "BranchId");

            migrationBuilder.CreateIndex(
                name: "IX_User_CompanyId",
                table: "User",
                column: "CompanyId");

            migrationBuilder.CreateIndex(
                name: "IX_UserRole_RoleId",
                table: "UserRole",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "IX_UserRole_UserId",
                table: "UserRole",
                column: "UserId");

            migrationBuilder.AddForeignKey(
                name: "FK_Transaction_User_UserId1",
                table: "Transaction",
                column: "UserId1",
                principalTable: "User",
                principalColumn: "Id");
        }
    }
}
