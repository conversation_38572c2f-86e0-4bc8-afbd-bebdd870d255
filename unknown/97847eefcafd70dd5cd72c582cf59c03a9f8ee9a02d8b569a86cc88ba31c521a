# Dolfin Mobile API Documentation

## Overview

The Dolfin Mobile API is a RESTful API that provides access to the Dolfin Mobile application's backend services. This API allows clients to manage users, companies, products, customers, transactions, and more.

## Base URL

```
https://[your-domain]/api
```

For local development:
```
https://localhost:7127/api
```

## Authentication

The API uses JWT (JSON Web Token) authentication. To access protected endpoints, you need to include the JWT token in the Authorization header of your requests.

```
Authorization: Bearer {your_token}
```

### Getting a Token

To get a token, you need to authenticate using the `/auth/login` endpoint.

## Common Response Format

All API responses follow a standard format:

```json
{
  "isSuccessful": true,
  "statusCode": 200,
  "statusMessage": "Success",
  "result": { ... },
  "exception": null
}
```

- `isSuccessful`: Indicates whether the request was successful
- `statusCode`: HTTP status code
- `statusMessage`: A human-readable message describing the result
- `result`: The actual data returned by the API (varies by endpoint)
- `exception`: Error details (only in development mode)

## Pagination

For endpoints that return lists of items, pagination is supported using the following query parameters:

- `pageNumber`: The page number to retrieve (default: 1)
- `pageSize`: The number of items per page
- `sortBy`: The field to sort by
- `sortType`: The sort direction (ASC or DESC, default: DESC)

Pagination metadata is returned in the `X-Pagination` header with the following format:

```json
{
  "totalCount": 100,
  "pageSize": 10,
  "currentPage": 1,
  "totalPages": 10,
  "hasNext": true,
  "hasPrevious": false
}
```

## Filtering

For endpoints that support filtering, you can use the `filterList` parameter to filter results:

```
?filterList[0].filterName=Name&filterList[0].filterValue=Test
```

## API Endpoints

### Authentication

#### Register a new user

```
POST /auth/register
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "Password123!",
  "confirmPassword": "Password123!",
  "username": "johndoe",
  "fullName": "John Doe",
  "phoneNo1": "1234567890",
  "phoneNo2": "0987654321",
  "faxNo1": "1234567",
  "faxNo2": "7654321",
  "serialNo": "ABC123",
  "branchId": "********-0000-0000-0000-********0000",
  "companyId": "********-0000-0000-0000-********0000",
  "userTypeId": "********-0000-0000-0000-********0000"
}
```

**Response:**
```
Registration successful. Please check your email for confirmation.
```

#### Login

```
POST /auth/login
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "Password123!"
}
```

**Response:**
```json
{
  "isSuccessful": true,
  "statusCode": 200,
  "statusMessage": "Success",
  "result": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "********-0000-0000-0000-********0000",
      "email": "<EMAIL>",
      "username": "johndoe"
    }
  },
  "exception": null
}
```

#### Logout

```
POST /auth/logout
```

**Response:**
```
Logout successful.
```

#### Confirm Email

```
GET /auth/confirm-email?userId={userId}&code={code}
```

**Response:**
```json
{
  "isSuccessful": true,
  "statusCode": 200,
  "statusMessage": "Email confirmed successfully",
  "result": true,
  "exception": null
}
```

#### Resend Confirmation Email

```
POST /auth/resend-confirmation-email?userId={userId}
```

**Response:**
```json
{
  "isSuccessful": true,
  "statusCode": 200,
  "statusMessage": "Confirmation email sent",
  "result": true,
  "exception": null
}
```

#### Forgot Password

```
POST /auth/forgot-password
```

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Response:**
```
Password reset link sent.
```

#### Reset Password

```
POST /auth/reset-password
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "token": "reset-token",
  "newPassword": "NewPassword123!",
  "confirmPassword": "NewPassword123!"
}
```

**Response:**
```
Password reset successful.
```

#### Change Password

```
POST /auth/change-password
```

**Request Body:**
```json
{
  "currentPassword": "Password123!",
  "newPassword": "NewPassword123!",
  "confirmNewPassword": "NewPassword123!"
}
```

**Response:**
```
Password changed successfully.
```

#### Refresh Token

```
POST /auth/refresh-token
```

**Request Body:**
```json
{
  "token": "refresh-token-string"
}
```

**Response:**
```json
{
  "isSuccessful": true,
  "statusCode": 200,
  "statusMessage": "Success",
  "result": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "new-refresh-token-string",
    "expiresIn": 3600,
    "user": {
      "id": "********-0000-0000-0000-********0000",
      "email": "<EMAIL>",
      "username": "johndoe"
    },
    "displayMessage": "Token refreshed successfully"
  },
  "exception": null
}
```

#### Revoke Token

```
POST /auth/revoke-token
```

**Request Body:**
```json
{
  "token": "refresh-token-string",
  "reason": "User logged out" // Optional
}
```

**Response:**
```json
{
  "isSuccessful": true,
  "statusCode": 200,
  "statusMessage": "Success",
  "result": {
    "displayMessage": "Token revoked successfully"
  },
  "exception": null
}
```

### User Management

#### Get User Profile

```
GET /user/profile?userId={userId}
```

**Response:**
```json
{
  "isSuccessful": true,
  "statusCode": 200,
  "statusMessage": "Success",
  "result": {
    "id": "********-0000-0000-0000-********0000",
    "email": "<EMAIL>",
    "username": "johndoe",
    "fullName": "John Doe",
    "phoneNo1": "1234567890"
  },
  "exception": null
}
```

#### Delete User

```
POST /user/delete?userId={userId}
```

**Response:**
```json
{
  "isSuccessful": true,
  "statusCode": 200,
  "statusMessage": "User deleted successfully",
  "result": null,
  "exception": null
}
```

### Company Management

#### Get Company Profile

```
GET /company/profile?companyId={companyId}
```

**Response:**
```json
{
  "isSuccessful": true,
  "statusCode": 200,
  "statusMessage": "Success",
  "result": {
    "id": "********-0000-0000-0000-********0000",
    "code": "COMP001",
    "name": "Example Company",
    "regNo": "REG12345",
    "sstNo": "SST12345",
    "tinNo": "TIN12345",
    "isEnableInventory": true
  },
  "exception": null
}
```

#### Get Company by User ID

```
GET /company/userid?userId={userId}
```

**Response:**
```json
{
  "isSuccessful": true,
  "statusCode": 200,
  "statusMessage": "Success",
  "result": {
    "id": "********-0000-0000-0000-********0000",
    "code": "COMP001",
    "name": "Example Company",
    "regNo": "REG12345"
  },
  "exception": null
}
```

### Product Management

#### Get Product List

```
GET /product?pageNumber=1&pageSize=10&sortBy=Name&sortType=ASC
```

**Response:**
```json
{
  "isSuccessful": true,
  "statusCode": 200,
  "statusMessage": "Success",
  "result": [
    {
      "id": "********-0000-0000-0000-********0000",
      "code": "PROD001",
      "name": "Product 1",
      "description": "Description of Product 1",
      "price": 100.00
    },
    {
      "id": "********-0000-0000-0000-********0001",
      "code": "PROD002",
      "name": "Product 2",
      "description": "Description of Product 2",
      "price": 200.00
    }
  ],
  "exception": null
}
```

#### Get Product by ID

```
GET /product/get/{productId}
```

**Response:**
```json
{
  "isSuccessful": true,
  "statusCode": 200,
  "statusMessage": "Success",
  "result": {
    "id": "********-0000-0000-0000-********0000",
    "code": "PROD001",
    "name": "Product 1",
    "description": "Description of Product 1",
    "price": 100.00
  },
  "exception": null
}
```

#### Create Product

```
POST /product/create
```

**Request Body:**
```json
{
  "code": "PROD003",
  "name": "Product 3",
  "description": "Description of Product 3",
  "price": 300.00,
  "productCategoryId": "********-0000-0000-0000-********0000",
  "companyId": "********-0000-0000-0000-********0000"
}
```

**Response:**
```json
{
  "isSuccessful": true,
  "statusCode": 200,
  "statusMessage": "Product created successfully",
  "result": {
    "id": "********-0000-0000-0000-********0002"
  },
  "exception": null
}
```

#### Update Product

```
POST /product/update
```

**Request Body:**
```json
{
  "id": "********-0000-0000-0000-********0000",
  "name": "Updated Product 1",
  "description": "Updated description of Product 1",
  "price": 150.00
}
```

**Response:**
```json
{
  "isSuccessful": true,
  "statusCode": 200,
  "statusMessage": "Product updated successfully",
  "result": {
    "id": "********-0000-0000-0000-********0000"
  },
  "exception": null
}
```

#### Delete Product

```
POST /product/delete?productId={productId}
```

**Response:**
```json
{
  "isSuccessful": true,
  "statusCode": 200,
  "statusMessage": "Product deleted successfully",
  "result": null,
  "exception": null
}
```

### Customer Management

#### Get Customer List

```
GET /customer?pageNumber=1&pageSize=10&sortBy=Name&sortType=ASC
```

**Response:**
```json
{
  "isSuccessful": true,
  "statusCode": 200,
  "statusMessage": "Success",
  "result": [
    {
      "id": "********-0000-0000-0000-********0000",
      "code": "CUST001",
      "name": "Customer 1",
      "description": "Description of Customer 1",
      "email": "<EMAIL>"
    },
    {
      "id": "********-0000-0000-0000-********0001",
      "code": "CUST002",
      "name": "Customer 2",
      "description": "Description of Customer 2",
      "email": "<EMAIL>"
    }
  ],
  "exception": null
}
```

#### Get Customer by ID

```
GET /customer/get/{customerId}
```

**Response:**
```json
{
  "isSuccessful": true,
  "statusCode": 200,
  "statusMessage": "Success",
  "result": {
    "id": "********-0000-0000-0000-********0000",
    "code": "CUST001",
    "name": "Customer 1",
    "description": "Description of Customer 1",
    "email": "<EMAIL>"
  },
  "exception": null
}
```

#### Create Customer

```
POST /customer/create
```

**Request Body:**
```json
{
  "code": "CUST003",
  "name": "Customer 3",
  "description": "Description of Customer 3",
  "isTaxExempt": false,
  "isPICEditable": true,
  "defaultPIC": "John Doe",
  "referralCode": "REF123",
  "accountCode": "ACC123",
  "remark": "New customer",
  "identityTypeId": "********-0000-0000-0000-********0000",
  "identityNo": "ID12345",
  "fullName": "Customer Three",
  "email": "<EMAIL>",
  "tinNo": "TIN12345",
  "tinVerifyStatus": 0
}
```

**Response:**
```json
{
  "isSuccessful": true,
  "statusCode": 200,
  "statusMessage": "Customer created successfully",
  "result": {
    "id": "********-0000-0000-0000-********0002"
  },
  "exception": null
}
```

#### Update Customer

```
POST /customer/update
```

**Request Body:**
```json
{
  "id": "********-0000-0000-0000-********0000",
  "name": "Updated Customer 1",
  "description": "Updated description of Customer 1",
  "isTaxExempt": true,
  "isPICEditable": true,
  "defaultPIC": "Jane Doe",
  "referralCode": "REF123",
  "accountCode": "ACC123",
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "isSuccessful": true,
  "statusCode": 200,
  "statusMessage": "Customer updated successfully",
  "result": {
    "id": "********-0000-0000-0000-********0000"
  },
  "exception": null
}
```

#### Delete Customer

```
POST /customer/delete?customerId={customerId}
```

**Response:**
```json
{
  "isSuccessful": true,
  "statusCode": 200,
  "statusMessage": "Customer deleted successfully",
  "result": null,
  "exception": null
}
```

### E-Invoice Management

#### Login to Taxpayer System

```
GET /einvoice/logintaxpayersystem
```

**Query Parameters:**
- `clientId` (optional): Client ID for the tax system
- `clientSecret` (optional): Client secret for the tax system

**Response:**
```json
{
  "isSuccessful": true,
  "statusCode": 200,
  "statusMessage": "Success",
  "result": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "Bearer",
    "expires_in": 3600
  },
  "exception": null
}
```

## Error Handling

When an error occurs, the API returns a response with `isSuccessful` set to `false` and additional error information:

```json
{
  "isSuccessful": false,
  "statusCode": 400,
  "statusMessage": "Invalid request",
  "result": null,
  "exception": "Error details (only in development mode)"
}
```

Common HTTP status codes:

- `200 OK`: The request was successful
- `400 Bad Request`: The request was invalid
- `401 Unauthorized`: Authentication is required
- `403 Forbidden`: The user does not have permission to access the resource
- `404 Not Found`: The requested resource was not found
- `500 Internal Server Error`: An error occurred on the server

## Development and Testing

The API includes Swagger documentation, which can be accessed at:

```
https://localhost:7127/swagger
```

This provides an interactive interface for exploring and testing the API endpoints.

## Rate Limiting

The API implements rate limiting to prevent abuse. If you exceed the rate limit, you will receive a `429 Too Many Requests` response.

## Support

For support or questions about the API, please contact the Dolfin support <NAME_EMAIL>.
