﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Dolfin.Framework.Data.Migrations
{
    /// <inheritdoc />
    public partial class TaxControl : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Company_TaxRate_SalesTaxNo",
                table: "Company");

            migrationBuilder.DropForeignKey(
                name: "FK_Company_TaxRate_ServiceTaxNo",
                table: "Company");

            migrationBuilder.DropForeignKey(
                name: "FK_Transaction_TaxRate_SalesTaxNo",
                table: "Transaction");

            migrationBuilder.DropForeignKey(
                name: "FK_Transaction_TaxRate_ServiceTaxNo",
                table: "Transaction");

            migrationBuilder.DropForeignKey(
                name: "FK_TransactionItem_TaxRate_SalesTaxNo",
                table: "TransactionItem");

            migrationBuilder.DropForeignKey(
                name: "FK_TransactionItem_TaxRate_ServiceTaxNo",
                table: "TransactionItem");

            migrationBuilder.AlterColumn<Guid>(
                name: "ServiceTaxNoId",
                table: "TransactionItem",
                type: "uuid",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AlterColumn<Guid>(
                name: "SalesTaxNoId",
                table: "TransactionItem",
                type: "uuid",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AlterColumn<Guid>(
                name: "ServiceTaxNoId",
                table: "Transaction",
                type: "uuid",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AlterColumn<Guid>(
                name: "SalesTaxNoId",
                table: "Transaction",
                type: "uuid",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AlterColumn<Guid>(
                name: "DefaultServiceTaxNoId",
                table: "Company",
                type: "uuid",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AlterColumn<Guid>(
                name: "DefaultSalesTaxNoId",
                table: "Company",
                type: "uuid",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.UpdateData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("69ed35dc-8775-4641-9f1d-26d9d91e5f4b"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 22, 17, 31, 30, 481, DateTimeKind.Utc).AddTicks(793));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("11fd5e73-51fa-428a-a919-960ee860b00f"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 22, 17, 31, 30, 481, DateTimeKind.Utc).AddTicks(880));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("5d22874b-5834-40a2-81be-bf5100eb845d"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 22, 17, 31, 30, 481, DateTimeKind.Utc).AddTicks(877));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("c219bf33-28d0-4595-a25b-8018bd87e67a"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 22, 17, 31, 30, 481, DateTimeKind.Utc).AddTicks(875));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("e57e65d0-85a9-49c8-8463-3424a42e1f01"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 22, 17, 31, 30, 481, DateTimeKind.Utc).AddTicks(879));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 22, 17, 31, 30, 481, DateTimeKind.Utc).AddTicks(838));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("81ba4893-d891-4e3c-a5e7-ba13c697bf97"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 22, 17, 31, 30, 481, DateTimeKind.Utc).AddTicks(843));

            migrationBuilder.AddForeignKey(
                name: "FK_Company_TaxRate_SalesTaxNo",
                table: "Company",
                column: "DefaultSalesTaxNoId",
                principalTable: "TaxRate",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Company_TaxRate_ServiceTaxNo",
                table: "Company",
                column: "DefaultServiceTaxNoId",
                principalTable: "TaxRate",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Transaction_TaxRate_SalesTaxNo",
                table: "Transaction",
                column: "SalesTaxNoId",
                principalTable: "TaxRate",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Transaction_TaxRate_ServiceTaxNo",
                table: "Transaction",
                column: "ServiceTaxNoId",
                principalTable: "TaxRate",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_TransactionItem_TaxRate_SalesTaxNo",
                table: "TransactionItem",
                column: "SalesTaxNoId",
                principalTable: "TaxRate",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_TransactionItem_TaxRate_ServiceTaxNo",
                table: "TransactionItem",
                column: "ServiceTaxNoId",
                principalTable: "TaxRate",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Company_TaxRate_SalesTaxNo",
                table: "Company");

            migrationBuilder.DropForeignKey(
                name: "FK_Company_TaxRate_ServiceTaxNo",
                table: "Company");

            migrationBuilder.DropForeignKey(
                name: "FK_Transaction_TaxRate_SalesTaxNo",
                table: "Transaction");

            migrationBuilder.DropForeignKey(
                name: "FK_Transaction_TaxRate_ServiceTaxNo",
                table: "Transaction");

            migrationBuilder.DropForeignKey(
                name: "FK_TransactionItem_TaxRate_SalesTaxNo",
                table: "TransactionItem");

            migrationBuilder.DropForeignKey(
                name: "FK_TransactionItem_TaxRate_ServiceTaxNo",
                table: "TransactionItem");

            migrationBuilder.AlterColumn<Guid>(
                name: "ServiceTaxNoId",
                table: "TransactionItem",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uuid",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "SalesTaxNoId",
                table: "TransactionItem",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uuid",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "ServiceTaxNoId",
                table: "Transaction",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uuid",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "SalesTaxNoId",
                table: "Transaction",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uuid",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "DefaultServiceTaxNoId",
                table: "Company",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uuid",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "DefaultSalesTaxNoId",
                table: "Company",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uuid",
                oldNullable: true);

            migrationBuilder.UpdateData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("69ed35dc-8775-4641-9f1d-26d9d91e5f4b"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 22, 8, 25, 6, 76, DateTimeKind.Utc).AddTicks(1775));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("11fd5e73-51fa-428a-a919-960ee860b00f"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 22, 8, 25, 6, 76, DateTimeKind.Utc).AddTicks(1896));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("5d22874b-5834-40a2-81be-bf5100eb845d"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 22, 8, 25, 6, 76, DateTimeKind.Utc).AddTicks(1892));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("c219bf33-28d0-4595-a25b-8018bd87e67a"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 22, 8, 25, 6, 76, DateTimeKind.Utc).AddTicks(1864));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("e57e65d0-85a9-49c8-8463-3424a42e1f01"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 22, 8, 25, 6, 76, DateTimeKind.Utc).AddTicks(1894));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 22, 8, 25, 6, 76, DateTimeKind.Utc).AddTicks(1822));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("81ba4893-d891-4e3c-a5e7-ba13c697bf97"),
                column: "CreatedAt",
                value: new DateTime(2025, 5, 22, 8, 25, 6, 76, DateTimeKind.Utc).AddTicks(1827));

            migrationBuilder.AddForeignKey(
                name: "FK_Company_TaxRate_SalesTaxNo",
                table: "Company",
                column: "DefaultSalesTaxNoId",
                principalTable: "TaxRate",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Company_TaxRate_ServiceTaxNo",
                table: "Company",
                column: "DefaultServiceTaxNoId",
                principalTable: "TaxRate",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Transaction_TaxRate_SalesTaxNo",
                table: "Transaction",
                column: "SalesTaxNoId",
                principalTable: "TaxRate",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Transaction_TaxRate_ServiceTaxNo",
                table: "Transaction",
                column: "ServiceTaxNoId",
                principalTable: "TaxRate",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_TransactionItem_TaxRate_SalesTaxNo",
                table: "TransactionItem",
                column: "SalesTaxNoId",
                principalTable: "TaxRate",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_TransactionItem_TaxRate_ServiceTaxNo",
                table: "TransactionItem",
                column: "ServiceTaxNoId",
                principalTable: "TaxRate",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
