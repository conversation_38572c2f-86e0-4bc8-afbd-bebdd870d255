﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Dolfin.Framework.Data.Migrations
{
    /// <inheritdoc />
    public partial class enhanceUOMCategories : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_UOM_UOMCategoryId",
                table: "UOM");

            migrationBuilder.UpdateData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("69ed35dc-8775-4641-9f1d-26d9d91e5f4b"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 4, 15, 13, 44, 756, DateTimeKind.Utc).AddTicks(780));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("11fd5e73-51fa-428a-a919-960ee860b00f"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 4, 15, 13, 44, 756, DateTimeKind.Utc).AddTicks(871));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("5d22874b-5834-40a2-81be-bf5100eb845d"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 4, 15, 13, 44, 756, DateTimeKind.Utc).AddTicks(865));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("c219bf33-28d0-4595-a25b-8018bd87e67a"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 4, 15, 13, 44, 756, DateTimeKind.Utc).AddTicks(862));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("e57e65d0-85a9-49c8-8463-3424a42e1f01"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 4, 15, 13, 44, 756, DateTimeKind.Utc).AddTicks(869));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 4, 15, 13, 44, 756, DateTimeKind.Utc).AddTicks(831));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("81ba4893-d891-4e3c-a5e7-ba13c697bf97"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 4, 15, 13, 44, 756, DateTimeKind.Utc).AddTicks(837));

            migrationBuilder.CreateIndex(
                name: "IX_UOM_UOMCategoryId",
                table: "UOM",
                column: "UOMCategoryId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_UOM_UOMCategoryId",
                table: "UOM");

            migrationBuilder.UpdateData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("69ed35dc-8775-4641-9f1d-26d9d91e5f4b"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 4, 14, 42, 13, 369, DateTimeKind.Utc).AddTicks(2236));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("11fd5e73-51fa-428a-a919-960ee860b00f"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 4, 14, 42, 13, 369, DateTimeKind.Utc).AddTicks(2406));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("5d22874b-5834-40a2-81be-bf5100eb845d"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 4, 14, 42, 13, 369, DateTimeKind.Utc).AddTicks(2397));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("c219bf33-28d0-4595-a25b-8018bd87e67a"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 4, 14, 42, 13, 369, DateTimeKind.Utc).AddTicks(2333));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("e57e65d0-85a9-49c8-8463-3424a42e1f01"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 4, 14, 42, 13, 369, DateTimeKind.Utc).AddTicks(2404));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 4, 14, 42, 13, 369, DateTimeKind.Utc).AddTicks(2294));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("81ba4893-d891-4e3c-a5e7-ba13c697bf97"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 4, 14, 42, 13, 369, DateTimeKind.Utc).AddTicks(2300));

            migrationBuilder.CreateIndex(
                name: "IX_UOM_UOMCategoryId",
                table: "UOM",
                column: "UOMCategoryId",
                unique: true);
        }
    }
}
