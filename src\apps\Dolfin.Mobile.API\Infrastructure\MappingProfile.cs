﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using AutoMapper;
using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Domains.CustomIdentity;
using Dolfin.Mobile.API.Models;
using Dolfin.Mobile.API.Models.Dto;
using Dolfin.Mobile.API.Models.Request;
using Dolfin.Mobile.API.Models.Response;
using Dolfin.Utility.Models;

namespace Dolfin.Mobile.API.Infrastructure
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            ConfigureBaseResponseMappings();
            ConfigureRequestToEntityMappings();
            ConfigureEntityToDtoMappings();
        }

        private void ConfigureBaseResponseMappings()
        {
            #region Base Response Mappings
            CreateMap(typeof(BaseResponse<>), typeof(NoResultResponse))
                .ForMember(nameof(NoResultResponse.Result), opt => opt.MapFrom(_ => new NoResult()))
                .ForMember(nameof(NoResultResponse.IsSuccessful), opt => opt.MapFrom("IsSuccessful"))
                .ForMember(nameof(NoResultResponse.StatusCode), opt => opt.MapFrom("StatusCode"))
                .ForMember(nameof(NoResultResponse.StatusMessage), opt => opt.MapFrom("StatusMessage"))
                .ForMember(nameof(NoResultResponse.Exception), opt => opt.MapFrom("Exception"))
                .AfterMap((src, dest) =>
                {
                    // Explicitly create NoResultResponse instance
                    var response = (NoResultResponse)dest;
                    response.Result = new NoResult();
                });
            CreateMap<BaseResponse<NoResult>, NoResultResponse>();
            CreateMap<BaseResponse<ResultId>, NoResultResponse>();

            CreateMap(typeof(BaseResponse<>), typeof(BaseResponse<>));

            CreateMap<BaseResponse<NoResult>, ResultId>();
            CreateMap<ResultId, NoResult>();
            #endregion
        }

        private void ConfigureRequestToEntityMappings()
        {
            #region Tax Mappings
            CreateMap<TaxRateRequest, TaxRate>()
                .ForMember(dest => dest.CompanyDefaultSalesTaxNo, opt => opt.Ignore())
                .ForMember(dest => dest.CompanyDefaultServiceTaxNo, opt => opt.Ignore())
                .ForMember(dest => dest.TransactionSalesTaxNo, opt => opt.Ignore())
                .ForMember(dest => dest.TransactionServiceTaxNo, opt => opt.Ignore())
                .ForMember(dest => dest.TransactionItemSalesTaxNo, opt => opt.Ignore())
                .ForMember(dest => dest.TransactionItemServiceTaxNo, opt => opt.Ignore())
                .ForMember(dest => dest.ProductCustomSalesTaxNo, opt => opt.Ignore())
                .ForMember(dest => dest.ProductCustomServiceTaxNo, opt => opt.Ignore());
            #endregion

            #region Address and Company Mappings
            CreateMap<AddressCompanyRequest, Address>();
            CreateMap<CompanyRequest, Company>();
            CreateMap<BranchRequest, Branch>();
            CreateMap<UpdateBranchRequest, Branch>();
            #endregion

            #region Customer Mappings
            CreateMap<CustomerRequest, Customer>()
                .ForMember(dest => dest.ReferralCode, opt => opt.Ignore()) // We'll set this manually
                .ForMember(dest => dest.Address, opt => opt.Ignore())
                .ForMember(dest => dest.AddressId, opt => opt.Ignore())
                .ForMember(dest => dest.DebtorType, opt => opt.Ignore())
                .ForMember(dest => dest.Referrer, opt => opt.Ignore())
                .ForMember(dest => dest.Currency, opt => opt.Ignore())
                .ForMember(dest => dest.AccountGroup, opt => opt.Ignore())
                .ForMember(dest => dest.IdentityType, opt => opt.Ignore())
                .ForMember(dest => dest.Company, opt => opt.Ignore())
                .ForMember(dest => dest.CustomerReferral, opt => opt.Ignore())
                .ForMember(dest => dest.Transaction, opt => opt.Ignore())
                .ForMember(dest => dest.EInvoice, opt => opt.Ignore());
            CreateMap<UpdateCustomerRequest, Customer>()
                .ForAllMembers(opts => opts.Condition((src, dest, srcMember) => srcMember != null));
            #endregion

            #region Inventory Mappings
            CreateMap<InventoryRequest, Inventory>();
            CreateMap<InventoryProductRequest, InventoryProduct>();
            #endregion

            #region User Mappings
            CreateMap<UserRequest, Register>();
            #endregion

            #region Transaction Mappings
            CreateMap<TransactionRequest, Transaction>();
            CreateMap<TransactionItemRequest, TransactionItem>();
            CreateMap<TransactionPaidRequest, TransactionPaid>();
            CreateMap<UpdateTransactionPaidRequest, TransactionPaid>();
            #endregion

            #region Product Mappings
            CreateMap<ProductRequest, Product>()
                .ForMember(dest => dest.CustomSalesTaxNoId, opt => opt.MapFrom(src => src.CustomSalesTaxNo))
                .ForMember(dest => dest.CustomServiceTaxNoId, opt => opt.MapFrom(src => src.CustomServiceTaxNo))
                .ForMember(dest => dest.CustomSalesTaxNo, opt => opt.Ignore())
                .ForMember(dest => dest.CustomServiceTaxNo, opt => opt.Ignore())
                .ForMember(dest => dest.Company, opt => opt.Ignore())
                .ForMember(dest => dest.ProductCategory, opt => opt.Ignore())
                .ForMember(dest => dest.ProductCostMethod, opt => opt.Ignore());
            CreateMap<UpdateProductRequest, Product>();
            CreateMap<ProductCategoryRequest, ProductCategory>();
            CreateMap<UpdateProductCategoryRequest, ProductCategory>();

            // ProductUOM mappings with null handling
            CreateMap<ProductUOMRequest, ProductUOM>()
                .ForMember(dest => dest.UomSecondary, opt => opt.Ignore())
                .ForMember(dest => dest.UomSecondaryId, opt => opt.MapFrom(src =>
                    src.UomSecondaryId.HasValue ? src.UomSecondaryId.Value : (Guid?)null))
                .ForMember(dest => dest.ProductPrice, opt => opt.Ignore());

            // Map from ProductUOMInputReqeust to ProductUOMRequest
            CreateMap<ProductUOMInputRequest, ProductUOMRequest>()
                .ForMember(dest => dest.FractionTotal, opt => opt.MapFrom(src => src.FractionTotal));

            CreateMap<UpdateProductUOMRequest, ProductUOM>()
                .ForAllMembers(opts => opts.Condition((src, dest, srcMember) => srcMember != null));

            CreateMap<ProductCostMethodRequest, ProductCostMethod>();
            CreateMap<UpdateProductCostMethodRequest, ProductCostMethod>();

            // UOM mappings
            CreateMap<UOMRequest, UOM>();
            CreateMap<UpdateUOMRequest, UOM>();

            // ProductPrice mappings with null handling for dates
            CreateMap<ProductPriceInputRequest, ProductPrice>()
                .ForMember(dest => dest.EffectiveAt, opt => opt.MapFrom(src =>
                    src.EffectiveAt.HasValue ? src.EffectiveAt.Value : DateTime.UtcNow));

            CreateMap<ProductPriceRequest, ProductPrice>()
                .ForMember(dest => dest.EffectiveAt, opt => opt.MapFrom(src =>
                    src.EffectiveAt.HasValue ? src.EffectiveAt.Value : DateTime.UtcNow));

            CreateMap<UpdateProductPriceRequest, ProductPrice>()
                .ForMember(dest => dest.EffectiveAt, opt => opt.MapFrom(src =>
                    src.EffectiveAt.HasValue ? src.EffectiveAt.Value : DateTime.UtcNow))
                .ForAllMembers(opts => opts.Condition((src, dest, srcMember) => srcMember != null));
            #endregion

            #region Prefix Mappings
            CreateMap<PrefixRequest, Prefix>();
            CreateMap<UpdatePrefixRequest, Prefix>();
            #endregion

            #region Lookup Mappings
            CreateMap<LookupGroupRequest, LookupGroup>();
            CreateMap<UpdateLookupGroupRequest, LookupGroup>();
            CreateMap<LookupItemRequest, LookupItem>();
            CreateMap<UpdateLookupItemRequest, LookupItem>();
            CreateMap<LookupGroupRequest, UpdateLookupGroupRequest>();
            CreateMap<LookupItemRequest, UpdateLookupItemRequest>();
            #endregion

            #region EInvoice Mappings
            CreateMap<HttpResponseMessage, EInvoiceAuthResponse>();
            CreateMap<HttpResponseMessage, EInvoiceSearchTinResponse>();
            CreateMap<HttpResponseMessage, EInvoiceValidateTinResponse>();
            CreateMap<EInvoiceErrorLogRequest, EInvoiceErrorLog>();
            #endregion
        }

        private void ConfigureEntityToDtoMappings()
        {
            #region User Mappings
            CreateMap<ApplicationUser, UserDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id));
            #endregion

            #region Account Mappings
            CreateMap<AccountGroupByPeriod, AccountGroupByPeriodDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id));
            CreateMap<BaseResponse<PagedList<AccountGroupByPeriod>>, BaseResponse<List<AccountGroupByPeriodDto>>>();

            CreateMap<AccountGroup, AccountGroupDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id));
            CreateMap<BaseResponse<PagedList<AccountGroup>>, BaseResponse<List<AccountGroupDto>>>();
            #endregion

            #region Address and Company Mappings
            CreateMap<Address, AddressDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id));
            CreateMap<BaseResponse<PagedList<Address>>, BaseResponse<List<AddressDto>>>();
            CreateMap<BaseResponse<Address>, BaseResponse<AddressDto>>();

            CreateMap<Branch, BranchDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id));
            CreateMap<BaseResponse<PagedList<Branch>>, BaseResponse<List<BranchDto>>>();

            CreateMap<Company, CompanyDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.DefaultSalesTaxNo, opt => opt.MapFrom(src => src.DefaultSalesTaxNo))
                .ForMember(dest => dest.DefaultServiceTaxNo, opt => opt.MapFrom(src => src.DefaultServiceTaxNo))
                .ForMember(dest => dest.Branch, opt => opt.MapFrom(src => src.Branch));
            #endregion

            #region Location Mappings
            CreateMap<Country, CountryDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id));
            CreateMap<BaseResponse<PagedList<Country>>, BaseResponse<List<CountryDto>>>();

            CreateMap<Region, RegionDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id));
            CreateMap<BaseResponse<PagedList<Region>>, BaseResponse<List<RegionDto>>>();

            CreateMap<State, StateDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id));
            CreateMap<BaseResponse<PagedList<State>>, BaseResponse<List<StateDto>>>();
            #endregion

            #region Financial Mappings
            CreateMap<Currency, CurrencyDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id));
            CreateMap<BaseResponse<PagedList<Currency>>, BaseResponse<List<CurrencyDto>>>();

            CreateMap<Customer, CustomerDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id));
            CreateMap<BaseResponse<PagedList<Customer>>, BaseResponse<List<CustomerDto>>>();

            CreateMap<DebtorType, DebtorTypeDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id));
            CreateMap<BaseResponse<PagedList<DebtorType>>, BaseResponse<List<DebtorTypeDto>>>();

            CreateMap<PaymentType, PaymentTypeDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id));
            CreateMap<BaseResponse<PagedList<PaymentType>>, BaseResponse<List<PaymentTypeDto>>>();
            #endregion

            #region Product Mappings
            CreateMap<Product, ProductDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.ProductCategory, opt => opt.MapFrom(src => src.ProductCategory))
                .ForMember(dest => dest.ProductCostMethod, opt => opt.MapFrom(src => src.ProductCostMethod));

            // Map FileUploadResponse to FileUploadDto
            CreateMap<FileUploadResponse, FileUploadDto>();

            // Map FileUploadResponse collection to Product's Images collection
            CreateMap<List<FileUploadResponse>, Product>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.Code, opt => opt.Ignore())
                .ForMember(dest => dest.Name, opt => opt.Ignore())
                .ForMember(dest => dest.Description, opt => opt.Ignore())
                .ForMember(dest => dest.IsTaxExempt, opt => opt.Ignore())
                .ForMember(dest => dest.IsTaxExcl, opt => opt.Ignore())
                .ForMember(dest => dest.CustomSalesTaxNoId, opt => opt.Ignore())
                .ForMember(dest => dest.CustomServiceTaxNoId, opt => opt.Ignore())
                .ForMember(dest => dest.Sku, opt => opt.Ignore())
                .ForMember(dest => dest.Image, opt => opt.Ignore())
                .ForMember(dest => dest.Weight, opt => opt.Ignore())
                .ForMember(dest => dest.Length, opt => opt.Ignore())
                .ForMember(dest => dest.Width, opt => opt.Ignore())
                .ForMember(dest => dest.Height, opt => opt.Ignore())
                .ForMember(dest => dest.AccountCode, opt => opt.Ignore())
                .ForMember(dest => dest.AvailableStartAt, opt => opt.Ignore())
                .ForMember(dest => dest.AvailableEndAt, opt => opt.Ignore())
                .ForMember(dest => dest.DisplayOrder, opt => opt.Ignore())
                .ForMember(dest => dest.Published, opt => opt.Ignore())
                .ForMember(dest => dest.CompanyId, opt => opt.Ignore())
                .ForMember(dest => dest.ProductCategoryId, opt => opt.Ignore())
                .ForMember(dest => dest.ProductCostMethodId, opt => opt.Ignore())
                .ForMember(dest => dest.ClassificationId, opt => opt.Ignore())
                .ForMember(dest => dest.CurrencyId, opt => opt.Ignore())
                .ForMember(dest => dest.Company, opt => opt.Ignore())
                .ForMember(dest => dest.ProductCategory, opt => opt.Ignore())
                .ForMember(dest => dest.ProductCostMethod, opt => opt.Ignore())
                .ForMember(dest => dest.CustomSalesTaxNo, opt => opt.Ignore())
                .ForMember(dest => dest.CustomServiceTaxNo, opt => opt.Ignore())
                .ForMember(dest => dest.Classification, opt => opt.Ignore())
                .ForMember(dest => dest.Currency, opt => opt.Ignore())
                .ForMember(dest => dest.InventoryProduct, opt => opt.Ignore())
                .ForMember(dest => dest.InventoryItem, opt => opt.Ignore())
                .ForMember(dest => dest.ProductUOM, opt => opt.Ignore())
                .ForMember(dest => dest.IsActive, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore());
            CreateMap<BaseResponse<PagedList<Product>>, BaseResponse<List<ProductDto>>>();

            CreateMap<ProductCategory, ProductCategoryDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id));
            CreateMap<BaseResponse<PagedList<ProductCategory>>, BaseResponse<List<ProductCategoryDto>>>();

            CreateMap<ProductCostMethod, ProductCostMethodDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id));
            CreateMap<BaseResponse<PagedList<ProductCostMethod>>, BaseResponse<List<ProductCostMethodDto>>>();

            // Enhanced ProductUOM mapping with related entities
            CreateMap<ProductUOM, ProductUOMDto>()
                .ForMember(dest => dest.UomPrimary, opt => opt.MapFrom(src => src.UomPrimary))
                .ForMember(dest => dest.UomSecondary, opt => opt.MapFrom(src => src.UomSecondary))
                .ForMember(dest => dest.EffectivedProductPrice, opt => opt.MapFrom(src => src.EffectivedProductPrice));
            CreateMap<BaseResponse<PagedList<ProductUOM>>, BaseResponse<List<ProductUOMDto>>>();

            // Enhanced ProductPrice mapping
            CreateMap<ProductPrice, ProductPriceDto>();
            CreateMap<BaseResponse<PagedList<ProductPrice>>, BaseResponse<List<ProductPriceDto>>>();

            CreateMap<UOM, UOMDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.UOMCategory, opt => opt.MapFrom(src => src.UOMCategory));
            CreateMap<BaseResponse<PagedList<UOM>>, BaseResponse<List<UOMDto>>>();

            CreateMap<UOMCategories, UOMCategoryDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id));
            CreateMap<BaseResponse<PagedList<UOMCategories>>, BaseResponse<List<UOMCategoryDto>>>();

            CreateMap<TaxCategories, TaxCategory2Dto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id));
            CreateMap<TaxCategories, TaxCategoryDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id));
            CreateMap<BaseResponse<PagedList<TaxCategories>>, BaseResponse<List<TaxCategoryDto>>>();
            #endregion

            #region Tax Mappings
            CreateMap<TaxRate, TaxRateDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id));
            CreateMap<BaseResponse<PagedList<TaxRate>>, BaseResponse<List<TaxRateDto>>>();
            #endregion

            #region Transaction Mappings
            CreateMap<Term, TermDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id));
            CreateMap<BaseResponse<PagedList<Term>>, BaseResponse<List<TermDto>>>();
            CreateMap<BaseResponse<Term>, BaseResponse<TermDto>>();

            CreateMap<TransactionStatus, TransactionStatusDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id));
            CreateMap<BaseResponse<PagedList<TransactionStatus>>, BaseResponse<List<TransactionStatusDto>>>();
            CreateMap<BaseResponse<TransactionStatus>, BaseResponse<TransactionStatusDto>>();

            // Enhanced Transaction mapping with related entities
            CreateMap<Transaction, TransactionDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.Branch, opt => opt.MapFrom(src => src.Branch))
                .ForMember(dest => dest.Customer, opt => opt.MapFrom(src => src.Customer))
                .ForMember(dest => dest.TransactionType, opt => opt.MapFrom(src => src.TransactionType))
                .ForMember(dest => dest.TransactionItem, opt => opt.MapFrom(src => src.TransactionItem));
            CreateMap<BaseResponse<PagedList<Transaction>>, BaseResponse<List<TransactionDto>>>();

            // Enhanced TransactionItem mapping with related entities
            CreateMap<TransactionItem, TransactionItemDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.ProductUOM, opt => opt.MapFrom(src => src.ProductUOM))
                .ForMember(dest => dest.Product, opt => opt.MapFrom(src => src.Product))
                .ForMember(dest => dest.SalesTaxNo, opt => opt.MapFrom(src => src.SalesTaxNo))
                .ForMember(dest => dest.ServiceTaxNo, opt => opt.MapFrom(src => src.ServiceTaxNo));
            CreateMap<BaseResponse<PagedList<TransactionItem>>, BaseResponse<List<TransactionItemDto>>>();

            CreateMap<TransactionPaid, TransactionPaidDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.PaymentType, opt => opt.MapFrom(src => src.PaymentType));
            CreateMap<BaseResponse<PagedList<TransactionPaid>>, BaseResponse<List<TransactionPaidDto>>>();

            CreateMap<TransactionType, TransactionTypeDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id));
            CreateMap<BaseResponse<PagedList<TransactionType>>, BaseResponse<List<TransactionTypeDto>>>();
            #endregion

            #region Prefix Mappings
            CreateMap<Prefix, PrefixDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id));
            CreateMap<BaseResponse<PagedList<Prefix>>, BaseResponse<List<PrefixDto>>>();
            #endregion

            #region Auth Mappings
            // Auth result mapping
            CreateMap<ResultMessage, AuthResultDto>();
            CreateMap<BaseResponse<ResultMessage>, BaseResponse<AuthResultDto>>();
            CreateMap<BaseResponse<AuthResultDto>, BaseResponse<AuthResultDto>>();
            #endregion

            #region Lookup Mappings
            CreateMap<LookupGroup, LookupGroupDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.LookupItems, opt => opt.MapFrom(src => src.LookupItems));
            CreateMap<BaseResponse<PagedList<LookupGroup>>, BaseResponse<List<LookupGroupDto>>>();

            CreateMap<LookupItem, LookupItemDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id));
            CreateMap<BaseResponse<PagedList<LookupItem>>, BaseResponse<List<LookupItemDto>>>();
            #endregion
        }
    }
}
