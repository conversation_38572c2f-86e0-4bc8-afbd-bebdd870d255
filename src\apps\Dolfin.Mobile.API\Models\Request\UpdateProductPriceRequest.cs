﻿﻿using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Dolfin.Mobile.API.Models.Request
{
    public class UpdateProductPriceRequest
    {
        [JsonIgnore]
        [Required(ErrorMessage = "ID is required")]
        public Guid Id { get; set; }
        [Range(1.00, double.MaxValue, ErrorMessage = "FractionQty must be at least 1")]
        public decimal FractionQty { get; set; } = 1.00m;

        [Required(ErrorMessage = "Price is required")]
        [Range(0.01, double.MaxValue, ErrorMessage = "Price must be greater than 0")]
        public decimal Price { get; set; }

        // EffectiveAt is not required, will be set to UTC now by default if not provided
        public DateTime? EffectiveAt { get; set; }

        public string? Remark { get; set; }
    }
}
