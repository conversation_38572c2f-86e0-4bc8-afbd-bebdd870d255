﻿using static Dolfin.Utility.Enum.Enums;

namespace Dolfin.Mobile.API.Models.Request
{
    public partial class UpdateInventoryProductRequest
    {
        public Guid Id { get; set; }
        public int UnitQuantity { get; set; }
        public bool IsAddSafeQuantity { get; set; } = false;
        public bool IsRemoveSafeQuantity { get; set; } = false;
        public Guid? BranchId { get; set; } // for Admin user only, others user will get from userManager
    }
}
