﻿using Dolfin.Framework.Data.Domains;
using System;
using System.Collections.Generic;

namespace Dolfin.Mobile.API.Models
{
    public partial class Email : _BaseDomain
    {
        public int Priority { get; set; }
        public string FromEmail { get; set; }
        public string FromName { get; set; }
        public string ToEmail { get; set; }
        public string ToName { get; set; }
        public string ReplyToEmail { get; set; }
        public string ReplyToName { get; set; }
        public string Cc { get; set; }
        public string Bcc { get; set; }
        public string Subject { get; set; }
        public string Body { get; set; }
        public string AttachmentFilePath { get; set; }
        public string AttachmentFileName { get; set; }
        public DateTime? DontSendBeforeDateUtc { get; set; }
        public int SentTries { get; set; }
        public DateTime? SentOnUtc { get; set; }
        public Guid EmailAccountId { get; set; }

        public virtual EmailAccount EmailAccount { get; set; }
    }
}
