﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace Dolfin.Framework.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddCompanyRelation : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Transaction_User_UserId",
                table: "Transaction");

            migrationBuilder.DeleteData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("1827ba02-cfb0-47b8-b6eb-d1b8b48149ee"));

            migrationBuilder.DeleteData(
                table: "Settings",
                keyColumn: "Id",
                keyValue: new Guid("e7697b90-e816-4c5c-aa41-c0582c6e2cca"));

            migrationBuilder.DeleteData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("175e61c2-f9d9-4a94-92fb-1118cad1f10e"));

            migrationBuilder.DeleteData(
                table: "TaxRate",
                keyColumn: "Id",
                keyValue: new Guid("9fe15bfd-653a-4a68-b40d-63f07d05b2fa"));

            migrationBuilder.DeleteData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("378a4f7a-f276-40ae-b6ee-d6717a3099f7"));

            migrationBuilder.AlterColumn<string>(
                name: "UserId",
                table: "Transaction",
                type: "text",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(Guid),
                oldType: "uuid",
                oldNullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "UserId1",
                table: "Transaction",
                type: "uuid",
                nullable: true);

            migrationBuilder.InsertData(
                table: "Currency",
                columns: new[] { "Id", "Code", "CreatedAt", "CreatedBy", "ExchangeRate", "IsActive", "Name", "Precision", "Symbol", "UpdatedAt", "UpdatedBy" },
                values: new object[] { new Guid("85c8bccf-7458-41af-84ec-4c6736f66644"), "MYR", new DateTime(2024, 9, 23, 16, 18, 28, 942, DateTimeKind.Utc).AddTicks(2772), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), 1.0m, true, "Malaysia Ringgit", 2, "RM", null, null });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("aef630d3-6f0b-4c1f-a17b-c60f8f0408ea"),
                column: "CreatedAt",
                value: new DateTime(2024, 9, 23, 16, 18, 28, 942, DateTimeKind.Utc).AddTicks(1807));

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("b3ec5d2b-ca0d-45bc-ba86-bc965852922c"),
                column: "CreatedAt",
                value: new DateTime(2024, 9, 23, 16, 18, 28, 942, DateTimeKind.Utc).AddTicks(2405));

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("c0a87fee-d2a5-4c7e-beee-e60c7936f056"),
                column: "CreatedAt",
                value: new DateTime(2024, 9, 23, 16, 18, 28, 942, DateTimeKind.Utc).AddTicks(2129));

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("dbd5a6a5-0995-4ab6-822f-559ec36d222e"),
                column: "CreatedAt",
                value: new DateTime(2024, 9, 23, 16, 18, 28, 942, DateTimeKind.Utc).AddTicks(2697));

            migrationBuilder.InsertData(
                table: "Settings",
                columns: new[] { "Id", "Code", "CreatedBy", "Description", "Name", "Type", "UpdatedAt", "UpdatedBy", "Value" },
                values: new object[] { new Guid("4ae51f62-59bf-4fbd-86b4-7635705c219d"), "SYSTEM_NAME", new Guid("00000000-0000-0000-0000-000000000000"), "Name of the system", "System name", "SYSTEM", null, null, "Dolfin Solutions" });

            migrationBuilder.InsertData(
                table: "TaxCategories",
                columns: new[] { "Id", "Code", "CreatedAt", "CreatedBy", "Description", "IsActive", "Name", "UpdatedAt", "UpdatedBy" },
                values: new object[,]
                {
                    { new Guid("23355ff7-e300-487c-b570-67d80384ce85"), "SERVICETAX", new DateTime(2024, 9, 23, 16, 18, 28, 942, DateTimeKind.Utc).AddTicks(2826), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), null, true, "Service Tax", null, null },
                    { new Guid("44bfa063-191f-41ff-bc73-7329a670632b"), "SALESTAX", new DateTime(2024, 9, 23, 16, 18, 28, 942, DateTimeKind.Utc).AddTicks(2819), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), null, true, "Sales Tax", null, null }
                });

            migrationBuilder.InsertData(
                table: "TaxRate",
                columns: new[] { "Id", "ChargePercentage", "Code", "CreatedAt", "CreatedBy", "Description", "IsActive", "Name", "TaxCategoryId", "UpdatedAt", "UpdatedBy" },
                values: new object[] { new Guid("24afe535-1923-45a0-9d8e-24b40e8b7fc0"), 0, "SR", new DateTime(2024, 9, 23, 16, 18, 28, 942, DateTimeKind.Utc).AddTicks(2864), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), null, true, "Sales Rate", new Guid("44bfa063-191f-41ff-bc73-7329a670632b"), null, null });

            migrationBuilder.CreateIndex(
                name: "IX_Transaction_UserId1",
                table: "Transaction",
                column: "UserId1");

            migrationBuilder.CreateIndex(
                name: "IX_AspNetUsers_BranchId",
                table: "AspNetUsers",
                column: "BranchId");

            migrationBuilder.CreateIndex(
                name: "IX_AspNetUsers_CompanyId",
                table: "AspNetUsers",
                column: "CompanyId");

            migrationBuilder.AddForeignKey(
                name: "FK_Branch_User",
                table: "AspNetUsers",
                column: "BranchId",
                principalTable: "Branch",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Company_User",
                table: "AspNetUsers",
                column: "CompanyId",
                principalTable: "Company",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Transaction_AspNetUsers_UserId",
                table: "Transaction",
                column: "UserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Transaction_User_UserId1",
                table: "Transaction",
                column: "UserId1",
                principalTable: "User",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Branch_User",
                table: "AspNetUsers");

            migrationBuilder.DropForeignKey(
                name: "FK_Company_User",
                table: "AspNetUsers");

            migrationBuilder.DropForeignKey(
                name: "FK_Transaction_AspNetUsers_UserId",
                table: "Transaction");

            migrationBuilder.DropForeignKey(
                name: "FK_Transaction_User_UserId1",
                table: "Transaction");

            migrationBuilder.DropIndex(
                name: "IX_Transaction_UserId1",
                table: "Transaction");

            migrationBuilder.DropIndex(
                name: "IX_AspNetUsers_BranchId",
                table: "AspNetUsers");

            migrationBuilder.DropIndex(
                name: "IX_AspNetUsers_CompanyId",
                table: "AspNetUsers");

            migrationBuilder.DeleteData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("85c8bccf-7458-41af-84ec-4c6736f66644"));

            migrationBuilder.DeleteData(
                table: "Settings",
                keyColumn: "Id",
                keyValue: new Guid("4ae51f62-59bf-4fbd-86b4-7635705c219d"));

            migrationBuilder.DeleteData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("23355ff7-e300-487c-b570-67d80384ce85"));

            migrationBuilder.DeleteData(
                table: "TaxRate",
                keyColumn: "Id",
                keyValue: new Guid("24afe535-1923-45a0-9d8e-24b40e8b7fc0"));

            migrationBuilder.DeleteData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("44bfa063-191f-41ff-bc73-7329a670632b"));

            migrationBuilder.DropColumn(
                name: "UserId1",
                table: "Transaction");

            migrationBuilder.AlterColumn<Guid>(
                name: "UserId",
                table: "Transaction",
                type: "uuid",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.InsertData(
                table: "Currency",
                columns: new[] { "Id", "Code", "CreatedAt", "CreatedBy", "ExchangeRate", "IsActive", "Name", "Precision", "Symbol", "UpdatedAt", "UpdatedBy" },
                values: new object[] { new Guid("1827ba02-cfb0-47b8-b6eb-d1b8b48149ee"), "MYR", new DateTime(2024, 9, 1, 14, 48, 11, 532, DateTimeKind.Utc).AddTicks(1907), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), 1.0m, true, "Malaysia Ringgit", 2, "RM", null, null });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("aef630d3-6f0b-4c1f-a17b-c60f8f0408ea"),
                column: "CreatedAt",
                value: new DateTime(2024, 9, 1, 14, 48, 11, 532, DateTimeKind.Utc).AddTicks(836));

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("b3ec5d2b-ca0d-45bc-ba86-bc965852922c"),
                column: "CreatedAt",
                value: new DateTime(2024, 9, 1, 14, 48, 11, 532, DateTimeKind.Utc).AddTicks(1488));

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("c0a87fee-d2a5-4c7e-beee-e60c7936f056"),
                column: "CreatedAt",
                value: new DateTime(2024, 9, 1, 14, 48, 11, 532, DateTimeKind.Utc).AddTicks(1181));

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("dbd5a6a5-0995-4ab6-822f-559ec36d222e"),
                column: "CreatedAt",
                value: new DateTime(2024, 9, 1, 14, 48, 11, 532, DateTimeKind.Utc).AddTicks(1843));

            migrationBuilder.InsertData(
                table: "Settings",
                columns: new[] { "Id", "Code", "CreatedBy", "Description", "Name", "Type", "UpdatedAt", "UpdatedBy", "Value" },
                values: new object[] { new Guid("e7697b90-e816-4c5c-aa41-c0582c6e2cca"), "SYSTEM_NAME", new Guid("00000000-0000-0000-0000-000000000000"), "Name of the system", "System name", "SYSTEM", null, null, "Dolfin Solutions" });

            migrationBuilder.InsertData(
                table: "TaxCategories",
                columns: new[] { "Id", "Code", "CreatedAt", "CreatedBy", "Description", "IsActive", "Name", "UpdatedAt", "UpdatedBy" },
                values: new object[,]
                {
                    { new Guid("175e61c2-f9d9-4a94-92fb-1118cad1f10e"), "SERVICETAX", new DateTime(2024, 9, 1, 14, 48, 11, 532, DateTimeKind.Utc).AddTicks(1965), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), null, true, "Service Tax", null, null },
                    { new Guid("378a4f7a-f276-40ae-b6ee-d6717a3099f7"), "SALESTAX", new DateTime(2024, 9, 1, 14, 48, 11, 532, DateTimeKind.Utc).AddTicks(1957), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), null, true, "Sales Tax", null, null }
                });

            migrationBuilder.InsertData(
                table: "TaxRate",
                columns: new[] { "Id", "ChargePercentage", "Code", "CreatedAt", "CreatedBy", "Description", "IsActive", "Name", "TaxCategoryId", "UpdatedAt", "UpdatedBy" },
                values: new object[] { new Guid("9fe15bfd-653a-4a68-b40d-63f07d05b2fa"), 0, "SR", new DateTime(2024, 9, 1, 14, 48, 11, 532, DateTimeKind.Utc).AddTicks(1999), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), null, true, "Sales Rate", new Guid("378a4f7a-f276-40ae-b6ee-d6717a3099f7"), null, null });

            migrationBuilder.AddForeignKey(
                name: "FK_Transaction_User_UserId",
                table: "Transaction",
                column: "UserId",
                principalTable: "User",
                principalColumn: "Id");
        }
    }
}
