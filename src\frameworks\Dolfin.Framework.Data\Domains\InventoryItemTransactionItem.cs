﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Framework.Data.Domains
{
    public partial class InventoryItemTransactionItem : _BaseDomain
    {
        public decimal Quantity { get; set; }
        public Guid InventoryItemId { get; set; }
        public Guid TransactionItemId { get; set; }
        public virtual InventoryItem? InventoryItem { get; set; }
        public virtual TransactionItem? TransactionItem { get; set; }
    }
}
