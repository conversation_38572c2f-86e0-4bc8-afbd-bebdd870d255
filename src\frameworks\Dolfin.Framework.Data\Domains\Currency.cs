﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Framework.Data.Domains
{
    public partial class Currency : _BaseDomain
    {
        public Currency()
        {
            Company = new HashSet<Company>();
            Customer = new HashSet<Customer>();
            Product = new HashSet<Product>();
        }

        public required string Code { get; set; }
        public required string Name { get; set; }
        public required string Symbol { get; set; }
        public decimal ExchangeRate { get; set; }
        public int Precision { get; set; }
        public virtual ICollection<Company> Company { get; }
        public virtual ICollection<Customer> Customer { get; }
        public virtual ICollection<Product> Product { get; }

    }
}
