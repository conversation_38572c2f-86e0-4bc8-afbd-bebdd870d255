﻿using Dolfin.Framework.Data.Domains.CustomIdentity;
using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Framework.Data.Domains
{
    public partial class Classification : _BaseDomain
    {
        public Classification()
        {
            Product = new HashSet<Product>();
        }
        public required string Code { get; set; }
        public required string Description { get; set; }
        public virtual ICollection<Product> Product { get; }
    }
}
