﻿using Dolfin.Framework.Data.Domains;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Dolfin.Mobile.API.Models.Request
{
    public class ProductUOMRequest
    {
        public string Code { get; set; }

        public string Name { get; set; }

        public string? Description { get; set; }

        [JsonIgnore]
        public decimal FractionTotal { get; set; }

        [Required(ErrorMessage = "Fraction is required")]
        [Range(1.00, double.MaxValue, ErrorMessage = "Fraction must be at least 1")]
        public decimal Fraction { get; set; }

        [Required(ErrorMessage = "Barcode is required")]
        public string Barcode { get; set; }

        public bool IsMainUom { get; set; }

        public bool IsPriceFollowUomMainId { get; set; }

        public decimal? Cost { get; set; }

        public decimal? PreviousCost { get; set; }

        public decimal? OrderMinQty { get; set; }

        public decimal? OrderMaxQty { get; set; }

        public bool PriceEditable { get; set; }

        public decimal? MinEditPrice { get; set; }

        public decimal? MaxEditPrice { get; set; }

        [Required(ErrorMessage = "UOM Primary ID is required")]
        public Guid UomPrimaryId { get; set; }

        public Guid? UomSecondaryId { get; set; }

        [Required(ErrorMessage = "Product ID is required")]
        public Guid ProductId { get; set; }

        public ProductPriceInputRequest ProductPrice { get; set; }
    }
}
