﻿namespace Dolfin.Utility.Constant
{
    public class Constant
    {
        public class AdminConfiguration()
        {
            public const string FrontendPath = "https://dev.dolfin.my";
            public const int MaxLoginCount = 3;
            public const int LoginLockedEndMin = 15;
            public const int MaxUserGenerateByCompany = 3;
            public const int PasswordExpiredDay = 90;
            public static Guid SystemUser = new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56");
        }

        public class StandardErrorMessage()
        {
            public const string MaxUserGenerateByCompany = "Maximun user created. Please contact Admin.";
            public const string AccessError = "No user access.";
        }
    }
}