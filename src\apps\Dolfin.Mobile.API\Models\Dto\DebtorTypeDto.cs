﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Mobile.API.Models.Dto
{
    public partial class DebtorTypeDto
    {
        public DebtorTypeDto()
        {
            //Customer = new HashSet<Customer>();
        }
        public Guid Id { get; set; }
        public required string Code { get; set; }
        public required string Name { get; set; }
        //public virtual ICollection<Customer> Customer { get; }
    }
}
