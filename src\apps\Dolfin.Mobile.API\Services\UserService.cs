﻿using AutoMapper;
using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Domains.CustomIdentity;
using Dolfin.Framework.Data.Entity;
using Dolfin.Framework.Repository.Common;
using Dolfin.Framework.Repository.Interfaces;
using Dolfin.Mobile.API.Constants;
using Dolfin.Mobile.API.Models;
using Dolfin.Mobile.API.Models.Request;
using Dolfin.Mobile.API.Repositories.Interfaces;
using Dolfin.Utility.Constant;
using Dolfin.Utility.Enum;
using Dolfin.Utility.Models;
using Dolfin.Utility.Utils;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using System.Security.Claims;
using static Dolfin.Utility.Constant.Constant;
using static Dolfin.Utility.Enum.Enums;

namespace Dolfin.Mobile.API.Services
{
    public class UserService : IUserService
    {
        private readonly StandardMessage _standardMessage;
        private readonly ILogger<UserService> _logger;
        private readonly IMapper _mapper;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IHttpContextAccessor _contextAccessor;
        private readonly ICacheService _cacheService;
        private readonly CacheSettings _cacheSettings;
        private readonly IUserRepository _userRepository;
        private readonly IUnitOfWork _unitOfWork;

        public UserService(
            UserManager<ApplicationUser> userManager,
            IHttpContextAccessor httpContextAccessor,
            ILoggerFactory loggerFactory,
            IMapper mapper,
            ICacheService cacheService,
            IOptions<CacheSettings> cacheSettings,
            IUserRepository userRepository,
            IUnitOfWork unitOfWork)
        {
            _standardMessage = new StandardMessage();
            _contextAccessor = httpContextAccessor;
            _userManager = userManager;
            _logger = loggerFactory.CreateLogger<UserService>();
            _mapper = mapper;
            _cacheService = cacheService;
            _cacheSettings = cacheSettings.Value;
            _userRepository = userRepository;
            _unitOfWork = unitOfWork;
        }

        //public async Task<Guid> CreateApplicationUser(ApplicationUser ApplicationUser)
        //{

        //    _db.Add(ApplicationUser);
        //    var result = await _db.SaveChangesAsync();
        //    return await ApplicationUser.Id;
        //}

        public async Task<BaseResponse<ApplicationUser>> GetUserProfile(string searchUserId)
        {
            var result = new BaseResponse<ApplicationUser> { IsSuccessful = true };
            try
            {
                var user = await GetCurrentUserAsync();
                var displayUserId = user.Id;
                var loginUserType = user.UserRoles.First().RoleId;

                if (searchUserId != null)
                {
                    var searchedUser = await GetUserByGuid(searchUserId);
                    if (searchedUser != null)
                    {
                        displayUserId = searchedUser.Id;
                    }
                }

                var response = await GetUserByGuid(displayUserId);
                if (response != null)
                {
                    response = MassageUserProfile(response, loginUserType);
                }
                result.Result = response;
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<ApplicationUser, ApplicationUser>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<ApplicationUser?> GetUserByGuid(string id)
        {
            return await _userRepository.GetUserByGuidAsync(id);
        }

        public async Task<ApplicationUser?> GetUserByUsername(string username)
        {
            return await _userRepository.GetUserByUsernameAsync(username);
        }

        public async Task<List<ApplicationUser>> GetUserByBranchId(Guid branchId)
        {
            return await _userRepository.GetUsersByBranchIdAsync(branchId);
        }

        private async Task<List<ApplicationUser>> GetUserByCompanyId(Guid companyId)
        {
            var users = await _userRepository.FindAsync(x => x.CompanyId == companyId && x.IsActive);
            return users.ToList();
        }

        public async Task<List<ApplicationUser>> GetUserList(string[] userId)
        {
            if (userId.Length > 0)
            {
                return await _userRepository.GetUserListAsync(userId);
            }
            else
            {
                return new List<ApplicationUser>();
            }
        }

        //public async Task<BaseResponse<ResultId>> InsertUser(UserRequest reqBody, Guid? UserId = null)
        //{
        //    var result = new BaseResponse<ResultId> { IsSuccessful = true };
        //    try
        //    {
        //        string emailName = null;

        //        var validateMaxUserGenerate = await ValidateMaxUserGenerate((Guid)reqBody.BranchId);
        //        if (!validateMaxUserGenerate)
        //            return _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.Unauthorized, StandardErrorMessage.MaxUserGenerateByCompany);

        //        if (!string.IsNullOrEmpty(reqBody.Email))
        //        {
        //            MailAddress email = new MailAddress(reqBody.Email);
        //            emailName = email.User;
        //        }

        //        reqBody.Username = string.IsNullOrEmpty(reqBody.Username) ? reqBody.Email : reqBody.Username;

        //        UserTypeEnum UserTypeId;
        //        if (reqBody.UserTypeId.HasValue)
        //            UserTypeId = Enums.GetValueFromAmbient<UserTypeEnum>((Guid)reqBody.UserTypeId);
        //        else
        //        {
        //            UserTypeId = Enums.GetValueFromAmbient<UserTypeEnum>(UserTypeEnum.User.GetAmbientValue().Value);
        //        }
        //        if (UserTypeId.GetAmbientValue() == null)
        //            return _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.NotFound, "User Type no found.");
        //        else
        //            reqBody.UserTypeId = UserTypeId.GetAmbientValue();

        //        if ((Guid)reqBody.UserTypeId != UserTypeEnum.Admin.GetAmbientValue().Value)
        //        {
        //            if (reqBody.BranchId == null || reqBody.CompanyId == null)
        //            {
        //                return _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.BadRequest, "BranchId and CompanyId is required.");
        //            }
        //        }

        //        // return error if duplicate email / Username found
        //        var uniqueCheck = GetUniqueUserProfile(reqBody.Email, reqBody.Username);
        //        if (!uniqueCheck.IsSuccessful)
        //        {
        //            return uniqueCheck;
        //        }

        //        // PasswordHash & PasswordSalt
        //        var passwordSaltHash = PasswordService.HashPasswordSalt(reqBody.Username, reqBody.Password);
        //        var passwordHash = PasswordService.HashPassword(reqBody, passwordSaltHash.Password);
        //        // Google and Facebook set email and phone number to true if exist
        //        //if ((int)AuthTypeEnum.Google == reqBody.AuthTypeId || (int)AuthTypeEnum.Facebook == reqBody.AuthTypeId)
        //        //{
        //        //    reqBody.EmailConfirmed = string.IsNullOrEmpty(reqBody.Email) ? false : true;
        //        //    reqBody.PhoneNumberConfirmed = string.IsNullOrEmpty(reqBody.PhoneNumber) ? false : true;
        //        //}

        //        // User
        //        var currentDateTIme = DateTime.UtcNow;
        //        User newUsers = _mapper.Map<User>(reqBody);
        //        newUsers.Username = reqBody.FullName ?? emailName;
        //        newUsers.Password = passwordHash;
        //        newUsers.PasswordSalt = passwordSaltHash.PasswordSalt;
        //        newUsers.PasswordExpireAt = currentDateTIme.AddDays(AdminConfiguration.PasswordExpiredDay);
        //        newUsers.SerialNo = "";
        //        newUsers.LastLoginAt = currentDateTIme;
        //        newUsers.IsActive = true;
        //        newUsers.CreatedAt = currentDateTIme;
        //        newUsers.CreatedBy = UserId ?? AdminConfiguration.SystemUser;

        //        var User = await CreateAsync(newUsers);

        //        // UserRoles
        //        CreateUserRole(User.Id, UserTypeId);

        //        //// Insert queue email
        //        //InsertEmailValidationEmail(User);
        //    }
        //    catch (Exception ex)
        //    {
        //        result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
        //    }
        //    return result;
        //}

        //public async Task<BaseResponse<ResultId>> ValidateCredentials(LoginRequestLocal loginPassword)
        //{
        //    var result = new BaseResponse<ResultId> { IsSuccessful = true, Result = new ResultId() };
        //    try
        //    {
        //        var validateCredentials = await ValidateCredentials(loginPassword, true);
        //        if (!validateCredentials.Item1)
        //            return _standardMessage.ErrorMessage<BaseResponse<ResultId>, ResultId>(result, Enums.StatusCode.BadRequest, statusMessage: "Invalid User or password.");
        //        else
        //            result.Result.Id = validateCredentials.Item2;
        //    }
        //    catch (Exception ex)
        //    {
        //        result = _standardMessage.ErrorMessage<BaseResponse<ResultId>, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
        //    }
        //    return result;
        //}

        //public async Task<(bool, Guid?)> ValidateCredentials(LoginRequestLocal loginPassword, bool isPasswordUpdate = false)
        //{
        //    var validCredentail = true;
        //    Guid? UserId = null;
        //    try
        //    {
        //        var User = await GetUserByUsername(loginPassword.Username);

        //        if (User == null)
        //            throw new Exception("User not found");

        //        var UserTypeId = User.UserRole.OrderBy(x => x.RoleId).FirstOrDefault()?.RoleId;

        //        if ((User.Locked && (AdminConfiguration.LoginLockedEndMin == 0 || (AdminConfiguration.LoginLockedEndMin > 0 && User.LockedAt >= DateTime.UtcNow)))
        //            //|| (User.Company == null && (Guid)UserTypeId != UserTypeEnum.Admin.GetAmbientValue().Value)
        //            //|| (User.Company != null && User.Company.ExpiredAt >= DateTime.UtcNow)
        //            || User.PasswordExpireAt >= DateTime.UtcNow)
        //        {
        //            validCredentail = false;
        //        }
        //        else
        //        {
        //            string? entPassword = null;
        //            // if salt is empty then its a clear text password, else hashed with SHA1
        //            if (!string.IsNullOrEmpty(User.PasswordSalt))
        //            {
        //                entPassword = PasswordService.CreatePasswordHash(loginPassword.Password, User.PasswordSalt);
        //            }
        //            else
        //            {
        //                entPassword = loginPassword.Password;
        //            }

        //            var isValid = PasswordService.VerifyPassword<User>(User, User?.Password, entPassword);

        //            if (isPasswordUpdate)
        //            {
        //                if (isValid)
        //                {
        //                    UserId = User.Id;
        //                    User.AccessFailedCount = 0;
        //                    User.Locked = false;
        //                    User.LockedAt = null;
        //                    User.LockedEnd = null;
        //                    User.LastLoginAt = DateTime.UtcNow;
        //                }
        //                else
        //                {
        //                    User.AccessFailedCount += 1;
        //                    if (User.AccessFailedCount >= AdminConfiguration.MaxLoginCount)
        //                    {
        //                        User.Locked = true;
        //                        User.LockedAt = DateTime.UtcNow;
        //                        if (AdminConfiguration.LoginLockedEndMin > 0)
        //                            User.LockedEnd = DateTime.UtcNow.AddMinutes(AdminConfiguration.LoginLockedEndMin);
        //                    }
        //                }
        //                await UpdateAsync(User);
        //            }
        //            return (isValid, UserId);
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }

        //    return (validCredentail, UserId);
        //}

        //public async Task<NoResultResponse> ChangePassword(Guid loginUserId, string oldPassword, string newPassword)
        //{
        //    var result = new NoResultResponse { IsSuccessful = true };
        //    try
        //    {
        //        var User = await GetUserByGuid(loginUserId);

        //        if (User == null)
        //        {
        //            return _standardMessage.ErrorMessage<NoResult, NoResult>(result, Enums.StatusCode.BadRequest, statusMessage: "Invalid User.");
        //        }
        //        else
        //        {
        //            if (!(await ValidateCredentials(new LoginRequestLocal() { Username = User.Username, Password = oldPassword }, false)).Item1)
        //            {
        //                return _standardMessage.ErrorMessage<NoResult, NoResult>(result, Enums.StatusCode.BadRequest, statusMessage: "Invalid User or password.");
        //            }
        //            else
        //            {
        //                var passwordSaltHash = PasswordService.HashPasswordSalt(User.Username, newPassword);
        //                var passwordHash = PasswordService.HashPassword(User, passwordSaltHash.Password);
        //                User.Password = passwordHash;
        //                User.PasswordSalt = passwordSaltHash.PasswordSalt;
        //                var UserTypeId = User.UserRole.OrderBy(x => x.RoleId).FirstOrDefault()?.RoleId;
        //                if ((Guid)UserTypeId != UserTypeEnum.Admin.GetAmbientValue().Value)
        //                    User.PasswordExpireAt = DateTime.UtcNow.AddDays(AdminConfiguration.PasswordExpiredDay);
        //                User.UpdatedAt = DateTime.UtcNow;
        //                User.UpdatedBy = loginUserId;
        //                await UpdateAsync(User);
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        result = _standardMessage.ErrorMessage<NoResult, NoResult>(result, Enums.StatusCode.InternalServerError, exception: ex);
        //    }
        //    return result;
        //}

        //public async Task<User> GetUser(Expression<Func<User, bool>> predicate)
        //{
        //    Expression<Func<User, bool>> filterPredicate = u => u.User.Any(n => n.IsActive == true && n.IsDeleted == false);
        //    predicate = ExpressionExtensions.AndAlso(predicate, filterPredicate);

        //    var q = GetDbContext().Set<AuthUsers>().AsQueryable();

        //    var data = await q.Include(u => u.AuthUserClaims)
        //        .Include(u => u.AuthUserRoles).ThenInclude(u => u.User)
        //        .Include(u => u.AuthUserRoles).ThenInclude(u => u.Role).ThenInclude(p => p.AuthRoleClaims)
        //        .Include(u => u.Customer).ThenInclude(u => u.Language)
        //        .Include(u => u.Customer).ThenInclude(c => c.Address).ThenInclude(c => c.StateProvinceNavigation)
        //        .Include(u => u.Customer).ThenInclude(c => c.Address).ThenInclude(c => c.CountryNavigation)
        //        .Include(u => u.Customer).ThenInclude(c => c.Country)
        //        .Include(u => u.UserType).Include(u => u.AuthType).Include(u => u.AuthUserLogins)
        //        .Where(predicate).FirstOrDefaultAsync();

        //    return data;
        //}

        public async Task<NoResultResponse> DeleteUser(string id)
        {
            var result = new NoResultResponse { IsSuccessful = true };
            try
            {
                var user = await GetUserByGuid(id);
                if (user == null)
                {
                    throw new Exception($"{id} not found.");
                }

                var userTypeId = user.UserRoles.OrderBy(x => x.RoleId).FirstOrDefault()?.RoleId;
                if (userTypeId == null)
                {
                    throw new Exception($"{id} user role not found.");
                }

                var adminValue = UserTypeEnum.Admin.GetAmbientValue();
                if (adminValue.HasValue && userTypeId == adminValue.Value.ToString())
                {
                    throw new Exception($"{id} not able to delete.");
                }

                var currentUser = await GetCurrentUserAsync();

                user.IsActive = false;
                user.UpdatedAt = DateTime.UtcNow;
                user.UpdatedBy = Guid.Parse(currentUser.Id);

                // Update user in repository
                _userRepository.Update(user);
                await _unitOfWork.SaveChangesAsync();

                // Invalidate user cache
                InvalidateUserCache(user.Id);
                _logger.LogInformation("Invalidated cache for deleted user {UserId} using key {CacheKey}",
                    user.Id, CacheKeys.User.GetCurrentUserKey(user.Id));
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<NoResult, NoResult>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        //private async Task<bool> ValidateMaxApplicationUserGenerate(Guid branchId)
        //{
        //    bool validMaxApplicationUserGenerateByCompany = true;
        //    try
        //    {
        //        var branchApplicationUsersResult = await GetUserByBranchId(branchId);
        //        if (branchApplicationUsersResult != null && branchApplicationUsersResult.Count() > AdminConfiguration.MaxApplicationUserGenerateByCompany)
        //        {
        //            validMaxApplicationUserGenerateByCompany = false;
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //    return validMaxApplicationUserGenerateByCompany;
        //}

        //private static bool ValidatePassword(string enteredPassword, LoginRequestLocal loginPassword)
        //{
        //    var savedPassword = string.Empty;
        //    try
        //    {
        //        if (loginPassword == null || string.IsNullOrEmpty(enteredPassword))
        //            return false;

        //        // if salt is empty then its a clear text password, else hashed with SHA1
        //        if (!string.IsNullOrEmpty(loginPassword.PasswordSalt))
        //        {
        //            savedPassword = PasswordService.CreatePasswordHash(enteredPassword, loginPassword.PasswordSalt);
        //            //savedPassword = PasswordService.HashPassword(reqBody, savedPassword);
        //        }
        //        else
        //        {
        //            savedPassword = enteredPassword;
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }

        //    return savedPassword.Equals(loginPassword.Password);
        //}

        private static ApplicationUser MassageUserProfile(ApplicationUser user, string userTypeId)
        {
            // only Admin and SuperAdmin allow to do all master edit
            var adminValue = UserTypeEnum.Admin.GetAmbientValue();
            var superAdminValue = UserTypeEnum.SuperAdmin.GetAmbientValue();

            if (userTypeId != null &&
                adminValue.HasValue &&
                superAdminValue.HasValue &&
                (userTypeId == adminValue.Value.ToString() ||
                 userTypeId == superAdminValue.Value.ToString()))
            {
                user.IsAllowEditable = true;
            }

            return user;
        }

        //private BaseResponse<ResultId> GetUniqueApplicationUserProfile(string email, string ApplicationUsername)
        //{
        //    var result = new BaseResponse<ResultId> { IsSuccessful = true, Result = new ResultId() };
        //    try
        //    {
        //        if (email != null)
        //        {
        //            bool isEmailExist = Exist(x => x.Email == email.ToUpper());
        //            if (isEmailExist)
        //            {
        //                return _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.Duplicate, "email");
        //            }
        //        }

        //        if (ApplicationUsername != null)
        //        {
        //            bool isApplicationUsernameExist = Exist(x => x.ApplicationUsername == ApplicationUsername.ToUpper());
        //            if (isApplicationUsernameExist)
        //            {
        //                return _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.Duplicate, "ApplicationUsername");
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
        //    }
        //    return result;
        //}

        //private Guid? CreateApplicationUserRole(Guid ApplicationUserId, ApplicationUserTypeEnum ApplicationUserType) {
        //    Guid? ApplicationUserRoleId;
        //    try
        //    {
        //        ApplicationUserRole ApplicationUserRole = new ApplicationUserRole { ApplicationUserId = ApplicationUserId, RoleId = ApplicationUserType.GetAmbientValue().Value };
        //        var ApplicationUserRoleResult = await CreateAsync<ApplicationUserRole>(ApplicationUserRole);
        //        ApplicationUserRoleId = ApplicationUserRoleResult.Id;
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //    return ApplicationUserRoleId;
        //}

        public async Task<ApplicationUser> GetCurrentUserAsync(ClaimsPrincipal? user = null)
        {
            if (user == null)
            {
                // Try to get user from HttpContext
                user = _contextAccessor.HttpContext?.User;

                // If still null, try to authenticate via JWT
                if (user == null || (user.Identity != null && !user.Identity.IsAuthenticated))
                {
                    if (_contextAccessor.HttpContext != null)
                    {
                        // Try cookie authentication first
                        var cookieResult = await _contextAccessor.HttpContext.AuthenticateAsync(CookieAuthenticationDefaults.AuthenticationScheme);
                        _logger.LogInformation("Cookie authentication result: {Result}", cookieResult.Succeeded);

                        if (cookieResult.Succeeded)
                        {
                            user = cookieResult.Principal;
                            // Update the HttpContext.User
                            _contextAccessor.HttpContext.User = user;
                        }
                        else
                        {
                            // Try JWT authentication
                            var jwtResult = await _contextAccessor.HttpContext.AuthenticateAsync(JwtBearerDefaults.AuthenticationScheme);
                            _logger.LogInformation("JWT authentication result: {Result}", jwtResult.Succeeded);

                            if (jwtResult.Succeeded)
                            {
                                user = jwtResult.Principal;
                                // Update the HttpContext.User
                                _contextAccessor.HttpContext.User = user;
                            }
                        }
                    }
                }
            }

            if (user == null || user.Identity == null || !user.Identity.IsAuthenticated)
                throw new Exception("Login user not found.");

            var id = _userManager.GetUserId(user);
            if (string.IsNullOrEmpty(id))
                throw new Exception("User ID not found in claims.");

            // Use repository to get the current user (which handles caching)
            var dbUser = await _userRepository.GetUserByGuidAsync(id);

            if (dbUser == null)
                throw new Exception($"User with ID {id} not found in database.");

            return dbUser;
        }

        /// <summary>
        /// Invalidates the cache for a specific user
        /// </summary>
        /// <param name="userId">The user ID</param>
        public void InvalidateUserCache(string userId)
        {
            _userRepository.InvalidateUserCache(userId);
            _logger.LogInformation("Cache invalidated for user {UserId}", userId);
        }

        public async Task<ApplicationUser> GetCurrentUserStdUpsertByAdminAsync()
        {
            var currentUser = await GetCurrentUserAsync();
            if (currentUser == null || (currentUser != null && currentUser.UserRoles.Any(x => !x.RoleId.Equals(UserTypeEnum.Admin.GetAmbientValue().Value.ToString()))))
                throw new Exception(StandardErrorMessage.AccessError);

            return currentUser;
        }

        /// <summary>
        /// Only allow Admin to do upsert with company
        /// </summary>
        /// <param name="reqBody"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<(ApplicationUser, dynamic)> GetCurrentUserStdAdminUpsertWithCompanyAsync(dynamic reqBody)
        {
            var currentUser = await GetCurrentUserAsync();
            if (currentUser == null || currentUser.UserRoles.Any(x => !x.RoleId.Equals(UserTypeEnum.Admin.GetAmbientValue().Value.ToString())))
            {
                throw new Exception(StandardErrorMessage.AccessError);
            }

            if (currentUser.UserRoles.Any(x => x.RoleId.Equals(UserTypeEnum.Admin.GetAmbientValue().Value.ToString())))
            {
                if (reqBody.CompanyId == null || reqBody.CompanyId == Guid.Empty)
                {
                    throw new Exception("Company Id is a mandatory field for Admin users.");
                }
            }
            else
            {
                // others than Admin, only allow get company Id from login user
                reqBody.CompanyId = (Guid)currentUser.CompanyId;
            }

            return (currentUser, reqBody);
        }

        /// <summary>
        /// Get user by company, if admin then company will be null
        /// </summary>
        /// <param name="companyId">The company ID to filter by</param>
        /// <returns>Tuple containing the current user and company ID</returns>
        /// <exception cref="Exception">Thrown when user is not found or access is denied</exception>
        public async Task<(ApplicationUser, Guid?)> GetCurrentUserStdGetWithCompanyAsync(Guid? companyId)
        {
            var currentUser = await GetCurrentUserAsync();
            if (currentUser == null)
                throw new Exception(StandardErrorMessage.AccessError);
            else if (currentUser.UserRoles.Any(x => !x.RoleId.Equals(UserTypeEnum.Admin.GetAmbientValue().Value.ToString())))
                companyId = (Guid)currentUser.CompanyId; // only allow to get own company profile

            return (currentUser, companyId);
        }

        /// <summary>
        /// Get user by branch, if admin then branch will be null
        /// </summary>
        /// <param name="branchId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<(ApplicationUser, Guid?)> GetCurrentUserStdGetWithBranchAsync(Guid? branchId)
        {
            var currentUser = await GetCurrentUserAsync();
            if (currentUser == null)
                throw new Exception(StandardErrorMessage.AccessError);
            else if (currentUser.UserRoles.Any(x => !x.RoleId.Equals(UserTypeEnum.Admin.GetAmbientValue().Value.ToString())))
                branchId = (Guid)currentUser.BranchId; // only allow to get own company profile

            return (currentUser, branchId);
        }

        /// <summary>
        /// Allow to do upsert others than admin roles and check by company
        /// </summary>
        /// <param name="reqBody"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<(ApplicationUser, dynamic)> GetCurrentUserStdUpsertWithCompanyAsync(dynamic reqBody)
        {
            var currentUser = await GetCurrentUserAsync();
            if (currentUser == null || currentUser.UserRoles.Any(x => x.RoleId.Equals(UserTypeEnum.User.GetAmbientValue().Value.ToString())))
            {
                throw new Exception(StandardErrorMessage.AccessError);
            }

            if (currentUser.UserRoles.Any(x => x.RoleId.Equals(UserTypeEnum.Admin.GetAmbientValue().Value.ToString())))
            {
                if (reqBody.CompanyId == null || reqBody.CompanyId == Guid.Empty)
                {
                    throw new Exception("Company Id is a mandatory field for Admin users.");
                }
            }
            else
            {
                // others than Admin, only allow get company Id from login user
                reqBody.CompanyId = (Guid)currentUser.CompanyId;
            }

            return (currentUser, reqBody);
        }

        /// <summary>
        /// Allow to do upsert others than admin roles and check by branch
        /// </summary>
        /// <param name="reqBody"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<(ApplicationUser, dynamic, Guid?)> GetCurrentUserStdUpsertWithBranchAsync(dynamic reqBody)
        {
            Guid? companyId = null;
            var currentUser = await GetCurrentUserAsync();
            if (currentUser == null || currentUser.UserRoles.Any(x => x.RoleId.Equals(UserTypeEnum.User.GetAmbientValue().Value.ToString())))
            {
                throw new Exception(StandardErrorMessage.AccessError);
            }

            if (currentUser.UserRoles.Any(x => x.RoleId.Equals(UserTypeEnum.Admin.GetAmbientValue().Value.ToString())))
            {
                if (reqBody.BranchId == null || reqBody.BranchId == Guid.Empty)
                {
                    throw new Exception("Branch Id is a mandatory field for Admin users.");
                }
                //// TODO should get branch's company
                Guid branchId = Guid.Parse(reqBody.BranchId.ToString());

                // Use repository to get branch
                var branchRepository = _unitOfWork.Repository<Branch>();
                var getBranchByGuid = await branchRepository.FirstOrDefaultAsync(
                    x => x.IsActive && x.Id == branchId,
                    b => b.Company);
                companyId = getBranchByGuid?.CompanyId;
            }
            else
            {
                // others than Admin, only allow get company Id from login user
                reqBody.BranchId = (Guid)currentUser.BranchId;
                companyId = (Guid)currentUser.CompanyId;
            }

            return (currentUser, reqBody, companyId);
        }


        public async Task<(ApplicationUser, dynamic)> GetCurrentUserStdAllUpsertWithCompanyAsync(dynamic reqBody)
        {
            var currentUser = await GetCurrentUserAsync();
            if (currentUser == null)
            {
                throw new Exception(StandardErrorMessage.AccessError);
            }

            if (currentUser.UserRoles.Any(x => x.RoleId.Equals(UserTypeEnum.Admin.GetAmbientValue().Value.ToString())))
            {
                if (reqBody.CompanyId == null || reqBody.CompanyId == Guid.Empty)
                {
                    throw new Exception("Company Id is a mandatory field for Admin users.");
                }
            }
            else
            {
                // others than Admin, only allow get company Id from login user
                reqBody.CompanyId = (Guid)currentUser.CompanyId;
            }

            return (currentUser, reqBody);
        }

        public async Task<(ApplicationUser, dynamic, Guid?)> GetCurrentUserStdAllUpsertWithBranchAsync(dynamic reqBody)
        {
            Guid? companyId = null;
            var currentUser = await GetCurrentUserAsync();
            if (currentUser == null)
            {
                throw new Exception(StandardErrorMessage.AccessError);
            }

            if (currentUser.UserRoles.Any(x => x.RoleId.Equals(UserTypeEnum.Admin.GetAmbientValue().Value.ToString())))
            {
                if (reqBody.BranchId == null || reqBody.BranchId == Guid.Empty)
                {
                    throw new Exception("Branch Id is a mandatory field for Admin users.");
                }
                //// TODO should get branch's company
                Guid branchId = Guid.Parse(reqBody.BranchId.ToString());

                // Use repository to get branch
                var branchRepository = _unitOfWork.Repository<Branch>();
                var getBranchByGuid = await branchRepository.FirstOrDefaultAsync(
                    x => x.IsActive && x.Id == branchId,
                    b => b.Company);
                companyId = getBranchByGuid?.CompanyId;
            }
            else
            {
                // others than Admin, only allow get company Id from login user
                reqBody.BranchId = (Guid)currentUser.BranchId;
                companyId = (Guid)currentUser.CompanyId;
            }

            return (currentUser, reqBody, companyId);
        }

        public async Task<BaseResponse<ResultId>> UserAccessValidatation(bool userTypeCheck = false)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };
            try
            {
                var user = await GetCurrentUserAsync();
                if (user == null)
                {
                    throw new Exception("User not exist");
                }
                var userRole = Guid.Parse(user.UserRoles.First().RoleId);
                if (userTypeCheck && (userRole == Enums.UserTypeEnum.User.GetAmbientValue().Value))
                {
                    throw new Exception("User no access.");
                }

                result.Result ??= new ResultId();
                result.Result.Id = Guid.Parse(user.Id);
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<NoResult, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }
    }
}
