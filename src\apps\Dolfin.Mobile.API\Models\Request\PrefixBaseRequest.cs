using Dolfin.Utility.Models;
using System.ComponentModel.DataAnnotations;

namespace Dolfin.Mobile.API.Models.Request
{
    /// <summary>
    /// Base request model for prefix operations
    /// </summary>
    public class PrefixBaseRequest
    {
        /// <summary>
        /// The prefix value to use (e.g., "CUST" for customers)
        /// </summary>
        [Required]
        public required string PrefixValue { get; set; }
        
        /// <summary>
        /// The padding length for the numeric part (default: 4)
        /// </summary>
        public int PaddingLength { get; set; } = 4;
    }
}
