﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Mobile.API.Models.Dto
{
    public partial class TransactionTypeDto
    {
        public TransactionTypeDto()
        {
            //Transaction = new HashSet<Transaction>();
            AccountGroupByPeriods = new HashSet<AccountGroupByPeriodDto>();
        }
        public required Guid Id { get; set; }
        public required string Code { get; set; }
        public required string Name { get; set; }
        //public virtual ICollection<Transaction> Transaction { get; }
        public virtual ICollection<AccountGroupByPeriodDto> AccountGroupByPeriods { get; }

    }
}
