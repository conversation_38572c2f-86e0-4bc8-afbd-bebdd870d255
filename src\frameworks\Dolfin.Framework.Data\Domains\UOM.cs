﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Framework.Data.Domains
{
    public partial class UOM : _BaseDomain
    {
        public UOM()
        {
            ProductUOMPrimary = new HashSet<ProductUOM>();
            ProductUOMSecondary = new HashSet<ProductUOM>();
        }
        public required string Code { get; set; }
        public required string Name { get; set; }
        public string? Description { get; set; }
        public Guid UOMCategoryId { get; set; }
        public virtual UOMCategories UOMCategory { get; set; }
        public virtual ICollection<ProductUOM> ProductUOMPrimary { get; }
        public virtual ICollection<ProductUOM> ProductUOMSecondary { get; }

    }
}
