﻿using AutoMapper;
using DocumentFormat.OpenXml.InkML;
using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Entity;
using Dolfin.Mobile.API.Helper;
using Dolfin.Mobile.API.Models.Request;
using Dolfin.Utility.Enum;
using Dolfin.Utility.Models;
using Dolfin.Utility.Utils;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using System.Linq.Expressions;
using static Dolfin.Utility.Enum.Enums;

namespace Dolfin.Mobile.API.Services
{
    public class TransactionService: BaseComponent<Transaction>, ITransactionService
    {
        private readonly StandardMessage _standardMessage;
        private readonly IUserService _userService;
        private readonly ILogger<TransactionService> _logger;
        private readonly IMapper _mapper;

        public TransactionService(DbContextOptions<DolfinDbContext> dbContextOptions, ISettingsService settingService, IUserService userService, ILoggerFactory loggerFactory, IMapper mapper) : base(dbContextOptions)
        {
            _standardMessage = new StandardMessage();
            _userService = userService;
            _logger = loggerFactory.CreateLogger<TransactionService>();
            _mapper = mapper;
        }

        public async Task<BaseResponse<PagedList<Transaction>>> GetTransactionList(Pagination pagination = null, CommonFilterList filterList = null, Guid? branchId = null)
        {
            var result = new BaseResponse<PagedList<Transaction>> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithBranchAsync(branchId);
                var currentUser = getCurrentUser.Item1;
                branchId = getCurrentUser.Item2;
                var companyId = currentUser.CompanyId;

                Expression<Func<Transaction, bool>> predicate = x => (x.Branch.CompanyId == companyId || (companyId == null && SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                        && (x.BranchId == branchId || SharedFunctionHelper.PermissionViewLimitCompanyOrBranchAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId))))
                        && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId))));

                if (filterList != null && filterList.FilterList != null && filterList.FilterList.Count > 0)
                    predicate = ExpressionExtensions.BuildPredicate(predicate, filterList);

                var query = GetDbContext().Set<Transaction>()
                    .Include(u => u.Branch).ThenInclude(u => u.Company)
                    .Include(u => u.Branch).ThenInclude(u => u.Address) // Include Address to prevent lazy loading
                    .Include(u => u.SalesTaxNo)
                    .Include(u => u.ServiceTaxNo)
                    .Include(u => u.Term)
                    .Include(u => u.AccountGroup)
                    .Include(u => u.TransactionType)
                    .Include(u => u.Customer)
                    .Include(u => u.User)
                    .Include(u => u.ShippingAddress)
                    .Include(u => u.BillingAddress)
                    .Include(u => u.TransactionStatus)
                    .Include(u => u.TransactionItem).ThenInclude(u => u.SalesTaxNo)
                    .Include(u => u.TransactionItem).ThenInclude(u => u.ServiceTaxNo)
                    .Include(u => u.TransactionItem).ThenInclude(u => u.ProductPrice)
                    .Include(u => u.TransactionItem).ThenInclude(u => u.ProductUOM)
                    .Include(u => u.TransactionItem).ThenInclude(u => u.Product)
                    .Include(u => u.TransactionPaid)
                    .Where(predicate)
                    .AsQueryable();

                var response = await query.ToListAsync();
                result.Result = SharedFunctionHelper.StandardPagination(response, pagination);
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<PagedList<Transaction>, PagedList<Transaction>>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<Transaction>> GetTransactionByGuid(Guid transactionId)
        {
            var result = new BaseResponse<Transaction> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
                var currentUser = getCurrentUser.Item1;
                var companyId = getCurrentUser.Item2;
                var branchId = currentUser.BranchId;

                var query = GetDbContext().Set<Transaction>()
                    .Include(u => u.Branch).ThenInclude(u => u.Company)
                    .Include(u => u.Branch).ThenInclude(u => u.Address) // Include Address to prevent lazy loading
                    .Include(u => u.SalesTaxNo)
                    .Include(u => u.ServiceTaxNo)
                    .Include(u => u.Term)
                    .Include(u => u.AccountGroup)
                    .Include(u => u.TransactionType)
                    .Include(u => u.Customer)
                    .Include(u => u.User)
                    .Include(u => u.ShippingAddress)
                    .Include(u => u.BillingAddress)
                    .Include(u => u.TransactionStatus)
                    .Include(u => u.TransactionItem).ThenInclude(u => u.SalesTaxNo)
                    .Include(u => u.TransactionItem).ThenInclude(u => u.ServiceTaxNo)
                    .Include(u => u.TransactionItem).ThenInclude(u => u.ProductPrice)
                    .Include(u => u.TransactionItem).ThenInclude(u => u.ProductUOM)
                    .Include(u => u.TransactionItem).ThenInclude(u => u.Product)
                    .Include(u => u.TransactionPaid)
                    .Where(x => x.Id == transactionId && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                    .Where(x => (x.Branch.CompanyId == companyId || (companyId == null && SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                        && (x.BranchId == branchId || SharedFunctionHelper.PermissionViewLimitCompanyOrBranchAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                    .AsQueryable();
                result.Result = await query.FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<Transaction, Transaction>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<ResultId>> InsertTransaction(TransactionRequest reqBody, DolfinDbContext dbContextRollback)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };

            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();
            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;

                try
                {
                    var getCurrentUser = await _userService.GetCurrentUserStdAllUpsertWithBranchAsync(reqBody);
                    var currentUser = getCurrentUser.Item1;
                    reqBody = getCurrentUser.Item2;

                    var newTransaction = _mapper.Map<Transaction>(reqBody);
                    newTransaction.IsActive = true;
                    newTransaction.CreatedAt = DateTime.UtcNow;
                    newTransaction.CreatedBy = Guid.Parse(currentUser.Id);
                    newTransaction.UserId = currentUser.Id.ToString();
                    var transactionCreation = await CreateAsync(newTransaction);
                    result.Result = new ResultId { Id = transactionCreation.Id };
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }
            return result;
        }

        public async Task<BaseResponse<TransactionItem>> GetTransactionItemByGuid(Guid trxId, Guid transactionItemId)
        {
            var result = new BaseResponse<TransactionItem> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
                var currentUser = getCurrentUser.Item1;
                var companyId = getCurrentUser.Item2;
                var branchId = currentUser.BranchId;

                var query = GetDbContext().Set<TransactionItem>()
                    .Include(t => t.Transaction).ThenInclude(t => t.Branch)
                    .Where(x => x.Id == transactionItemId && x.TrxId == trxId && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                    .Where(x => (x.Transaction.Branch.CompanyId == companyId || (companyId == null && SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                        && (x.Transaction.BranchId == branchId || SharedFunctionHelper.PermissionViewLimitCompanyOrBranchAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                    .AsQueryable();
                result.Result = await query.FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<TransactionItem, TransactionItem>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<ResultId>> InsertTransactionItem(TransactionItemRequest reqBody, DolfinDbContext dbContextRollback)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };

            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();
            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;

                try
                {
                    var getCurrentUser = await _userService.GetCurrentUserStdAllUpsertWithBranchAsync(reqBody);
                    var currentUser = getCurrentUser.Item1;
                    reqBody = getCurrentUser.Item2;

                    var newTransaction = _mapper.Map<TransactionItem>(reqBody);
                    newTransaction.IsActive = true;
                    newTransaction.CreatedAt = DateTime.UtcNow;
                    newTransaction.CreatedBy = Guid.Parse(currentUser.Id);
                    var transactionCreation = await CreateAsync(newTransaction);
                    result.Result = new ResultId { Id = transactionCreation.Id };
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }
            return result;
        }

        public async Task<BaseResponse<ResultId>> UpdateTransaction(UpdateTransactionRequest reqBody, DolfinDbContext dbContextRollback)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };

            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();
            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;

                try
                {
                    var getCurrentUser = await _userService.GetCurrentUserStdAllUpsertWithBranchAsync(reqBody);
                    var currentUser = getCurrentUser.Item1;
                    reqBody = getCurrentUser.Item2;

                    var transactionResponse = await GetTransactionByGuid((Guid)reqBody.Id);
                    if (!transactionResponse.IsSuccessful)
                        throw new Exception(transactionResponse.Exception);
                    else if (transactionResponse == null || transactionResponse.Result?.Id == null)
                        throw new Exception($"{reqBody.Id} not found.");
                    else
                    {
                        var existingTransaction = transactionResponse.Result;

                        // Update properties directly
                        existingTransaction.TrxDatetime = reqBody.TrxDatetime;

                        if (reqBody.CurrencyCode != null)
                            existingTransaction.CurrencyCode = reqBody.CurrencyCode;

                        existingTransaction.ExchangeRate = reqBody.ExchangeRate;
                        existingTransaction.TotalRoundingAdjustmentAmount = reqBody.TotalRoundingAdjustmentAmount;
                        existingTransaction.TotalAmount = reqBody.TotalAmount;
                        existingTransaction.TotalDiscount = reqBody.TotalDiscount;
                        existingTransaction.TotalExclTaxAmount = reqBody.TotalExclTax;
                        existingTransaction.TotalInclTaxAmount = reqBody.TotalInclTax;
                        existingTransaction.TotalSalesTaxAmount = reqBody.TotalSalesTaxAmount;
                        existingTransaction.TotalServiceTaxAmount = reqBody.TotalServiceTaxAmount;
                        existingTransaction.SalesTaxNoId = reqBody.SalesTaxNoId;
                        existingTransaction.ServiceTaxNoId = reqBody.ServiceTaxNoId;
                        existingTransaction.TotalPayableAmount = reqBody.TotalPayableAmount;

                        if (reqBody.PIC != null)
                            existingTransaction.PIC = reqBody.PIC;

                        if (reqBody.TermDay != null)
                            existingTransaction.TermDay = reqBody.TermDay;

                        existingTransaction.TermId = reqBody.TermId;
                        existingTransaction.AccountGroupId = reqBody.AccountGroupId;
                        existingTransaction.TransactionTypeId = reqBody.TransactionTypeId;
                        existingTransaction.BranchId = reqBody.BranchId;
                        existingTransaction.CustomerId = reqBody.CustomerId;

                        if (reqBody.UserId != null)
                            existingTransaction.UserId = reqBody.UserId;

                        existingTransaction.ShippingAddressId = reqBody.ShippingAddressId;
                        existingTransaction.BillingAddressId = reqBody.BillingAddressId;
                        existingTransaction.TransactionStatusId = reqBody.TransactionStatusId;

                        // Update audit fields
                        existingTransaction.UpdatedAt = DateTime.UtcNow;
                        existingTransaction.UpdatedBy = Guid.Parse(currentUser.Id);

                        var transactionUpdate = await UpdateAsync(existingTransaction);
                        result.Result = new ResultId { Id = transactionUpdate.Id };
                    }
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }
            return result;
        }

        public async Task<BaseResponse<ResultId>> UpdateTransactionItem(Guid trxId, UpsertTransactionItemRequest reqBody, DolfinDbContext dbContextRollback)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };

            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();
            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;

                try
                {
                    var getCurrentUser = await _userService.GetCurrentUserStdAllUpsertWithCompanyAsync(reqBody);
                    var currentUser = getCurrentUser.Item1;
                    reqBody = getCurrentUser.Item2;

                    var transactionItemResponse = await GetTransactionItemByGuid(trxId, (Guid)reqBody.Id);
                    if (!transactionItemResponse.IsSuccessful)
                        throw new Exception(transactionItemResponse.Exception);
                    else if (transactionItemResponse == null || transactionItemResponse.Result?.Id == null)
                        throw new Exception($"{reqBody.Id} not found.");
                    else
                    {
                        var existingTransactionItem = transactionItemResponse.Result;

                        // Update properties directly
                        if (reqBody.ProductUOMPrimaryMCode != null)
                            existingTransactionItem.ProductUOMPrimaryMCode = reqBody.ProductUOMPrimaryMCode;

                        if (reqBody.ProductUOMSecondaryMCode != null)
                            existingTransactionItem.ProductUOMSecondaryMCode = reqBody.ProductUOMSecondaryMCode;

                        existingTransactionItem.TransactionProductPrice = reqBody.TransactionProductPrice;
                        existingTransactionItem.FractionTotal = reqBody.FractionTotal;
                        existingTransactionItem.FractionQuantity = reqBody.FractionQuantity;
                        existingTransactionItem.Quantity = reqBody.Quantity;
                        existingTransactionItem.Discount = reqBody.Discount;
                        existingTransactionItem.SalesTaxAmount = reqBody.SalesTaxAmount;
                        existingTransactionItem.ServiceTaxAmount = reqBody.ServiceTaxAmount;
                        existingTransactionItem.SalesTaxNoId = reqBody.SalesTaxNoId;
                        existingTransactionItem.ServiceTaxNoId = reqBody.ServiceTaxNoId;
                        existingTransactionItem.ExclTaxAmount = reqBody.TaxExclAmount;
                        existingTransactionItem.InclTaxAmount = reqBody.TaxInclAmount;
                        existingTransactionItem.UnitAmount = reqBody.UnitAmount;
                        existingTransactionItem.TotalUnitAmount = reqBody.TotalUnitAmount;
                        existingTransactionItem.AdjAmount = reqBody.AdjAmount;
                        existingTransactionItem.ProductPriceId = reqBody.ProductPriceId;
                        existingTransactionItem.CustomerId = reqBody.CustomerId;
                        existingTransactionItem.ProductUOMId = reqBody.ProductUOMId;
                        existingTransactionItem.ProductId = reqBody.ProductId;

                        // Update audit fields
                        existingTransactionItem.UpdatedAt = DateTime.UtcNow;
                        existingTransactionItem.UpdatedBy = Guid.Parse(currentUser.Id);

                        var transactionItemUpdate = await UpdateAsync(existingTransactionItem);
                        result.Result = new ResultId { Id = transactionItemUpdate.Id };
                    }
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }
            return result;
        }

        public async Task<NoResultResponse> DeleteTransactionItem(Guid id, DolfinDbContext dbContextRollback = null)
        {
            var result = new NoResultResponse { IsSuccessful = true };

            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();

            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;

                try
                {
                    var currentUser = await _userService.GetCurrentUserAsync();

                    // Get existing transaction item
                    var transactionItem = await dbContext.Set<TransactionItem>()
                        .FirstOrDefaultAsync(x => x.Id == id && x.IsActive);

                    if (transactionItem == null)
                        throw new Exception($"Transaction item with ID {id} not found.");

                    // Soft delete by setting IsActive to false
                    transactionItem.IsActive = false;
                    transactionItem.UpdatedAt = DateTime.UtcNow;
                    transactionItem.UpdatedBy = Guid.Parse(currentUser.Id);

                    // Save changes
                    await dbContext.SaveChangesAsync();

                    if (currentTransaction == null)
                        await transaction.CommitAsync();
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    throw ex;
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<NoResult, NoResult>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }

            return result;
        }

        public async Task<NoResultResponse> DeleteTransaction(Guid id, DolfinDbContext dbContextRollback = null)
        {
            var result = new NoResultResponse { IsSuccessful = true };

            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();

            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;

                try
                {
                    var currentUser = await _userService.GetCurrentUserAsync();

                    // Get existing transaction paid
                    var transactionResponse = await GetTransactionByGuid(id);
                    if (!transactionResponse.IsSuccessful)
                        throw new Exception(transactionResponse.Exception);
                    else if (transactionResponse.Result == null || transactionResponse.Result.Id == Guid.Empty)
                        throw new Exception($"Transaction with ID {id} not found.");

                    var trans = transactionResponse.Result;

                    // Soft delete by setting IsActive to false
                    trans.IsActive = false;
                    trans.UpdatedAt = DateTime.UtcNow;
                    trans.UpdatedBy = Guid.Parse(currentUser.Id);

                    // Save changes
                    await dbContext.SaveChangesAsync();

                    if (currentTransaction == null)
                        await transaction.CommitAsync();
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    throw ex;
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<NoResult, NoResult>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }

            return result;
        }

        public async Task<BaseResponse<PagedList<TransactionStatus>>> GetTransactionStatusList(Pagination pagination = null)
        {
            var result = new BaseResponse<PagedList<TransactionStatus>> { IsSuccessful = true };
            try
            {
                UserTypeEnum? userType = null;
                var currentUser = await _userService.GetCurrentUserAsync();
                if (currentUser != null)
                    userType = Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId));

                var response = await GetTransactionStatusList(userType);
                result.Result = SharedFunctionHelper.StandardPagination(response, pagination);
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<PagedList<TransactionStatus>, PagedList<TransactionStatus>>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<PagedList<TransactionType>>> GetTransactionTypeList(Pagination pagination = null)
        {
            var result = new BaseResponse<PagedList<TransactionType>> { IsSuccessful = true };
            try
            {
                UserTypeEnum? userType = null;
                var currentUser = await _userService.GetCurrentUserAsync();
                if (currentUser != null)
                    userType = Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId));

                var response = await GetTransactionTypeList(userType);
                result.Result = SharedFunctionHelper.StandardPagination(response, pagination);
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<PagedList<TransactionType>, PagedList<TransactionType>>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        private async Task<List<TransactionStatus>> GetTransactionStatusList(UserTypeEnum? userType)
        {
            var query = GetDbContext().Set<TransactionStatus>().AsQueryable()
                .Where(x => x.IsActive || (userType != null && SharedFunctionHelper.PermissionViewAll((UserTypeEnum)userType)));

            return await query.ToListAsync();
        }

        private async Task<List<TransactionType>> GetTransactionTypeList(UserTypeEnum? userType)
        {
            var query = GetDbContext().Set<TransactionType>().AsQueryable()
                .Where(x => x.IsActive || (userType != null && SharedFunctionHelper.PermissionViewAll((UserTypeEnum)userType)));

            return await query.ToListAsync();
        }

        public async Task<BaseResponse<TransactionStatus>> GetTransactionStatusByGuid(Guid transactionStatusId)
        {
            var result = new BaseResponse<TransactionStatus> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserAsync();
                UserTypeEnum? userType = null;
                if (getCurrentUser != null)
                    userType = Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(getCurrentUser.UserRoles.First().RoleId));

                var query = GetDbContext().Set<TransactionStatus>()
                    .Where(x => x.Id == transactionStatusId &&
                           (x.IsActive || (userType != null && SharedFunctionHelper.PermissionViewAll((UserTypeEnum)userType))))
                    .AsQueryable();

                result.Result = await query.FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<TransactionStatus, TransactionStatus>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<TransactionType>> GetTransactionTypeByGuid(Guid transactionTypeId)
        {
            var result = new BaseResponse<TransactionType> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserAsync();
                UserTypeEnum? userType = null;
                if (getCurrentUser != null)
                    userType = Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(getCurrentUser.UserRoles.First().RoleId));

                var query = GetDbContext().Set<TransactionType>()
                    .Where(x => x.Id == transactionTypeId &&
                           (x.IsActive || (userType != null && SharedFunctionHelper.PermissionViewAll((UserTypeEnum)userType))))
                    .AsQueryable();

                result.Result = await query.FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<TransactionType, TransactionType>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<AccountGroup>> GetAccountGroupByGuid(Guid accountGroupId)
        {
            var result = new BaseResponse<AccountGroup> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserAsync();
                UserTypeEnum? userType = null;
                if (getCurrentUser != null)
                    userType = Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(getCurrentUser.UserRoles.First().RoleId));

                var query = GetDbContext().Set<AccountGroup>()
                    .Where(x => x.Id == accountGroupId &&
                           (x.IsActive || (userType != null && SharedFunctionHelper.PermissionViewAll((UserTypeEnum)userType))))
                    .AsQueryable();

                result.Result = await query.FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<AccountGroup, AccountGroup>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        #region TransactionPaid Methods
        public async Task<BaseResponse<PagedList<TransactionPaid>>> GetTransactionPaidList(Pagination pagination = null, CommonFilterList filterList = null, Guid? transactionId = null)
        {
            var result = new BaseResponse<PagedList<TransactionPaid>> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
                var currentUser = getCurrentUser.Item1;
                var companyId = currentUser.CompanyId;

                Expression<Func<TransactionPaid, bool>> predicate = x =>
                    x.IsActive &&
                    (transactionId == null || x.TrxId == transactionId) &&
                    x.Transaction.Branch.CompanyId == companyId;

                if (filterList != null && filterList.FilterList != null && filterList.FilterList.Count > 0)
                    predicate = ExpressionExtensions.BuildPredicate(predicate, filterList);

                var query = GetDbContext().Set<TransactionPaid>()
                    .Include(u => u.PaymentType)
                    .Include(u => u.Transaction)
                    .Where(predicate)
                    .AsQueryable();

                var response = await query.ToListAsync();

                result.Result = SharedFunctionHelper.StandardPagination(response, pagination);
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<PagedList<TransactionPaid>, PagedList<TransactionPaid>>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<TransactionPaid>> GetTransactionPaidByGuid(Guid transactionPaidId)
        {
            var result = new BaseResponse<TransactionPaid> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
                var currentUser = getCurrentUser.Item1;
                var companyId = currentUser.CompanyId;

                var query = GetDbContext().Set<TransactionPaid>()
                    .Include(u => u.PaymentType)
                    .Include(u => u.Transaction)
                    .Where(x => x.Id == transactionPaidId &&
                           x.IsActive &&
                           x.Transaction.Branch.CompanyId == companyId)
                    .AsQueryable();

                result.Result = await query.FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<TransactionPaid, TransactionPaid>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<ResultId>> InsertTransactionPaid(TransactionPaidRequest reqBody, DolfinDbContext dbContextRollback)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };

            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();

            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;

                try
                {
                    var getCurrentUser = await _userService.GetCurrentUserStdAllUpsertWithBranchAsync(reqBody);
                    var currentUser = getCurrentUser.Item1;
                    reqBody = getCurrentUser.Item2;

                    // Create new transaction paid entity
                    var transactionPaid = new TransactionPaid
                    {
                        PaidAmount = reqBody.PaidAmount,
                        PaidAt = DateTime.UtcNow,
                        PaymentTypeId = reqBody.PaymentTypeId,
                        TrxId = reqBody.TrxId,
                        Remark = reqBody.Remark ?? string.Empty,
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = Guid.Parse(currentUser.Id)
                    };

                    // Add to database
                    dbContext.TransactionPaid.Add(transactionPaid);
                    await dbContext.SaveChangesAsync();

                    result.Result = new ResultId { Id = transactionPaid.Id };

                    if (currentTransaction == null)
                        await transaction.CommitAsync();
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    throw ex;
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }

            return result;
        }

        public async Task<BaseResponse<ResultId>> UpdateTransactionPaid(UpdateTransactionPaidRequest reqBody, DolfinDbContext dbContextRollback)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };

            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();

            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;

                try
                {
                    var getCurrentUser = await _userService.GetCurrentUserStdAllUpsertWithCompanyAsync(reqBody);
                    var currentUser = getCurrentUser.Item1;
                    reqBody = getCurrentUser.Item2;

                    // Get existing transaction paid
                    var transactionPaidResponse = await GetTransactionPaidByGuid(reqBody.Id);
                    if (!transactionPaidResponse.IsSuccessful)
                        throw new Exception(transactionPaidResponse.Exception);
                    else if (transactionPaidResponse.Result == null || transactionPaidResponse.Result.Id == Guid.Empty)
                        throw new Exception($"Transaction payment with ID {reqBody.Id} not found.");

                    var transactionPaid = await dbContext.TransactionPaid
                        .Where(tp => tp.Id == reqBody.Id && tp.IsActive)
                        .FirstOrDefaultAsync();

                    // Update transaction paid entity
                    transactionPaid.PaidAmount = reqBody.PaidAmount;
                    transactionPaid.PaidAt = DateTime.UtcNow;
                    transactionPaid.PaymentTypeId = reqBody.PaymentTypeId;
                    transactionPaid.TrxId = reqBody.TrxId;

                    if (reqBody.Remark != null)
                        transactionPaid.Remark = reqBody.Remark;
                    else
                        transactionPaid.Remark = string.Empty;

                    transactionPaid.UpdatedAt = DateTime.UtcNow;
                    transactionPaid.UpdatedBy = Guid.Parse(currentUser.Id);

                    // Save changes
                    await dbContext.SaveChangesAsync();

                    result.Result = new ResultId { Id = transactionPaid.Id };

                    if (currentTransaction == null)
                        await transaction.CommitAsync();
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    throw ex;
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }

            return result;
        }

        public async Task<NoResultResponse> DeleteTransactionPaid(Guid id, DolfinDbContext dbContextRollback = null)
        {
            var result = new NoResultResponse { IsSuccessful = true };

            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();

            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;

                try
                {
                    var currentUser = await _userService.GetCurrentUserAsync();

                    // Get existing transaction paid
                    var transactionPaidResponse = await GetTransactionPaidByGuid(id);
                    if (!transactionPaidResponse.IsSuccessful)
                        throw new Exception(transactionPaidResponse.Exception);
                    else if (transactionPaidResponse.Result == null || transactionPaidResponse.Result.Id == Guid.Empty)
                        throw new Exception($"Transaction payment with ID {id} not found.");

                    var transactionPaid = transactionPaidResponse.Result;

                    // Soft delete by setting IsActive to false
                    transactionPaid.IsActive = false;
                    transactionPaid.UpdatedAt = DateTime.UtcNow;
                    transactionPaid.UpdatedBy = Guid.Parse(currentUser.Id);

                    // Save changes
                    await dbContext.SaveChangesAsync();

                    if (currentTransaction == null)
                        await transaction.CommitAsync();
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    throw ex;
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<NoResult, NoResult>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }

            return result;
        }
        #endregion

        #region PaymentType Methods
        public async Task<BaseResponse<PagedList<PaymentType>>> GetPaymentTypeList(Pagination pagination = null, CommonFilterList filterList = null, Guid? companyId = null)
        {
            var result = new BaseResponse<PagedList<PaymentType>> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
                var currentUser = getCurrentUser.Item1;

                // If companyId is not provided, use the current user's company ID
                if (companyId == null && currentUser.CompanyId != null)
                {
                    companyId = currentUser.CompanyId;
                }

                Expression<Func<PaymentType, bool>> predicate = x =>
                    x.IsActive ||
                    SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)));

                if (filterList != null && filterList.FilterList != null && filterList.FilterList.Count > 0)
                    predicate = ExpressionExtensions.BuildPredicate(predicate, filterList);

                var query = GetDbContext().Set<PaymentType>()
                    .Include(p => p.AccountGroup)
                    .Where(predicate)
                    .AsQueryable();

                var response = await query.ToListAsync();

                result.Result = SharedFunctionHelper.StandardPagination(response, pagination);
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<PagedList<PaymentType>, PagedList<PaymentType>>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<PaymentType>> GetPaymentTypeByGuid(Guid paymentTypeId)
        {
            var result = new BaseResponse<PaymentType> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
                var currentUser = getCurrentUser.Item1;

                var query = GetDbContext().Set<PaymentType>()
                    .Include(p => p.AccountGroup)
                    .Where(x => x.Id == paymentTypeId &&
                           (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                    .AsQueryable();

                result.Result = await query.FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<PaymentType, PaymentType>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<ResultId>> InsertPaymentType(PaymentTypeRequest reqBody, DolfinDbContext dbContextRollback = null)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };

            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();

            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;

                try
                {
                    var currentUser = await _userService.GetCurrentUserAsync();

                    // Validate account group if provided
                    if (reqBody.AccountGroupId.HasValue)
                    {
                        var accountGroup = await dbContext.AccountGroup
                            .Where(ag => ag.Id == reqBody.AccountGroupId.Value && ag.IsActive)
                            .FirstOrDefaultAsync();

                        if (accountGroup == null)
                        {
                            throw new Exception($"Account group with ID {reqBody.AccountGroupId} not found.");
                        }
                    }

                    // Create new payment type entity
                    var paymentType = new PaymentType
                    {
                        Code = reqBody.Code,
                        Name = reqBody.Name,
                        AccountCode = reqBody.AccountCode,
                        AccountGroupId = reqBody.AccountGroupId,
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = Guid.Parse(currentUser.Id)
                    };

                    // Add to database
                    dbContext.PaymentType.Add(paymentType);
                    await dbContext.SaveChangesAsync();

                    result.Result = new ResultId { Id = paymentType.Id };

                    if (currentTransaction == null)
                        await transaction.CommitAsync();
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    throw ex;
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }

            return result;
        }

        public async Task<BaseResponse<ResultId>> UpdatePaymentType(UpdatePaymentTypeRequest reqBody, DolfinDbContext dbContextRollback = null)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };

            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();

            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;

                try
                {
                    var currentUser = await _userService.GetCurrentUserAsync();

                    // Get existing payment type
                    var paymentTypeResponse = await GetPaymentTypeByGuid(reqBody.Id);
                    if (!paymentTypeResponse.IsSuccessful)
                        throw new Exception(paymentTypeResponse.Exception);
                    else if (paymentTypeResponse.Result == null || paymentTypeResponse.Result.Id == Guid.Empty)
                        throw new Exception($"Payment type with ID {reqBody.Id} not found.");

                    var paymentType = await dbContext.PaymentType
                        .Where(pt => pt.Id == reqBody.Id && pt.IsActive)
                        .FirstOrDefaultAsync();

                    // Validate account group if provided
                    if (reqBody.AccountGroupId.HasValue)
                    {
                        // Get account group using a service method (assuming one exists)
                        // If there's no GetAccountGroupByGuid method, we'll keep the direct query
                        var accountGroup = await dbContext.AccountGroup
                            .Where(ag => ag.Id == reqBody.AccountGroupId.Value && ag.IsActive)
                            .FirstOrDefaultAsync();

                        if (accountGroup == null)
                        {
                            throw new Exception($"Account group with ID {reqBody.AccountGroupId} not found.");
                        }
                    }

                    // Update payment type entity
                    if (reqBody.Code != null)
                        paymentType.Code = reqBody.Code;

                    if (reqBody.Name != null)
                        paymentType.Name = reqBody.Name;

                    if (reqBody.AccountCode != null)
                        paymentType.AccountCode = reqBody.AccountCode;

                    if (reqBody.AccountGroupId.HasValue)
                        paymentType.AccountGroupId = reqBody.AccountGroupId;

                    paymentType.UpdatedAt = DateTime.UtcNow;
                    paymentType.UpdatedBy = Guid.Parse(currentUser.Id);

                    // Save changes
                    await dbContext.SaveChangesAsync();

                    result.Result = new ResultId { Id = paymentType.Id };

                    if (currentTransaction == null)
                        await transaction.CommitAsync();
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    throw ex;
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }

            return result;
        }

        public async Task<NoResultResponse> DeletePaymentType(Guid id, DolfinDbContext dbContextRollback = null)
        {
            var result = new NoResultResponse { IsSuccessful = true };

            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();

            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;

                try
                {
                    var currentUser = await _userService.GetCurrentUserAsync();

                    // Get existing payment type
                    var paymentTypeResponse = await GetPaymentTypeByGuid(id);
                    if (!paymentTypeResponse.IsSuccessful)
                        throw new Exception(paymentTypeResponse.Exception);
                    else if (paymentTypeResponse.Result == null || paymentTypeResponse.Result.Id == Guid.Empty)
                        throw new Exception($"Payment type with ID {id} not found.");

                    var paymentType = await dbContext.PaymentType
                        .Where(pt => pt.Id == id && pt.IsActive)
                        .FirstOrDefaultAsync();

                    // Check if payment type is in use
                    var isInUse = await dbContext.TransactionPaid
                        .AnyAsync(tp => tp.PaymentTypeId == id && tp.IsActive);

                    if (isInUse)
                    {
                        throw new Exception($"Cannot delete payment type with ID {id} because it is in use by one or more transactions.");
                    }

                    // Soft delete by setting IsActive to false
                    paymentType.IsActive = false;
                    paymentType.UpdatedAt = DateTime.UtcNow;
                    paymentType.UpdatedBy = Guid.Parse(currentUser.Id);

                    // Save changes
                    await dbContext.SaveChangesAsync();

                    if (currentTransaction == null)
                        await transaction.CommitAsync();
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    throw ex;
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<NoResult, NoResult>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }

            return result;
        }
        #endregion

        public DolfinDbContext CreateDbContext()
        {
            return GetDbContext();
        }
    }
}
