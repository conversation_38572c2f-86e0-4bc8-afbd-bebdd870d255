﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Dolfin.Utility.Enum.Enums;

namespace Dolfin.Framework.Data.Domains
{
    public partial class EInvoice : _BaseDomain
    {
		public EInvoice()
		{
		}

		public Guid CompanyId { get; set; }
		public string InternalNo { get; set; }
		public string InvoiceNo { get; set; } = string.Empty; // Unique Invoice Number / codeNumber
		public Guid TransactionId { get; set; }
		public Guid CustomerId { get; set; }
		public string? DocumentType { get; set; } // The document type
		public string? DocumentTypeVersion { get; set; } // The document type version
		public string? DocumentXml { get; set; } // The document XML
		public string? DocumentJson { get; set; } // The document Json (Not in UBL format)
		public string? DocumentBase64 { get; set; } // The base64 of the document XML
		public string? DocumentHash { get; set; } // The hash value of the document being submitted
		public string? SubmissionUID { get; set; } // Unique ID of the submission from e-invoice
		public string? DocumentUUID { get; set; } // Document ID assigned by e-Invoice (Use to generate QR Code)
		public string? DocumentLongId { get; set; } // Unique long temporary Id assigned by e-Invoice (Use to generate QR Code)
		public string? DocumentToken { get; set; }
        public EInvoiceStatusEnum Status { get; set; } = EInvoiceStatusEnum.Pending;

		public int? RetryCount { get; set; }
		public string? CancelReason { get; set; }
		public string? RejectReason { get; set; }
		public DateTime? SubmittedDate { get; set; }
		public DateTime? CancelledDate { get; set; }
		public DateTime? RejectedDate { get; set; }
		public DateTime? AcceptedDate { get; set; }
		public string? ResponseCode { get; set; } // Gov API Response Code
		public string? ResponseMessage { get; set; } // Gov API Response Message
		public string? QrCodeUrl { get; set; } // URL to the QR code image
		public string? DocumentUrl { get; set; } // URL to view the document

        public virtual Company Company { get; set; }
        public virtual Customer Customer { get; set; }
		public virtual Transaction Transaction { get; set; }
	}
}
