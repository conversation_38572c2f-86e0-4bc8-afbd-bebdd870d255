﻿using Dolfin.Framework.Data.Domains.CustomIdentity;
using OrchardCore.Modules;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Framework.Data.Domains
{
    public partial class Branch : _BaseDomain
    {
        public Branch() {
            AccountGroupByPeriods = new HashSet<AccountGroupByPeriod>(); 
            Transaction = new HashSet<Transaction>();
            Inventory = new HashSet<Inventory>();
            User = new HashSet<ApplicationUser>();
        }

        public required string Code { get; set; }
        public required string Name { get; set; }
        public bool IsHq { get; set; }
        public Guid AddressId { get; set; }
        public Guid CompanyId { get; set; }
        public virtual Address Address { get; set; }
        public virtual Company Company { get; set; }
        public virtual ICollection<AccountGroupByPeriod> AccountGroupByPeriods { get; }
        public virtual ICollection<Transaction> Transaction { get; }
        public virtual ICollection<Inventory> Inventory { get; }
        public virtual ICollection<ApplicationUser> User { get; }
    }
}
