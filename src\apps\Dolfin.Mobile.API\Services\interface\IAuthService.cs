﻿using Dolfin.Framework.Data.Domains.CustomIdentity;
using Dolfin.Framework.Data.Entity;
using Dolfin.Mobile.API.Models.Request;
using Dolfin.Mobile.API.Models.Response;
using Dolfin.Utility.Models;
using Microsoft.AspNetCore.Identity;

namespace Dolfin.Mobile.API.Services
{
    public interface IAuthService
    {
        Task<(BaseResponse<ResultMessage> result, string userId)> RegisterAsync(Register model, Guid? userId = null, DolfinDbContext dbContextRollback = null);
        Task<BaseResponse<ResultMessage>> UpdateAsync(Register model, string userId, DolfinDbContext dbContextRollback = null);
        Task<BaseResponse<AuthResultDto>> LoginAsync(Login model);
        Task<BaseResponse<ResultMessage>> LogoutAsync();
        Task<BaseResponse<ResultMessage>> ConfirmEmailAsync(string userId, string code);
        Task<BaseResponse<ResultMessage>> ResendConfirmationEmailAsync(string userId);
        Task<BaseResponse<ResultMessage>> ForgotPasswordAsync(ForgotPassword model);
        Task<BaseResponse<ResultMessage>> ResetPasswordAsync(ResetPassword model);

        /// <summary>
        /// Refresh an access token using a refresh token
        /// </summary>
        /// <param name="refreshToken">The refresh token</param>
        /// <param name="accessToken">The expired access token</param>
        /// <returns>A response containing a new access token and refresh token</returns>
        Task<BaseResponse<AuthResultDto>> RefreshTokenAsync(string refreshToken, string? accessToken);

        /// <summary>
        /// Revoke a refresh token, preventing it from being used to obtain new access tokens
        /// </summary>
        /// <param name="token">The refresh token to revoke</param>
        /// <param name="reason">Optional reason for revocation</param>
        /// <returns>A response indicating success or failure</returns>
        Task<BaseResponse<ResultMessage>> RevokeTokenAsync(string token, string? reason = null);
        Task<BaseResponse<ResultMessage>> ChangePasswordAsync(string userId, ChangePassword model);
    }
}
