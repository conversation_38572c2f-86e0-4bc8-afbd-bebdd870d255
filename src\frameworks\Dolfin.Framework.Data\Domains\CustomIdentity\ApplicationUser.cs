﻿using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Framework.Data.Domains.CustomIdentity
{
    public class ApplicationUser : IdentityUser
    {
        public bool IsAllowEditable { get; set; }
        public DateTime PasswordExpireAt { get; set; }
        public required string PhoneNo1 { get; set; }
        public string? PhoneNo2 { get; set; }
        public string? FaxNo1 { get; set; }
        public string? FaxNo2 { get; set; }
        public string? SerialNo { get; set; }
        public DateTime LastLoginAt { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public Guid CreatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public Guid? UpdatedBy { get; set; }
        public Guid? BranchId { get; set; }
        public Guid? CompanyId { get; set; }
        public virtual Branch? Branch { get; set; }
        public virtual Company? Company { get; set; }
        public virtual ICollection<Transaction> Transaction { get; }
        public virtual ICollection<IdentityUserClaim<string>> Claims { get; set; }
        public virtual ICollection<IdentityUserLogin<string>> Logins { get; set; }
        public virtual ICollection<IdentityUserToken<string>> Tokens { get; set; }
        public virtual ICollection<ApplicationUserRole> UserRoles { get; set; }
    }

    public class ApplicationRole : IdentityRole
    {
        public required string TargetType { get; set; }
        public bool Editable { get; set; }
        public bool PartailEditable { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public Guid CreatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public Guid? UpdatedBy { get; set; }
        public virtual ICollection<ApplicationUserRole> UserRoles { get; set; }
    }

    public class ApplicationUserRole : IdentityUserRole<string>
    {
        public virtual ApplicationUser User { get; set; }
        public virtual ApplicationRole Role { get; set; }
    }
}
