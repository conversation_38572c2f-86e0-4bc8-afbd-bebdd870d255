﻿﻿using Microsoft.AspNetCore.Http;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using static Dolfin.Mobile.API.Constants.Constants;

namespace Dolfin.Mobile.API.Models.Request
{
    /// <summary>
    /// Request model for uploading a file
    /// </summary>
    public class FileUploadRequest
    {
        /// <summary>
        /// The file to upload
        /// </summary>
        [Required(ErrorMessage = "File is required")]
        public IFormFile File { get; set; }

        /// <summary>
        /// Optional custom filename (if not provided, the original filename will be used)
        /// </summary>
        public string? FileName { get; set; }

        /// <summary>
        /// The module this file belongs to (e.g., "product", "customer", etc.)
        /// </summary>
        [Required(ErrorMessage = "Module is required")]
        public string Module { get; set; }

        /// <summary>
        /// Optional URL of a file to delete after successful upload (for replacing files)
        /// </summary>
        public string? DeleteFileUrl { get; set; }

        /// <summary>
        /// Optional unique identifier for organizing files (e.g., product ID, customer ID)
        /// </summary>
        [Required(ErrorMessage = "Unique Key is required")]
        public Guid UniqueKey { get; set; }
    }
}
