﻿namespace Dolfin.Mobile.API.Models
{
    public class EInvoiceRequest
    {
        public string InvoiceNo { get; set; }
        public DateTime Date { get; set; }
        public Seller Seller { get; set; }
        public Buyer Buyer { get; set; }
        public List<InvoiceItem> Items { get; set; }
    }

    public class Seller
    {
        public string Name { get; set; }
        public string TaxNo { get; set; }
    }

    public class Buyer
    {
        public string Name { get; set; }
        public string TaxNo { get; set; }
    }

    public class InvoiceItem
    {
        public string Description { get; set; }
        public int Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TaxRate { get; set; }
    }
}