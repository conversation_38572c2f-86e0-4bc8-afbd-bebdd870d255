﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Dolfin.Framework.Data.Migrations
{
    /// <inheritdoc />
    public partial class EInvoiceTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "EInvoiceSyncAt",
                table: "Transaction");

            migrationBuilder.DropColumn(
                name: "EInvoiceSyncPayload",
                table: "Transaction");

            migrationBuilder.DropColumn(
                name: "EInvoiceSyncResponse",
                table: "Transaction");

            migrationBuilder.DropColumn(
                name: "EInvoiceSyncRetryCount",
                table: "Transaction");

            migrationBuilder.DropColumn(
                name: "SignatureUBL21",
                table: "Transaction");

            migrationBuilder.AddColumn<string>(
                name: "Email",
                table: "Customer",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FullName",
                table: "Customer",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "IdentityNo",
                table: "Customer",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "IdentityTypeId",
                table: "Customer",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TinNo",
                table: "Customer",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "TinVerifyStatus",
                table: "Customer",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "EInvoiceClientId",
                table: "Company",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EInvoiceClientSecret",
                table: "Company",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EInvoiceToken",
                table: "Company",
                type: "text",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "EInvoice",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CompanyId = table.Column<Guid>(type: "uuid", nullable: false),
                    InternalNo = table.Column<string>(type: "text", nullable: false),
                    InvoiceNo = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    TransactionId = table.Column<Guid>(type: "uuid", nullable: false),
                    CustomerId = table.Column<Guid>(type: "uuid", nullable: false),
                    DocumentType = table.Column<string>(type: "text", nullable: true),
                    DocumentTypeVersion = table.Column<string>(type: "text", nullable: true),
                    DocumentXml = table.Column<string>(type: "text", nullable: true),
                    DocumentJson = table.Column<string>(type: "text", nullable: true),
                    DocumentBase64 = table.Column<string>(type: "text", nullable: true),
                    DocumentHash = table.Column<string>(type: "text", nullable: true),
                    SubmissionUID = table.Column<string>(type: "text", nullable: true),
                    DocumentUUID = table.Column<string>(type: "text", nullable: true),
                    DocumentLongId = table.Column<string>(type: "text", nullable: true),
                    Status = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    RetryCount = table.Column<int>(type: "integer", nullable: true),
                    CancelReason = table.Column<string>(type: "text", nullable: true),
                    RejectReason = table.Column<string>(type: "text", nullable: true),
                    SubmittedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CancelledDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RejectedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    AcceptedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ResponseCode = table.Column<string>(type: "text", nullable: true),
                    ResponseMessage = table.Column<string>(type: "text", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValueSql: "true"),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "now()"),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EInvoice", x => x.Id);
                    table.ForeignKey(
                        name: "FK_EInvoice_Customer",
                        column: x => x.CustomerId,
                        principalTable: "Customer",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_EInvoice_Transaction",
                        column: x => x.TransactionId,
                        principalTable: "Transaction",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "EInvoiceSourceUpload",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CompanyId = table.Column<Guid>(type: "uuid", nullable: false),
                    FileUrl = table.Column<string>(type: "text", nullable: false),
                    FileName = table.Column<string>(type: "text", nullable: true),
                    MimeType = table.Column<string>(type: "text", nullable: true),
                    SourceUploadStatus = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    ProcessedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LogDetails = table.Column<string>(type: "text", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValueSql: "true"),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "now()"),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EInvoiceSourceUpload", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "IdentityType",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Code = table.Column<string>(type: "text", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Format = table.Column<string>(type: "text", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValueSql: "true"),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "now()"),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_IdentityType", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "TraceLogIntegration",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TransactionId = table.Column<string>(type: "text", nullable: true),
                    Url = table.Column<string>(type: "text", nullable: false),
                    Type = table.Column<string>(type: "text", nullable: false),
                    Request = table.Column<string>(type: "text", nullable: true),
                    ResponseCode = table.Column<string>(type: "text", nullable: true),
                    Response = table.Column<string>(type: "text", nullable: true),
                    StartTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    EndTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValueSql: "true"),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "now()"),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TraceLogIntegration", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "EInvoiceErrorLog",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    EInvoiceId = table.Column<Guid>(type: "uuid", nullable: false),
                    SubmissionUID = table.Column<string>(type: "text", nullable: true),
                    ErrorMessage = table.Column<string>(type: "text", nullable: false),
                    ErrorCode = table.Column<string>(type: "text", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValueSql: "true"),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "now()"),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EInvoiceErrorLog", x => x.Id);
                    table.ForeignKey(
                        name: "FK_EInvoiceErrorLog_EInvoice",
                        column: x => x.EInvoiceId,
                        principalTable: "EInvoice",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.UpdateData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("69ed35dc-8775-4641-9f1d-26d9d91e5f4b"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 15, 6, 21, 44, 163, DateTimeKind.Utc).AddTicks(7078));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("11fd5e73-51fa-428a-a919-960ee860b00f"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 15, 6, 21, 44, 163, DateTimeKind.Utc).AddTicks(7178));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("5d22874b-5834-40a2-81be-bf5100eb845d"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 15, 6, 21, 44, 163, DateTimeKind.Utc).AddTicks(7169));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("c219bf33-28d0-4595-a25b-8018bd87e67a"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 15, 6, 21, 44, 163, DateTimeKind.Utc).AddTicks(7167));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("e57e65d0-85a9-49c8-8463-3424a42e1f01"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 15, 6, 21, 44, 163, DateTimeKind.Utc).AddTicks(7176));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 15, 6, 21, 44, 163, DateTimeKind.Utc).AddTicks(7133));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("81ba4893-d891-4e3c-a5e7-ba13c697bf97"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 15, 6, 21, 44, 163, DateTimeKind.Utc).AddTicks(7139));

            migrationBuilder.CreateIndex(
                name: "IX_Customer_IdentityTypeId",
                table: "Customer",
                column: "IdentityTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_EInvoice_CustomerId",
                table: "EInvoice",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_EInvoice_TransactionId",
                table: "EInvoice",
                column: "TransactionId");

            migrationBuilder.CreateIndex(
                name: "IX_EInvoiceErrorLog_EInvoiceId",
                table: "EInvoiceErrorLog",
                column: "EInvoiceId");

            migrationBuilder.AddForeignKey(
                name: "FK_Customer_IdentityType",
                table: "Customer",
                column: "IdentityTypeId",
                principalTable: "IdentityType",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Customer_IdentityType",
                table: "Customer");

            migrationBuilder.DropTable(
                name: "EInvoiceErrorLog");

            migrationBuilder.DropTable(
                name: "EInvoiceSourceUpload");

            migrationBuilder.DropTable(
                name: "IdentityType");

            migrationBuilder.DropTable(
                name: "TraceLogIntegration");

            migrationBuilder.DropTable(
                name: "EInvoice");

            migrationBuilder.DropIndex(
                name: "IX_Customer_IdentityTypeId",
                table: "Customer");

            migrationBuilder.DropColumn(
                name: "Email",
                table: "Customer");

            migrationBuilder.DropColumn(
                name: "FullName",
                table: "Customer");

            migrationBuilder.DropColumn(
                name: "IdentityNo",
                table: "Customer");

            migrationBuilder.DropColumn(
                name: "IdentityTypeId",
                table: "Customer");

            migrationBuilder.DropColumn(
                name: "TinNo",
                table: "Customer");

            migrationBuilder.DropColumn(
                name: "TinVerifyStatus",
                table: "Customer");

            migrationBuilder.DropColumn(
                name: "EInvoiceClientId",
                table: "Company");

            migrationBuilder.DropColumn(
                name: "EInvoiceClientSecret",
                table: "Company");

            migrationBuilder.DropColumn(
                name: "EInvoiceToken",
                table: "Company");

            migrationBuilder.AddColumn<DateTime>(
                name: "EInvoiceSyncAt",
                table: "Transaction",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EInvoiceSyncPayload",
                table: "Transaction",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EInvoiceSyncResponse",
                table: "Transaction",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "EInvoiceSyncRetryCount",
                table: "Transaction",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SignatureUBL21",
                table: "Transaction",
                type: "text",
                nullable: true);

            migrationBuilder.UpdateData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("69ed35dc-8775-4641-9f1d-26d9d91e5f4b"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 14, 18, 14, 21, 588, DateTimeKind.Utc).AddTicks(3900));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("11fd5e73-51fa-428a-a919-960ee860b00f"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 14, 18, 14, 21, 588, DateTimeKind.Utc).AddTicks(3996));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("5d22874b-5834-40a2-81be-bf5100eb845d"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 14, 18, 14, 21, 588, DateTimeKind.Utc).AddTicks(3991));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("c219bf33-28d0-4595-a25b-8018bd87e67a"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 14, 18, 14, 21, 588, DateTimeKind.Utc).AddTicks(3989));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("e57e65d0-85a9-49c8-8463-3424a42e1f01"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 14, 18, 14, 21, 588, DateTimeKind.Utc).AddTicks(3994));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 14, 18, 14, 21, 588, DateTimeKind.Utc).AddTicks(3951));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("81ba4893-d891-4e3c-a5e7-ba13c697bf97"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 14, 18, 14, 21, 588, DateTimeKind.Utc).AddTicks(3957));
        }
    }
}
