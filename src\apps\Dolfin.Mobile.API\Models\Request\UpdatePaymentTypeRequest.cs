﻿﻿using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Dolfin.Mobile.API.Models.Request
{
    /// <summary>
    /// Request model for updating an existing payment type
    /// </summary>
    public class UpdatePaymentTypeRequest
    {
        /// <summary>
        /// The ID of the payment type to update
        /// </summary>
        [Required]
        public Guid Id { get; set; }

        /// <summary>
        /// The code for the payment type
        /// </summary>
        [Required]
        public string Code { get; set; }

        /// <summary>
        /// The name of the payment type
        /// </summary>
        [Required]
        public string Name { get; set; }

        /// <summary>
        /// The account code for the payment type
        /// </summary>
        public string? AccountCode { get; set; }

        /// <summary>
        /// The ID of the account group this payment type belongs to
        /// </summary>
        public Guid? AccountGroupId { get; set; }
    }
}
