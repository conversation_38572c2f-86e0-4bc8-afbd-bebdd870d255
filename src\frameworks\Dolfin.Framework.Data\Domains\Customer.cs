﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Dolfin.Utility.Enum.Enums;

namespace Dolfin.Framework.Data.Domains
{
    public partial class Customer : _BaseDomain
    {
        public Customer()
        {
            CustomerReferral = new HashSet<Customer>();
            Transaction = new HashSet<Transaction>();
            EInvoice = new HashSet<EInvoice>();
            //TransactionItem = new HashSet<TransactionItem>();
        }
        public required string Code { get; set; }
        public required string Name { get; set; }
        public string? Description { get; set; }
        public bool IsTaxExempt { get; set; }
        public bool IsPICEditable { get; set; }
        public string DefaultPIC { get; set; }
        public string ReferralCode { get; set; }
        public required string AccountCode { get; set; }
        public string? Remark { get; set; }
        public Guid? IdentityTypeId { get; set; }
		public string? IdentityNo { get; set; }
		public string? FullName { get; set; }
		public string? Email { get; set; }
		public string? TinNo { get; set; }
		public TinVerifyStatusEnum TinVerifyStatus { get; set; } = TinVerifyStatusEnum.Unverified;
		public Guid DebtorTypeId { get; set; }
        public Guid? ReferrerId { get; set; }
        public Guid? AddressId { get; set; }
        public Guid CurrencyId { get; set; }
        public Guid? AccountGroupId { get; set; }
        public Guid CompanyId { get; set; }
        public virtual DebtorType? DebtorType { get; set; }
        public virtual Customer? Referrer { get; set; }
        public virtual Address? Address { get; set; }
        public virtual Currency? Currency { get; set; }
        public virtual AccountGroup? AccountGroup { get; set; }
		public virtual IdentityType? IdentityType { get; set; }
        public virtual Company Company { get; set; }
        public virtual ICollection<Customer> CustomerReferral { get; }
        public virtual ICollection<Transaction> Transaction { get; }
        public virtual ICollection<EInvoice> EInvoice { get; }
        //public virtual ICollection<TransactionItem> TransactionItem { get; }

    }
}
