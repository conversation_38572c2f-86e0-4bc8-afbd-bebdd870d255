﻿using System;
using System.Xml.Serialization;
using System.Collections.Generic;

namespace Dolfin.Utility.Models.UBLInvoice
{
	[XmlRoot("Invoice", Namespace = "urn:oasis:names:specification:ubl:schema:xsd:Invoice-2")]
	public class Invoice
	{
		[XmlElement("UBLExtensions")]
		public UBLExtensions UBLExtensions { get; set; }

		[XmlElement("cbc:ID")]
		public string ID { get; set; }

		[XmlElement("cbc:IssueDate")]
		public string IssueDate { get; set; }

		[XmlElement("cbc:IssueTime")]
		public string IssueTime { get; set; }

		[XmlElement("cbc:InvoiceTypeCode")]
		public string InvoiceTypeCode { get; set; }

		[XmlElement("cbc:DocumentCurrencyCode")]
		public string DocumentCurrencyCode { get; set; }

		[XmlElement("cbc:TaxCurrencyCode")]
		public string TaxCurrencyCode { get; set; }

		[XmlElement("cac:InvoicePeriod")]
		public InvoicePeriod InvoicePeriod { get; set; }

		[XmlElement("cac:BillingReference")]
		public BillingReference BillingReference { get; set; }

		[XmlElement("cac:AdditionalDocumentReference")]
		public List<AdditionalDocumentReference> AdditionalDocumentReferences { get; set; }

		[XmlElement("cac:Signature")]
		public Signature Signature { get; set; }

		[XmlElement("cac:AccountingSupplierParty")]
		public AccountingSupplierParty AccountingSupplierParty { get; set; }

		[XmlElement("cac:AccountingCustomerParty")]
		public AccountingCustomerParty AccountingCustomerParty { get; set; }

		[XmlElement("cac:Delivery")]
		public Delivery Delivery { get; set; }
	}

	public class UBLExtensions
	{
		[XmlElement("UBLExtension")]
		public UBLExtension UBLExtension { get; set; }
	}

	public class UBLExtension
	{
		[XmlElement("ExtensionURI")]
		public string ExtensionURI { get; set; }

		[XmlElement("ExtensionContent")]
		public ExtensionContent ExtensionContent { get; set; }
	}

	public class ExtensionContent
	{
		[XmlElement("sig:UBLDocumentSignatures")]
		public UBLDocumentSignatures UBLDocumentSignatures { get; set; }
	}

	public class UBLDocumentSignatures
	{
		[XmlElement("sac:SignatureInformation")]
		public SignatureInformation SignatureInformation { get; set; }
	}

	public class SignatureInformation
	{
		[XmlElement("cbc:ID")]
		public string ID { get; set; }

		[XmlElement("sbc:ReferencedSignatureID")]
		public string ReferencedSignatureID { get; set; }

		[XmlElement("ds:Signature")]
		public Signature Signature { get; set; }
	}

	public class Signature
	{
		[XmlElement("ds:SignedInfo")]
		public SignedInfo SignedInfo { get; set; }

		[XmlElement("ds:SignatureValue")]
		public string SignatureValue { get; set; }

		[XmlElement("ds:KeyInfo")]
		public KeyInfo KeyInfo { get; set; }

		[XmlElement("ds:Object")]
		public Object Object { get; set; }
	}

	public class SignedInfo
	{
		[XmlElement("ds:CanonicalizationMethod")]
		public CanonicalizationMethod CanonicalizationMethod { get; set; }

		[XmlElement("ds:SignatureMethod")]
		public SignatureMethod SignatureMethod { get; set; }

		[XmlElement("ds:Reference")]
		public List<Reference> References { get; set; }
	}

	public class CanonicalizationMethod
	{
		[XmlAttribute("Algorithm")]
		public string Algorithm { get; set; }
	}

	public class SignatureMethod
	{
		[XmlAttribute("Algorithm")]
		public string Algorithm { get; set; }
	}

	public class Reference
	{
		[XmlAttribute("Id")]
		public string Id { get; set; }

		[XmlAttribute("URI")]
		public string URI { get; set; }

		[XmlElement("ds:Transforms")]
		public Transforms Transforms { get; set; }

		[XmlElement("ds:DigestMethod")]
		public DigestMethod DigestMethod { get; set; }

		[XmlElement("ds:DigestValue")]
		public string DigestValue { get; set; }
	}

	public class Transforms
	{
		[XmlElement("ds:Transform")]
		public List<Transform> TransformList { get; set; }
	}

	public class Transform
	{
		[XmlAttribute("Algorithm")]
		public string Algorithm { get; set; }

		[XmlElement("ds:XPath")]
		public string XPath { get; set; }
	}

	public class DigestMethod
	{
		[XmlAttribute("Algorithm")]
		public string Algorithm { get; set; }
	}

	public class KeyInfo
	{
		[XmlElement("ds:X509Data")]
		public X509Data X509Data { get; set; }
	}

	public class X509Data
	{
		[XmlElement("ds:X509Certificate")]
		public string X509Certificate { get; set; }
	}

	public class Object
	{
		[XmlElement("xades:QualifyingProperties")]
		public QualifyingProperties QualifyingProperties { get; set; }
	}

	public class QualifyingProperties
	{
		[XmlElement("xades:SignedProperties")]
		public SignedProperties SignedProperties { get; set; }
	}

	public class SignedProperties
	{
		[XmlElement("xades:SignedSignatureProperties")]
		public SignedSignatureProperties SignedSignatureProperties { get; set; }
	}

	public class SignedSignatureProperties
	{
		[XmlElement("xades:SigningTime")]
		public string SigningTime { get; set; }

		[XmlElement("xades:SigningCertificate")]
		public SigningCertificate SigningCertificate { get; set; }
	}

	public class SigningCertificate
	{
		[XmlElement("xades:Cert")]
		public Cert Cert { get; set; }
	}

	public class Cert
	{
		[XmlElement("xades:CertDigest")]
		public CertDigest CertDigest { get; set; }

		[XmlElement("xades:IssuerSerial")]
		public IssuerSerial IssuerSerial { get; set; }
	}

	public class CertDigest
	{
		[XmlElement("ds:DigestMethod")]
		public DigestMethod DigestMethod { get; set; }

		[XmlElement("ds:DigestValue")]
		public string DigestValue { get; set; }
	}

	public class IssuerSerial
	{
		[XmlElement("ds:X509IssuerName")]
		public string X509IssuerName { get; set; }

		[XmlElement("ds:X509SerialNumber")]
		public string X509SerialNumber { get; set; }
	}

	// Other classes like AccountingSupplierParty, AccountingCustomerParty, Delivery, etc.

	public class InvoicePeriod
	{
		[XmlElement("cbc:StartDate")]
		public string StartDate { get; set; }

		[XmlElement("cbc:EndDate")]
		public string EndDate { get; set; }

		[XmlElement("cbc:Description")]
		public string Description { get; set; }
	}

	public class BillingReference
	{
		[XmlElement("cac:AdditionalDocumentReference")]
		public AdditionalDocumentReference AdditionalDocumentReference { get; set; }
	}

	public class AdditionalDocumentReference
	{
		[XmlElement("cbc:ID")]
		public string ID { get; set; }

		[XmlElement("cbc:DocumentType")]
		public string DocumentType { get; set; }

		[XmlElement("cbc:DocumentDescription")]
		public string DocumentDescription { get; set; }
	}

	public class AccountingSupplierParty
	{
		[XmlElement("cbc:AdditionalAccountID")]
		public string AdditionalAccountID { get; set; }

		[XmlElement("cac:Party")]
		public Party Party { get; set; }
	}

	public class AccountingCustomerParty
	{
		[XmlElement("cac:Party")]
		public Party Party { get; set; }
	}

	public class Delivery
	{
		[XmlElement("cac:DeliveryParty")]
		public DeliveryParty DeliveryParty { get; set; }
	}

	public class DeliveryParty
	{
		[XmlElement("cac:PartyIdentification")]
		public List<PartyIdentification> PartyIdentification { get; set; }

		[XmlElement("cac:PostalAddress")]
		public PostalAddress PostalAddress { get; set; }

		[XmlElement("cac:PartyLegalEntity")]
		public PartyLegalEntity PartyLegalEntity { get; set; }
	}

	public class Party
	{
		[XmlElement("cbc:IndustryClassificationCode")]
		public string IndustryClassificationCode { get; set; }

		[XmlElement("cac:PartyIdentification")]
		public List<PartyIdentification> PartyIdentification { get; set; }

		[XmlElement("cac:PostalAddress")]
		public PostalAddress PostalAddress { get; set; }

		[XmlElement("cac:PartyLegalEntity")]
		public PartyLegalEntity PartyLegalEntity { get; set; }

		[XmlElement("cac:Contact")]
		public Contact Contact { get; set; }
	}

	public class PartyIdentification
	{
		[XmlElement("cbc:ID")]
		public string ID { get; set; }
	}

	public class PostalAddress
	{
		[XmlElement("cbc:StreetName")]
		public string StreetName { get; set; }

		[XmlElement("cbc:BuildingNumber")]
		public string BuildingNumber { get; set; }

		[XmlElement("cbc:CityName")]
		public string CityName { get; set; }

		[XmlElement("cbc:PostalZone")]
		public string PostalZone { get; set; }

		[XmlElement("cbc:CountrySubentity")]
		public string CountrySubentity { get; set; }

		[XmlElement("cac:Country")]
		public Country Country { get; set; }
	}

	public class Country
	{
		[XmlElement("cbc:IdentificationCode")]
		public string IdentificationCode { get; set; }

		[XmlElement("cbc:Name")]
		public string Name { get; set; }
	}

	public class PartyLegalEntity
	{
		[XmlElement("cbc:RegistrationName")]
		public string RegistrationName { get; set; }

		[XmlElement("cbc:CompanyID")]
		public string CompanyID { get; set; }
	}

	public class Contact
	{
		[XmlElement("cbc:Name")]
		public string Name { get; set; }

		[XmlElement("cbc:Telephone")]
		public string Telephone { get; set; }
	}
}
