﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Dolfin.Framework.Data.Migrations
{
    /// <inheritdoc />
    public partial class addcustomermsicclassification : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Customer_AccountGroup",
                table: "Customer");

            migrationBuilder.DropForeignKey(
                name: "FK_Customer_Referrer",
                table: "Customer");

            migrationBuilder.DropForeignKey(
                name: "FK_Transaction_AccountGroup",
                table: "Transaction");

            migrationBuilder.AlterColumn<Guid>(
                name: "AccountGroupId",
                table: "Transaction",
                type: "uuid",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AddColumn<Guid>(
                name: "ClassificationId",
                table: "Product",
                type: "uuid",
                nullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "ReferrerId",
                table: "Customer",
                type: "uuid",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AlterColumn<Guid>(
                name: "AccountGroupId",
                table: "Customer",
                type: "uuid",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AddColumn<int>(
                name: "ConsolidatePay",
                table: "Company",
                type: "integer",
                nullable: true,
                defaultValueSql: "5");

            migrationBuilder.AddColumn<bool>(
                name: "IsEInvoiceSubmitAuto",
                table: "Company",
                type: "boolean",
                nullable: true,
                defaultValueSql: "false");

            migrationBuilder.AddColumn<Guid>(
                name: "MsicId",
                table: "Company",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "Classification",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Code = table.Column<string>(type: "text", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Classification", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Msic",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Code = table.Column<string>(type: "text", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Msic", x => x.Id);
                });

            migrationBuilder.UpdateData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("69ed35dc-8775-4641-9f1d-26d9d91e5f4b"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 27, 18, 2, 46, 942, DateTimeKind.Utc).AddTicks(6565));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("11fd5e73-51fa-428a-a919-960ee860b00f"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 27, 18, 2, 46, 942, DateTimeKind.Utc).AddTicks(6695));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("5d22874b-5834-40a2-81be-bf5100eb845d"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 27, 18, 2, 46, 942, DateTimeKind.Utc).AddTicks(6689));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("c219bf33-28d0-4595-a25b-8018bd87e67a"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 27, 18, 2, 46, 942, DateTimeKind.Utc).AddTicks(6686));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("e57e65d0-85a9-49c8-8463-3424a42e1f01"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 27, 18, 2, 46, 942, DateTimeKind.Utc).AddTicks(6692));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 27, 18, 2, 46, 942, DateTimeKind.Utc).AddTicks(6630));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("81ba4893-d891-4e3c-a5e7-ba13c697bf97"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 27, 18, 2, 46, 942, DateTimeKind.Utc).AddTicks(6639));

            migrationBuilder.CreateIndex(
                name: "IX_Product_ClassificationId",
                table: "Product",
                column: "ClassificationId");

            migrationBuilder.CreateIndex(
                name: "IX_Company_MsicId",
                table: "Company",
                column: "MsicId");

            migrationBuilder.AddForeignKey(
                name: "FK_Company_Msic",
                table: "Company",
                column: "MsicId",
                principalTable: "Msic",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Customer_AccountGroup",
                table: "Customer",
                column: "AccountGroupId",
                principalTable: "AccountGroup",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Customer_Referrer",
                table: "Customer",
                column: "ReferrerId",
                principalTable: "Customer",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Product_Classification",
                table: "Product",
                column: "ClassificationId",
                principalTable: "Classification",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Transaction_AccountGroup",
                table: "Transaction",
                column: "AccountGroupId",
                principalTable: "AccountGroup",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Company_Msic",
                table: "Company");

            migrationBuilder.DropForeignKey(
                name: "FK_Customer_AccountGroup",
                table: "Customer");

            migrationBuilder.DropForeignKey(
                name: "FK_Customer_Referrer",
                table: "Customer");

            migrationBuilder.DropForeignKey(
                name: "FK_Product_Classification",
                table: "Product");

            migrationBuilder.DropForeignKey(
                name: "FK_Transaction_AccountGroup",
                table: "Transaction");

            migrationBuilder.DropTable(
                name: "Classification");

            migrationBuilder.DropTable(
                name: "Msic");

            migrationBuilder.DropIndex(
                name: "IX_Product_ClassificationId",
                table: "Product");

            migrationBuilder.DropIndex(
                name: "IX_Company_MsicId",
                table: "Company");

            migrationBuilder.DropColumn(
                name: "ClassificationId",
                table: "Product");

            migrationBuilder.DropColumn(
                name: "ConsolidatePay",
                table: "Company");

            migrationBuilder.DropColumn(
                name: "IsEInvoiceSubmitAuto",
                table: "Company");

            migrationBuilder.DropColumn(
                name: "MsicId",
                table: "Company");

            migrationBuilder.AlterColumn<Guid>(
                name: "AccountGroupId",
                table: "Transaction",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("********-0000-0000-0000-********0000"),
                oldClrType: typeof(Guid),
                oldType: "uuid",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "ReferrerId",
                table: "Customer",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("********-0000-0000-0000-********0000"),
                oldClrType: typeof(Guid),
                oldType: "uuid",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "AccountGroupId",
                table: "Customer",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("********-0000-0000-0000-********0000"),
                oldClrType: typeof(Guid),
                oldType: "uuid",
                oldNullable: true);

            migrationBuilder.UpdateData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("69ed35dc-8775-4641-9f1d-26d9d91e5f4b"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 26, 3, 21, 34, 600, DateTimeKind.Utc).AddTicks(2904));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("11fd5e73-51fa-428a-a919-960ee860b00f"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 26, 3, 21, 34, 600, DateTimeKind.Utc).AddTicks(3061));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("5d22874b-5834-40a2-81be-bf5100eb845d"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 26, 3, 21, 34, 600, DateTimeKind.Utc).AddTicks(3054));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("c219bf33-28d0-4595-a25b-8018bd87e67a"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 26, 3, 21, 34, 600, DateTimeKind.Utc).AddTicks(3051));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("e57e65d0-85a9-49c8-8463-3424a42e1f01"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 26, 3, 21, 34, 600, DateTimeKind.Utc).AddTicks(3058));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 26, 3, 21, 34, 600, DateTimeKind.Utc).AddTicks(2992));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("81ba4893-d891-4e3c-a5e7-ba13c697bf97"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 26, 3, 21, 34, 600, DateTimeKind.Utc).AddTicks(3001));

            migrationBuilder.AddForeignKey(
                name: "FK_Customer_AccountGroup",
                table: "Customer",
                column: "AccountGroupId",
                principalTable: "AccountGroup",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Customer_Referrer",
                table: "Customer",
                column: "ReferrerId",
                principalTable: "Customer",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Transaction_AccountGroup",
                table: "Transaction",
                column: "AccountGroupId",
                principalTable: "AccountGroup",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
