﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Framework.Data.Domains
{
    public partial class TransactionItem : _BaseDomain
    {
        public TransactionItem()
        {
            InventoryItemTransactionItem = new HashSet<InventoryItemTransactionItem>();
        }
        public string ProductUOMPrimaryMCode { get; set; }
        public string ProductUOMSecondaryMCode { get; set; }
        public decimal TransactionProductPrice { get; set; }
        public decimal FractionTotal { get; set; }
        public decimal FractionQuantity { get; set; }
        public decimal Quantity { get; set; }
        public decimal Discount { get; set; }
        public decimal SalesTaxAmount { get; set; }
        public decimal ServiceTaxAmount { get; set; }
        public decimal SalesTaxRate { get; set; }
        public decimal ServiceTaxRate { get; set; }
        public Guid? SalesTaxNoId { get; set; }
        public Guid? ServiceTaxNoId { get; set; }
        public decimal ExclTaxAmount { get; set; }
        public decimal InclTaxAmount { get; set; }
        public decimal OriUnitProductCost { get; set; }
        public decimal UnitAmount { get; set; }
        public decimal TotalUnitAmountWOTax { get; set; }
        public decimal TotalUnitAmount { get; set; }
        public decimal SubTotalAmount { get; set; }
        public decimal AdjAmount { get; set; }
        public Guid ProductPriceId { get; set; }
        public Guid CustomerId { get; set; }
        public Guid ProductUOMId { get; set; }
        public Guid TrxId { get; set; }
        public Guid ProductId { get; set; }
        public virtual TaxRate? SalesTaxNo { get; set; }
        public virtual TaxRate? ServiceTaxNo { get; set; }
        public virtual ProductPrice? ProductPrice { get; set; }
        //public Customer Customer { get; set; }
        public virtual ProductUOM? ProductUOM { get; set; }
        public virtual Transaction? Transaction { get; set; }
        public virtual Product? Product { get; set; }
        public virtual ICollection<InventoryItemTransactionItem> InventoryItemTransactionItem { get; }
    }
}
