﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Framework.Data.Model
{
    public partial class UploadFileRequest
    {
        public string FileName { get; set; }
        public string FolderPath { get; set; }
        public IFormFile File { get; set; }

        // Upload success and delete existing file
        public string DeleteFileUrl { get; set; }
    }
}
