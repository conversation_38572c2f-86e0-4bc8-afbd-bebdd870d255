﻿using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Domains.CustomIdentity;
using Dolfin.Mobile.API.Models;
using Dolfin.Mobile.API.Models.Request;
using Dolfin.Utility.Models;
using System.Security.Claims;

namespace Dolfin.Mobile.API.Services
{
    public interface IUserService
    {
        Task<BaseResponse<ApplicationUser>> GetUserProfile(string searchUserId);
        Task<ApplicationUser?> GetUserByGuid(string id);
        Task<ApplicationUser?> GetUserByUsername(string username);
        Task<List<ApplicationUser>> GetUserList(string[] userId);
        //Task<BaseResponse<ResultId>> InsertUser(UserRequest reqBody, Guid? userId = null);
        //Task<BaseResponse<ResultId>> ValidateCredentials(LoginRequestLocal loginPassword);
        //Task<(bool, Guid?)> ValidateCredentials(LoginRequestLocal loginPassword, bool isPasswordUpdate);
        //Task<NoResultResponse> ChangePassword(Guid loginUserId, string oldPassword, string newPassword);
        Task<NoResultResponse> DeleteUser(string id);
        Task<ApplicationUser> GetCurrentUserAsync(ClaimsPrincipal? user = null);
        void InvalidateUserCache(string userId);
        Task<ApplicationUser> GetCurrentUserStdUpsertByAdminAsync();
        Task<(ApplicationUser, Guid?)> GetCurrentUserStdGetWithCompanyAsync(Guid? companyId = null);
        Task<(ApplicationUser, Guid?)> GetCurrentUserStdGetWithBranchAsync(Guid? branchId = null);
        Task<(ApplicationUser, dynamic)> GetCurrentUserStdUpsertWithCompanyAsync(dynamic reqBody);
        Task<(ApplicationUser, dynamic, Guid?)> GetCurrentUserStdUpsertWithBranchAsync(dynamic reqBody);
        Task<(ApplicationUser, dynamic)> GetCurrentUserStdAllUpsertWithCompanyAsync(dynamic reqBody);
        Task<(ApplicationUser, dynamic, Guid?)> GetCurrentUserStdAllUpsertWithBranchAsync(dynamic reqBody);
        Task<List<ApplicationUser>> GetUserByBranchId(Guid branchId);
        Task<BaseResponse<ResultId>> UserAccessValidatation(bool userTypeCheck = false);
    }
}
