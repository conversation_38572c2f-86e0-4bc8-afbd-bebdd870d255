using Dolfin.Framework.Data.Domains;
using Dolfin.Mobile.API.Models.Request;
using Dolfin.Utility.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Dolfin.Mobile.API.Services
{
    /// <summary>
    /// Service interface for lookup operations
    /// </summary>
    public interface ILookupService
    {
        /// <summary>
        /// Get all lookup groups
        /// </summary>
        /// <param name="pagination">Pagination parameters</param>
        /// <param name="filterList">Filter parameters</param>
        /// <param name="companyId">Optional company ID to filter by</param>
        /// <returns>A response containing a paged list of lookup groups</returns>
        Task<BaseResponse<PagedList<LookupGroup>>> GetLookupGroupList(Pagination pagination = null, CommonFilterList filterList = null, Guid? companyId = null);

        /// <summary>
        /// Get a lookup group by its ID
        /// </summary>
        /// <param name="lookupGroupId">The lookup group ID</param>
        /// <returns>A response containing the lookup group if found</returns>
        Task<BaseResponse<LookupGroup>> GetLookupGroupById(Guid lookupGroupId);

        /// <summary>
        /// Get a lookup group by its code
        /// </summary>
        /// <param name="code">The lookup group code</param>
        /// <param name="companyId">Optional company ID to filter by</param>
        /// <returns>A response containing the lookup group if found</returns>
        Task<BaseResponse<LookupGroup>> GetLookupGroupByCode(string code, Guid? companyId = null);

        /// <summary>
        /// Create a new lookup group
        /// </summary>
        /// <param name="request">The lookup group request</param>
        /// <returns>A response containing the ID of the created lookup group</returns>
        Task<BaseResponse<ResultId>> CreateLookupGroup(LookupGroupRequest request);

        /// <summary>
        /// Update an existing lookup group
        /// </summary>
        /// <param name="request">The update lookup group request</param>
        /// <returns>A response containing the ID of the updated lookup group</returns>
        Task<BaseResponse<ResultId>> UpdateLookupGroup(UpdateLookupGroupRequest request);

        /// <summary>
        /// Delete a lookup group
        /// </summary>
        /// <param name="lookupGroupId">The lookup group ID</param>
        /// <returns>A response indicating success or failure</returns>
        Task<NoResultResponse> DeleteLookupGroup(Guid lookupGroupId);

        /// <summary>
        /// Get all lookup items for a specific group
        /// </summary>
        /// <param name="lookupGroupId">The lookup group ID</param>
        /// <param name="pagination">Pagination parameters</param>
        /// <param name="filterList">Filter parameters</param>
        /// <returns>A response containing a paged list of lookup items</returns>
        Task<BaseResponse<PagedList<LookupItem>>> GetLookupItemList(Guid lookupGroupId, Pagination pagination = null, CommonFilterList filterList = null);

        /// <summary>
        /// Get all lookup items for a specific group without pagination
        /// </summary>
        /// <param name="lookupGroupId">The lookup group ID</param>
        /// <returns>A response containing a list of lookup items</returns>
        Task<BaseResponse<IEnumerable<LookupItem>>> GetAllLookupItemsByGroup(Guid lookupGroupId);

        /// <summary>
        /// Get a lookup item by its ID
        /// </summary>
        /// <param name="lookupItemId">The lookup item ID</param>
        /// <returns>A response containing the lookup item if found</returns>
        Task<BaseResponse<LookupItem>> GetLookupItemById(Guid lookupItemId);

        /// <summary>
        /// Create a new lookup item
        /// </summary>
        /// <param name="request">The lookup item request</param>
        /// <returns>A response containing the ID of the created lookup item</returns>
        Task<BaseResponse<ResultId>> CreateLookupItem(LookupItemRequest request);

        /// <summary>
        /// Update an existing lookup item
        /// </summary>
        /// <param name="request">The update lookup item request</param>
        /// <returns>A response containing the ID of the updated lookup item</returns>
        Task<BaseResponse<ResultId>> UpdateLookupItem(UpdateLookupItemRequest request);

        /// <summary>
        /// Delete a lookup item
        /// </summary>
        /// <param name="lookupItemId">The lookup item ID</param>
        /// <returns>A response indicating success or failure</returns>
        Task<NoResultResponse> DeleteLookupItem(Guid lookupItemId);
    }
}
