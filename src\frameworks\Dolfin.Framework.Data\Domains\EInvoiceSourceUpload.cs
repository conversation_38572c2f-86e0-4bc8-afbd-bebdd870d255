﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Dolfin.Utility.Enum.Enums;

namespace Dolfin.Framework.Data.Domains
{
	public partial class EInvoiceSourceUpload : _BaseDomain
	{
		public Guid CompanyId { get; set; }
		public required string FileUrl { get; set; }
		public string? FileName { get; set; }
		public string? MimeType { get; set; }
		public SourceUploadStatusEnum SourceUploadStatus { get; set; } = SourceUploadStatusEnum.Pending;
		public DateTime? ProcessedAt { get; set; }
		public string? LogDetails { get; set; }
	}
}
