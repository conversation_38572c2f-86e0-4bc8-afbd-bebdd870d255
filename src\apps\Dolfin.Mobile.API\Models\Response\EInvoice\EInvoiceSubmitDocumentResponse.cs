using System.Text.Json.Serialization;
using System.Linq;

namespace Dolfin.Mobile.API.Models
{
    public class EInvoiceSubmitDocumentResponse
    {
        [JsonPropertyName("documents")]
        public List<DocumentResponse> Documents { get; set; } = new List<DocumentResponse>();

        // Helper properties to access the first document's properties directly
        [JsonIgnore]
        public string SubmissionUID => Documents?.FirstOrDefault()?.SubmissionUID;

        [JsonIgnore]
        public string DocumentUUID => Documents?.FirstOrDefault()?.DocumentUUID;

        [JsonIgnore]
        public string DocumentLongId => Documents?.FirstOrDefault()?.DocumentLongId;

        [JsonIgnore]
        public string DocumentToken => Documents?.FirstOrDefault()?.DocumentToken;

        [JsonIgnore]
        public string Status => Documents?.FirstOrDefault()?.Status;

        [JsonIgnore]
        public string ResponseCode => Documents?.FirstOrDefault()?.ResponseCode;

        [JsonIgnore]
        public string ResponseMessage => Documents?.FirstOrDefault()?.ResponseMessage;

        [JsonIgnore]
        public string QrCodeUrl => Documents?.FirstOrDefault()?.QrCodeUrl;

        [JsonIgnore]
        public string DocumentUrl => Documents?.FirstOrDefault()?.DocumentUrl;
    }

    public class DocumentResponse
    {
        [JsonPropertyName("submissionUID")]
        public string SubmissionUID { get; set; }

        [JsonPropertyName("documentUUID")]
        public string DocumentUUID { get; set; }

        [JsonPropertyName("documentLongId")]
        public string DocumentLongId { get; set; }

        [JsonPropertyName("documentToken")]
        public string DocumentToken { get; set; }

        [JsonPropertyName("status")]
        public string Status { get; set; }

        [JsonPropertyName("responseCode")]
        public string ResponseCode { get; set; }

        [JsonPropertyName("responseMessage")]
        public string ResponseMessage { get; set; }

        [JsonPropertyName("qrCodeUrl")]
        public string QrCodeUrl { get; set; }

        [JsonPropertyName("documentUrl")]
        public string DocumentUrl { get; set; }
    }
}
