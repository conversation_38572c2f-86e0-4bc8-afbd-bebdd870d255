using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace Dolfin.Framework.Repository.Interfaces
{
    /// <summary>
    /// Generic repository interface for data access operations
    /// </summary>
    /// <typeparam name="T">Entity type</typeparam>
    public interface IRepository<T> where T : class
    {
        /// <summary>
        /// Get entity by ID
        /// </summary>
        /// <param name="id">Entity ID</param>
        /// <returns>Entity or null if not found</returns>
        Task<T> GetByIdAsync(object id);

        /// <summary>
        /// Get entity by ID with included related entities
        /// </summary>
        /// <param name="id">Entity ID</param>
        /// <param name="includes">Related entities to include</param>
        /// <returns>Entity or null if not found</returns>
        Task<T> GetByIdAsync(object id, params Expression<Func<T, object>>[] includes);

        /// <summary>
        /// Get all entities
        /// </summary>
        /// <returns>List of all entities</returns>
        Task<IEnumerable<T>> GetAllAsync();

        /// <summary>
        /// Get all entities with included related entities
        /// </summary>
        /// <param name="includes">Related entities to include</param>
        /// <returns>List of all entities</returns>
        Task<IEnumerable<T>> GetAllAsync(params Expression<Func<T, object>>[] includes);

        /// <summary>
        /// Find entities by predicate
        /// </summary>
        /// <param name="predicate">Search predicate</param>
        /// <returns>List of matching entities</returns>
        Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate);

        /// <summary>
        /// Find entities by predicate with included related entities
        /// </summary>
        /// <param name="predicate">Search predicate</param>
        /// <param name="includes">Related entities to include</param>
        /// <returns>List of matching entities</returns>
        Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate, params Expression<Func<T, object>>[] includes);

        /// <summary>
        /// Get first entity matching predicate or default if none found
        /// </summary>
        /// <param name="predicate">Search predicate</param>
        /// <returns>Matching entity or null</returns>
        Task<T> FirstOrDefaultAsync(Expression<Func<T, bool>> predicate);

        /// <summary>
        /// Get first entity matching predicate with included related entities or default if none found
        /// </summary>
        /// <param name="predicate">Search predicate</param>
        /// <param name="includes">Related entities to include</param>
        /// <returns>Matching entity or null</returns>
        Task<T> FirstOrDefaultAsync(Expression<Func<T, bool>> predicate, params Expression<Func<T, object>>[] includes);

        /// <summary>
        /// Add a new entity
        /// </summary>
        /// <param name="entity">Entity to add</param>
        Task AddAsync(T entity);

        /// <summary>
        /// Add multiple entities
        /// </summary>
        /// <param name="entities">Entities to add</param>
        Task AddRangeAsync(IEnumerable<T> entities);

        /// <summary>
        /// Update an existing entity
        /// </summary>
        /// <param name="entity">Entity to update</param>
        void Update(T entity);

        /// <summary>
        /// Remove an entity
        /// </summary>
        /// <param name="entity">Entity to remove</param>
        void Remove(T entity);

        /// <summary>
        /// Remove multiple entities
        /// </summary>
        /// <param name="entities">Entities to remove</param>
        void RemoveRange(IEnumerable<T> entities);

        /// <summary>
        /// Count entities matching predicate
        /// </summary>
        /// <param name="predicate">Search predicate (optional)</param>
        /// <returns>Count of matching entities</returns>
        Task<int> CountAsync(Expression<Func<T, bool>> predicate = null);

        /// <summary>
        /// Check if any entities match predicate
        /// </summary>
        /// <param name="predicate">Search predicate</param>
        /// <returns>True if any entities match, false otherwise</returns>
        Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate);

        /// <summary>
        /// Get entity from cache or create if not found
        /// </summary>
        /// <param name="cacheKey">Cache key</param>
        /// <param name="factory">Function to create entity if not in cache</param>
        /// <param name="absoluteExpirationMinutes">Cache expiration in minutes</param>
        /// <returns>Entity from cache or created by factory</returns>
        Task<T> GetOrCreateAsync(string cacheKey, Func<Task<T>> factory, int? absoluteExpirationMinutes = null);

        /// <summary>
        /// Get item from cache or create if not found
        /// </summary>
        /// <typeparam name="TResult">Type of the result</typeparam>
        /// <param name="cacheKey">Cache key</param>
        /// <param name="factory">Function to create item if not in cache</param>
        /// <param name="absoluteExpirationMinutes">Cache expiration in minutes</param>
        /// <returns>Item from cache or created by factory</returns>
        Task<TResult> GetOrCreateAsync<TResult>(string cacheKey, Func<Task<TResult>> factory, int? absoluteExpirationMinutes = null);

        /// <summary>
        /// Invalidate cache entry
        /// </summary>
        /// <param name="cacheKey">Cache key to invalidate</param>
        void InvalidateCache(string cacheKey);
    }
}
