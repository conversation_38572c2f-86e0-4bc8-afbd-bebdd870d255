﻿using Dolfin.Framework.Data.Domains;
using System.Text.Json.Serialization;

namespace Dolfin.Mobile.API.Models.Request
{
    public class AddressCompanyRequest
    {
        public required string FirstName { get; set; }
        public required string LastName { get; set; }
        [JsonIgnore]
        public string? Email { get; set; }
        [JsonIgnore]
        public string? Company { get; set; }
        public required string Address1 { get; set; }
        public string? Address2 { get; set; }
        public string? Address3 { get; set; }
        public required string PostalCode { get; set; }
        public string PhoneNo { get; set; }
        public string? FaxNo { get; set; }
        public string? Coordinate { get; set; }
        public Guid CountryId { get; set; }
        public Guid StateProvinceId { get; set; }
        public Guid RegionId { get; set; }
    }
}