# Deploying .NET 8.0 Application to AWS EC2

This guide provides step-by-step instructions for deploying the Dolfin.Mobile.API application to an AWS EC2 instance.

## Prerequisites

- .NET 8.0 SDK installed on your development machine
- Access to AWS EC2 instance (IP: *************)
- SSH key file (dolfin-master.pem)
- FileZilla or another SFTP client
- SSH client

## Deployment Steps

### 1. Publish the Project

Publish the Dolfin.Mobile.API project in Release mode.

### 2. Prepare the Deployment Package

1. Navigate to the publish folder (typically located at `bin/Release/net8.0/publish/`)
2. Delete the `appsettings.json` file (prevent overwriting the existing configuration on the server)
3. Zip all the files in the publish folder as `deploy.zip`

### 3. First-Time Setup with FileZilla

If this is your first time connecting to the EC2 instance:

1. Open FileZilla
2. Go to **Site Manager**
3. Click **New Site**
4. Enter "Dev" as the site name
5. Configure the connection:
   - Protocol: SFTP
   - Host: *************
   - Logon Type: Key file
   - User: ec2-user
   - Key file: Select the `dolfin-master.pem` file
6. Click **OK**

### 4. Upload the Deployment Package

1. Connect to the EC2 instance using FileZilla
2. Upload the `deploy.zip` file to the `/home/<USER>/` directory

### 5. SSH into the EC2 Instance

1. Navigate to the directory containing the `dolfin-master.pem` file
2. Open a terminal or command prompt
3. Run the following command:

```bash
ssh -i dolfin-master.pem ec2-user@*************
```

### 6. Deploy the Application

Once connected to the EC2 instance, run the following commands:

```bash
# Stop the existing service
sudo systemctl stop kestrel-dolfin-api.service

# Extract the new files to the application directory
sudo -s unzip /home/<USER>/deploy.zip -d /var/app/dolfin-api

# Start the service
sudo systemctl start kestrel-dolfin-api.service
```

## Service Configuration

The application runs as a systemd service named `kestrel-dolfin-api.service`. This service is configured to:

1. Start the .NET application using Kestrel web server
2. Restart automatically if it crashes
3. Run with appropriate permissions

To check the status of the service:

```bash
sudo systemctl status kestrel-dolfin-api.service
```

To view the application logs:

```bash
sudo journalctl -u kestrel-dolfin-api.service
```

## Troubleshooting

If you encounter issues during deployment:

1. Check the service status:
   ```bash
   sudo systemctl status kestrel-dolfin-api.service
   ```

2. View the application logs:
   ```bash
   sudo journalctl -u kestrel-dolfin-api.service -f
   ```

3. Verify file permissions:
   ```bash
   ls -la /var/app/dolfin-api
   ```

4. Ensure the service user has appropriate permissions:
   ```bash
   sudo chown -R appropriate-user:appropriate-group /var/app/dolfin-api
   ```