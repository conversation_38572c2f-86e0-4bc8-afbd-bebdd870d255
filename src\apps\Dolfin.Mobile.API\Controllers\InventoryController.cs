﻿//using Dolfin.Framework.Data.Domains;
//using Dolfin.Mobile.API.Models.Request;
//using Dolfin.Mobile.API.Services;
//using Microsoft.AspNetCore.Mvc;

//namespace Dolfin.Mobile.API.Controllers
//{
//    [ApiController]
//    [Route("api/[controller]")]
//    public class InventoryController : ControllerBase
//    {
//        private readonly IInventoryService _inventoryService;

//        public InventoryController(IInventoryService inventoryService)
//        {
//            _inventoryService = inventoryService;
//        }

//        // GET: api/Inventory
//        [HttpGet]
//        public async Task<IActionResult> GetAllInventories()
//        {
//            var inventories = await _inventoryService.GetAllInventoriesAsync();
//            return Ok(inventories);
//        }

//        // GET: api/Inventory/{id}
//        [HttpGet("{id}")]
//        public async Task<IActionResult> GetInventoryById(Guid id)
//        {
//            var inventory = await _inventoryService.GetInventoryByIdAsync(id);
//            if (inventory == null) return NotFound();
//            return Ok(inventory);
//        }

//        // POST: api/Inventory
//        [HttpPost]
//        public async Task<IActionResult> CreateInventory([FromBody] InventoryRequest inventoryRequest)
//        {
//            if (!ModelState.IsValid) return BadRequest(ModelState);

//            await _inventoryService.CreateInventoryAsync(inventoryRequest);
//            return CreatedAtAction(nameof(GetInventoryById), new { id = inventory.Id }, inventory);
//        }

//        // PUT: api/Inventory/{id}
//        [HttpPut("{id}")]
//        public async Task<IActionResult> UpdateInventory(Guid id, [FromBody] Inventory inventory)
//        {
//            if (id != inventory.Id) return BadRequest("ID mismatch");
//            if (!ModelState.IsValid) return BadRequest(ModelState);

//            await _inventoryService.UpdateInventoryAsync(inventory);
//            return NoContent();
//        }

//        // DELETE: api/Inventory/{id}
//        [HttpDelete("{id}")]
//        public async Task<IActionResult> DeleteInventory(Guid id)
//        {
//            await _inventoryService.DeleteInventoryAsync(id);
//            return NoContent();
//        }
//    }
//}
