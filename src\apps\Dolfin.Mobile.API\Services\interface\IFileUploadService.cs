﻿﻿using Dolfin.Mobile.API.Models.Request;
using Dolfin.Mobile.API.Models.Response;
using Dolfin.Utility.Models;

namespace Dolfin.Mobile.API.Services
{
    public interface IFileUploadService
    {
        /// <summary>
        /// Upload a file to Amazon S3
        /// </summary>
        /// <param name="request">The file upload request</param>
        /// <param name="companyId">The company ID for organizing files</param>
        /// <returns>Response with file details</returns>
        Task<BaseResponse<FileUploadResponse>> UploadFile(FileUploadRequest request, Guid companyId);

        /// <summary>
        /// Get a pre-signed URL for a file
        /// </summary>
        /// <param name="fileUrl">The file URL/path in S3</param>
        /// <returns>Pre-signed URL for temporary access</returns>
        Task<BaseResponse<string>> GetPreSignedUrl(string fileUrl);

        /// <summary>
        /// Delete a file from Amazon S3
        /// </summary>
        /// <param name="fileUrl">The file URL/path in S3</param>
        /// <returns>Response indicating success or failure</returns>
        Task<NoResultResponse> DeleteFile(string fileUrl);

        /// <summary>
        /// Get files by reference ID and module
        /// </summary>
        /// <param name="referenceId">The reference ID (e.g., product ID, customer ID)</param>
        /// <param name="module">The module name (required)</param>
        /// <returns>List of file metadata</returns>
        Task<BaseResponse<List<FileUploadResponse>>> GetFilesByReferenceId(Guid referenceId, string module);
    }
}
