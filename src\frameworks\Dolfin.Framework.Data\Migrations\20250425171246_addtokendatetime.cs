﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Dolfin.Framework.Data.Migrations
{
    /// <inheritdoc />
    public partial class addtokendatetime : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "EInvoiceTokenDateTime",
                table: "Company",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.UpdateData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("69ed35dc-8775-4641-9f1d-26d9d91e5f4b"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 25, 17, 12, 45, 537, DateTimeKind.Utc).AddTicks(4687));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("11fd5e73-51fa-428a-a919-960ee860b00f"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 25, 17, 12, 45, 537, DateTimeKind.Utc).AddTicks(4812));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("5d22874b-5834-40a2-81be-bf5100eb845d"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 25, 17, 12, 45, 537, DateTimeKind.Utc).AddTicks(4805));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("c219bf33-28d0-4595-a25b-8018bd87e67a"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 25, 17, 12, 45, 537, DateTimeKind.Utc).AddTicks(4802));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("e57e65d0-85a9-49c8-8463-3424a42e1f01"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 25, 17, 12, 45, 537, DateTimeKind.Utc).AddTicks(4810));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 25, 17, 12, 45, 537, DateTimeKind.Utc).AddTicks(4749));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("81ba4893-d891-4e3c-a5e7-ba13c697bf97"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 25, 17, 12, 45, 537, DateTimeKind.Utc).AddTicks(4755));
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "EInvoiceTokenDateTime",
                table: "Company");

            migrationBuilder.UpdateData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("69ed35dc-8775-4641-9f1d-26d9d91e5f4b"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 15, 15, 39, 58, 640, DateTimeKind.Utc).AddTicks(8514));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("11fd5e73-51fa-428a-a919-960ee860b00f"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 15, 15, 39, 58, 640, DateTimeKind.Utc).AddTicks(8623));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("5d22874b-5834-40a2-81be-bf5100eb845d"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 15, 15, 39, 58, 640, DateTimeKind.Utc).AddTicks(8618));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("c219bf33-28d0-4595-a25b-8018bd87e67a"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 15, 15, 39, 58, 640, DateTimeKind.Utc).AddTicks(8616));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("e57e65d0-85a9-49c8-8463-3424a42e1f01"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 15, 15, 39, 58, 640, DateTimeKind.Utc).AddTicks(8621));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 15, 15, 39, 58, 640, DateTimeKind.Utc).AddTicks(8574));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("81ba4893-d891-4e3c-a5e7-ba13c697bf97"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 15, 15, 39, 58, 640, DateTimeKind.Utc).AddTicks(8579));
        }
    }
}
