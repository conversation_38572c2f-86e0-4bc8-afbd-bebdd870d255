﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Dolfin.Mobile.API.Models
{
    public class EmailSettings
    {
        public virtual string SmtpServer { get; set; }
        public virtual int SmtpPort { get; set; }
        public virtual bool SmtpUseSSL { get; set; }
        public virtual string SmtpUsername { get; set; }
        public virtual string SmtpPassword { get; set; }

        public virtual bool UseImap { get; set; }

        public virtual string PopServer { get; set; }
        public virtual int PopPort { get; set; }
        public virtual bool PopUseSSL { get; set; }
        public virtual string PopUsername { get; set; }
        public virtual string PopPassword { get; set; }

        public virtual string ImapServer { get; set; }
        public virtual int ImapPort { get; set; }
        public virtual bool ImapUseSSL { get; set; }
        public virtual string ImapUsername { get; set; }
        public virtual string ImapPassword { get; set; }

    }
}
