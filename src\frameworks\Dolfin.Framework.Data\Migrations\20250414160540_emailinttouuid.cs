﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Dolfin.Framework.Data.Migrations
{
    public partial class emailinttouuid : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Step 1: Discover and drop existing foreign key constraints
            migrationBuilder.Sql(@"
                -- Drop Email foreign key constraint if it exists
                DO $$
                BEGIN
                    IF EXISTS (
                        SELECT 1 FROM pg_constraint 
                        WHERE conrelid = '""Email""'::regclass 
                        AND confrelid = '""EmailAccount""'::regclass
                    ) THEN
                        EXECUTE (
                            SELECT format('ALTER TABLE ""Email"" DROP CONSTRAINT %I', conname)
                            FROM pg_constraint
                            WHERE conrelid = '""Email""'::regclass 
                            AND confrelid = '""EmailAccount""'::regclass
                            LIMIT 1
                        );
                    END IF;
                    
                    -- Drop EmailTemplate foreign key constraint if it exists
                    IF EXISTS (
                        SELECT 1 FROM pg_constraint 
                        WHERE conrelid = '""EmailTemplate""'::regclass 
                        AND confrelid = '""EmailAccount""'::regclass
                    ) THEN
                        EXECUTE (
                            SELECT format('ALTER TABLE ""EmailTemplate"" DROP CONSTRAINT %I', conname)
                            FROM pg_constraint
                            WHERE conrelid = '""EmailTemplate""'::regclass 
                            AND confrelid = '""EmailAccount""'::regclass
                            LIMIT 1
                        );
                    END IF;
                END $$;
            ");

            // Step 2: Add temporary UUID columns to all tables
            migrationBuilder.AddColumn<Guid>(
                name: "TempId",
                table: "EmailAccount",
                type: "uuid",
                nullable: false,
                defaultValueSql: "gen_random_uuid()");

            migrationBuilder.AddColumn<Guid>(
                name: "TempEmailAccountId",
                table: "EmailTemplate",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("********-0000-0000-0000-********0000"));

            migrationBuilder.AddColumn<Guid>(
                name: "TempEmailAccountId",
                table: "Email",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("********-0000-0000-0000-********0000"));

            // Step 3: Update relationships with new UUIDs
            migrationBuilder.Sql(@"
                -- Update EmailTemplate references
                UPDATE ""EmailTemplate"" AS et
                SET ""TempEmailAccountId"" = ea.""TempId""
                FROM ""EmailAccount"" AS ea
                WHERE et.""EmailAccountId"" = ea.""Id"";

                -- Update Email references
                UPDATE ""Email"" AS e
                SET ""TempEmailAccountId"" = ea.""TempId""
                FROM ""EmailAccount"" AS ea
                WHERE e.""EmailAccountId"" = ea.""Id"";
            ");

            // Step 4: Drop old columns
            migrationBuilder.DropPrimaryKey(
                name: "PK_EmailAccount",
                table: "EmailAccount");

            migrationBuilder.DropColumn(
                name: "Id",
                table: "EmailAccount");

            migrationBuilder.DropColumn(
                name: "EmailAccountId",
                table: "EmailTemplate");

            migrationBuilder.DropColumn(
                name: "EmailAccountId",
                table: "Email");

            // Step 5: Rename temporary columns to final names
            migrationBuilder.RenameColumn(
                name: "TempId",
                table: "EmailAccount",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "TempEmailAccountId",
                table: "EmailTemplate",
                newName: "EmailAccountId");

            migrationBuilder.RenameColumn(
                name: "TempEmailAccountId",
                table: "Email",
                newName: "EmailAccountId");

            // Step 6: Recreate primary key and indexes
            migrationBuilder.AddPrimaryKey(
                name: "PK_EmailAccount",
                table: "EmailAccount",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "IX_EmailTemplate_EmailAccountId",
                table: "EmailTemplate",
                column: "EmailAccountId");

            migrationBuilder.CreateIndex(
                name: "IX_Email_EmailAccountId",
                table: "Email",
                column: "EmailAccountId");

            // Step 7: Recreate foreign key constraints
            migrationBuilder.AddForeignKey(
                name: "FK_Email_EmailAccount_EmailAccountId",
                table: "Email",
                column: "EmailAccountId",
                principalTable: "EmailAccount",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_EmailTemplate_EmailAccount_EmailAccountId",
                table: "EmailTemplate",
                column: "EmailAccountId",
                principalTable: "EmailAccount",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            // Update seed data
            migrationBuilder.UpdateData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("69ed35dc-8775-4641-9f1d-26d9d91e5f4b"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 14, 16, 5, 39, 489, DateTimeKind.Utc).AddTicks(6620));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("11fd5e73-51fa-428a-a919-960ee860b00f"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 14, 16, 5, 39, 489, DateTimeKind.Utc).AddTicks(6763));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("5d22874b-5834-40a2-81be-bf5100eb845d"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 14, 16, 5, 39, 489, DateTimeKind.Utc).AddTicks(6758));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("c219bf33-28d0-4595-a25b-8018bd87e67a"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 14, 16, 5, 39, 489, DateTimeKind.Utc).AddTicks(6754));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("e57e65d0-85a9-49c8-8463-3424a42e1f01"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 14, 16, 5, 39, 489, DateTimeKind.Utc).AddTicks(6760));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 14, 16, 5, 39, 489, DateTimeKind.Utc).AddTicks(6705));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("81ba4893-d891-4e3c-a5e7-ba13c697bf97"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 14, 16, 5, 39, 489, DateTimeKind.Utc).AddTicks(6712));
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Step 1: Drop foreign key constraints
            migrationBuilder.Sql(@"
                -- Drop Email foreign key constraint if it exists
                DO $$
                BEGIN
                    IF EXISTS (
                        SELECT 1 FROM pg_constraint 
                        WHERE conrelid = '""Email""'::regclass 
                        AND confrelid = '""EmailAccount""'::regclass
                    ) THEN
                        EXECUTE (
                            SELECT format('ALTER TABLE ""Email"" DROP CONSTRAINT %I', conname)
                            FROM pg_constraint
                            WHERE conrelid = '""Email""'::regclass 
                            AND confrelid = '""EmailAccount""'::regclass
                            LIMIT 1
                        );
                    END IF;
                    
                    -- Drop EmailTemplate foreign key constraint if it exists
                    IF EXISTS (
                        SELECT 1 FROM pg_constraint 
                        WHERE conrelid = '""EmailTemplate""'::regclass 
                        AND confrelid = '""EmailAccount""'::regclass
                    ) THEN
                        EXECUTE (
                            SELECT format('ALTER TABLE ""EmailTemplate"" DROP CONSTRAINT %I', conname)
                            FROM pg_constraint
                            WHERE conrelid = '""EmailTemplate""'::regclass 
                            AND confrelid = '""EmailAccount""'::regclass
                            LIMIT 1
                        );
                    END IF;
                END $$;
            ");

            // Step 2: Add temporary integer columns
            migrationBuilder.AddColumn<int>(
                name: "OldId",
                table: "EmailAccount",
                type: "integer",
                nullable: false)
                .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

            migrationBuilder.AddColumn<int>(
                name: "OldEmailAccountId",
                table: "EmailTemplate",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "OldEmailAccountId",
                table: "Email",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            // Step 3: Restore integer IDs (simplified approach)
            migrationBuilder.Sql(@"
                -- Create sequential integer IDs
                WITH numbered_accounts AS (
                    SELECT ""Id"", ROW_NUMBER() OVER () as new_id
                    FROM ""EmailAccount""
                )
                UPDATE ""EmailAccount""
                SET ""OldId"" = na.new_id
                FROM numbered_accounts na
                WHERE ""EmailAccount"".""Id"" = na.""Id"";

                -- Update EmailTemplate references
                UPDATE ""EmailTemplate""
                SET ""OldEmailAccountId"" = ea.""OldId""
                FROM ""EmailAccount"" ea
                WHERE ""EmailTemplate"".""EmailAccountId"" = ea.""Id"";

                -- Update Email references
                UPDATE ""Email""
                SET ""OldEmailAccountId"" = ea.""OldId""
                FROM ""EmailAccount"" ea
                WHERE ""Email"".""EmailAccountId"" = ea.""Id"";
            ");

            // Step 4: Drop UUID columns
            migrationBuilder.DropPrimaryKey(
                name: "PK_EmailAccount",
                table: "EmailAccount");

            migrationBuilder.DropColumn(
                name: "Id",
                table: "EmailAccount");

            migrationBuilder.DropColumn(
                name: "EmailAccountId",
                table: "EmailTemplate");

            migrationBuilder.DropColumn(
                name: "EmailAccountId",
                table: "Email");

            // Step 5: Rename temporary columns back to original names
            migrationBuilder.RenameColumn(
                name: "OldId",
                table: "EmailAccount",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "OldEmailAccountId",
                table: "EmailTemplate",
                newName: "EmailAccountId");

            migrationBuilder.RenameColumn(
                name: "OldEmailAccountId",
                table: "Email",
                newName: "EmailAccountId");

            // Step 6: Recreate primary key and indexes
            migrationBuilder.AddPrimaryKey(
                name: "PK_EmailAccount",
                table: "EmailAccount",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "IX_EmailTemplate_EmailAccountId",
                table: "EmailTemplate",
                column: "EmailAccountId");

            migrationBuilder.CreateIndex(
                name: "IX_Email_EmailAccountId",
                table: "Email",
                column: "EmailAccountId");

            // Step 7: Recreate foreign key constraints
            migrationBuilder.AddForeignKey(
                name: "FK_Email_EmailAccount_EmailAccountId",
                table: "Email",
                column: "EmailAccountId",
                principalTable: "EmailAccount",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_EmailTemplate_EmailAccount_EmailAccountId",
                table: "EmailTemplate",
                column: "EmailAccountId",
                principalTable: "EmailAccount",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            // Restore original data values
            migrationBuilder.UpdateData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("69ed35dc-8775-4641-9f1d-26d9d91e5f4b"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 14, 14, 58, 50, 200, DateTimeKind.Utc).AddTicks(2979));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("11fd5e73-51fa-428a-a919-960ee860b00f"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 14, 14, 58, 50, 200, DateTimeKind.Utc).AddTicks(3116));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("5d22874b-5834-40a2-81be-bf5100eb845d"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 14, 14, 58, 50, 200, DateTimeKind.Utc).AddTicks(3109));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("c219bf33-28d0-4595-a25b-8018bd87e67a"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 14, 14, 58, 50, 200, DateTimeKind.Utc).AddTicks(3106));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("e57e65d0-85a9-49c8-8463-3424a42e1f01"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 14, 14, 58, 50, 200, DateTimeKind.Utc).AddTicks(3112));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 14, 14, 58, 50, 200, DateTimeKind.Utc).AddTicks(3059));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("81ba4893-d891-4e3c-a5e7-ba13c697bf97"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 14, 14, 58, 50, 200, DateTimeKind.Utc).AddTicks(3065));
        }
    }
}