using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.OpenApi.Models;
using System.Reflection;

namespace Dolfin.Mobile.API.Infrastructure
{
    public static class SwaggerConfiguration
    {
        public static IServiceCollection ConfigureSwagger(this IServiceCollection services)
        {
            // Get IConfiguration from service collection
            var serviceProvider = services.BuildServiceProvider();
            var configuration = serviceProvider.GetRequiredService<IConfiguration>();

            // Check if Swagger is enabled in configuration
            var swaggerEnabled = configuration.GetValue<bool>("SwaggerSettings:Enabled");

            // Only register Swagger services if enabled
            if (swaggerEnabled)
            {
                services.AddSwaggerGen(options =>
                {
                    options.SwaggerDoc("v1", new OpenApiInfo
                    {
                        Title = "Dolfin Mobile API",
                        Version = "v1",
                        Description = "REST API for Dolfin Mobile Application",
                        Contact = new OpenApiContact
                        {
                            Name = "Dolfin Support",
                            Email = "<EMAIL>"//,
                            //Url = new Uri("https://dolfin.com/support")
                        }//,
                        //License = new OpenApiLicense
                        //{
                        //    Name = "Dolfin License",
                        //    Url = new Uri("https://dolfin.com/license")
                        //}
                    });

                    // Add JWT Authentication support in Swagger UI
                    options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
                    {
                        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
                        Name = "Authorization",
                        In = ParameterLocation.Header,
                        Type = SecuritySchemeType.ApiKey,
                        Scheme = "Bearer"
                    });

                    options.AddSecurityRequirement(new OpenApiSecurityRequirement
                    {
                        {
                            new OpenApiSecurityScheme
                            {
                                Reference = new OpenApiReference
                                {
                                    Type = ReferenceType.SecurityScheme,
                                    Id = "Bearer"
                                }
                            },
                            Array.Empty<string>()
                        }
                    });

                    // Set the comments path for the Swagger JSON and UI
                    var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
                    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);

                    // Only include XML comments if the file exists
                    if (File.Exists(xmlPath))
                    {
                        options.IncludeXmlComments(xmlPath);
                    }
                });
            }
            else
            {
                // Add empty services to avoid errors when Swagger is referenced but disabled
                services.AddSwaggerGen();
            }

            return services;
        }
    }
}
