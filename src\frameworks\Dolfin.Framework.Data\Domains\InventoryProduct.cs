﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Dolfin.Utility.Enum.Enums;

namespace Dolfin.Framework.Data.Domains
{
    public partial class InventoryProduct : _BaseDomain
    {
        public InventoryProduct()
        {
            InventoryItem = new HashSet<InventoryItem>();
        }
        public decimal BalanceQuantity { get; set; }
        public decimal SafeQuantity { get; set; }
        public Guid ProductId { get; set; }
        public Guid InventoryId { get; set; }
        public virtual Product? Product { get; set; }
        public virtual Inventory? Inventory { get; set; }
        public virtual ICollection<InventoryItem> InventoryItem { get; }
    }
}
