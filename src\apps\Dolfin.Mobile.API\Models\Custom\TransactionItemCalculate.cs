﻿using Dolfin.Framework.Data.Domains;
using System.Text.Json.Serialization;

namespace Dolfin.Mobile.API.Models.Request
{
    public class TransactionCalculate
    {
        public decimal SalesTaxAmount { get; set; } = 0.0m;
        public decimal ServiceTaxAmount { get; set; } = 0.0m;

        public decimal TaxExclAmount { get; set; } = 0.0m;
        public decimal TaxInclAmount { get; set; } = 0.0m;
        public decimal TotalUnitAmountWOTax { get; set; } = 0.0m;
        public decimal TotalUnitAmount { get; set; } = 0.0m;
        public decimal SubTotalAmount { get; set; } = 0.0m;
    }
}