﻿using Dolfin.Framework.Data.Domains;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Dolfin.Mobile.API.Models.Request
{
    public class UpdateProductUOMRequest
    {
        [JsonIgnore]
        public Guid Id { get; set; }

        public string? Name { get; set; }

        public string? Description { get; set; }

        public bool? IsMainUom { get; set; }

        public decimal? OrderMinQty { get; set; }

        public decimal? OrderMaxQty { get; set; }

        public bool? PriceEditable { get; set; }

        public decimal? MinEditPrice { get; set; }

        public decimal? MaxEditPrice { get; set; }

        public Guid? CompanyId { get; set; }
    }
}
