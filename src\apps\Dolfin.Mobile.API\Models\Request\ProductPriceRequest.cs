﻿
using System.ComponentModel.DataAnnotations;

namespace Dolfin.Mobile.API.Models.Request
{
    public class ProductPriceRequest
    {
        [Range(1.00, double.MaxValue, ErrorMessage = "FractionQty must be at least 1")]
        public decimal FractionQty { get; set; } = 1.00m;
        [Required(ErrorMessage = "Price is required")]
        [Range(0.00, double.MaxValue, ErrorMessage = "Price must be greater than 0")]
        public decimal Price { get; set; }

        // EffectiveAt is not required, will be set to UTC now by default
        public DateTime? EffectiveAt { get; set; } = DateTime.UtcNow;

        public string? Remark { get; set; }
        public Guid ProductUOMId { get; set; }
    }
}
