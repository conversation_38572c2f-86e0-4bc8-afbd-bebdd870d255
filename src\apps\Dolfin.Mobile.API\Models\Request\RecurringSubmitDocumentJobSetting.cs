﻿﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Dolfin.Mobile.API.Models.Request
{
    public class RecurringSubmitDocumentJobSetting : RecurringJobOption
    {
        /// <summary>
        /// Maximum number of documents to process in a single batch
        /// </summary>
        public int BatchSize { get; set; } = 10;
        
        /// <summary>
        /// Maximum number of retry attempts for failed submissions
        /// </summary>
        public int MaxRetries { get; set; } = 3;
    }
}
