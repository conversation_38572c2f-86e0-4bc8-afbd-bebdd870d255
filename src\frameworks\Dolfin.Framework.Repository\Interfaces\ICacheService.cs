using System;
using System.Threading.Tasks;

namespace Dolfin.Framework.Repository.Interfaces
{
    /// <summary>
    /// Interface for cache service operations
    /// </summary>
    public interface ICacheService
    {
        /// <summary>
        /// Gets an item from the cache or creates it if it doesn't exist
        /// </summary>
        /// <typeparam name="T">Type of the cached item</typeparam>
        /// <param name="key">Cache key</param>
        /// <param name="factory">Function to create the item if not found in cache</param>
        /// <param name="absoluteExpirationMinutes">Optional absolute expiration time in minutes</param>
        /// <param name="slidingExpirationMinutes">Optional sliding expiration time in minutes</param>
        /// <returns>The cached item</returns>
        T GetOrCreate<T>(string key, Func<T> factory, int? absoluteExpirationMinutes = null, int? slidingExpirationMinutes = null);

        /// <summary>
        /// Gets an item from the cache or creates it if it doesn't exist asynchronously
        /// </summary>
        /// <typeparam name="T">Type of the cached item</typeparam>
        /// <param name="key">Cache key</param>
        /// <param name="factory">Async function to create the item if not found in cache</param>
        /// <param name="absoluteExpirationMinutes">Optional absolute expiration time in minutes</param>
        /// <param name="slidingExpirationMinutes">Optional sliding expiration time in minutes</param>
        /// <returns>The cached item</returns>
        Task<T> GetOrCreateAsync<T>(string key, Func<Task<T>> factory, int? absoluteExpirationMinutes = null, int? slidingExpirationMinutes = null);

        /// <summary>
        /// Removes an item from the cache
        /// </summary>
        /// <param name="key">Cache key</param>
        void Remove(string key);

        /// <summary>
        /// Checks if an item exists in the cache
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>True if the item exists, false otherwise</returns>
        bool Exists(string key);
    }
}
