﻿using Dolfin.Mobile.API.Helper;
using Dolfin.Mobile.API.Models.Request;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.DependencyInjection;
using Dolfin.Framework.Data.Entity;
using System;
using System.Threading;

namespace Dolfin.Mobile.API.Scheduler
{
    internal class RecurringSendEmailJob : BaseRecurringJob
    {
        private readonly RecurringSendEmailJobSetting _jobSetting;
        private readonly ILogger _logger;
        private readonly IServiceProvider _serviceProvider;

        public RecurringSendEmailJob(
            ILogger<RecurringSendEmailJob> logger,
            IOptions<RecurringSendEmailJobSetting> jobSetting,
            IServiceProvider serviceProvider)
        {
            _logger = logger;
            _jobSetting = jobSetting.Value;
            _serviceProvider = serviceProvider;
        }

        public override void Execute()
        {
            try
            {
                _logger.LogInformation($"[{this.Name}] Job Started at {DateTime.Now}");
                _logger.LogInformation($"[{this.Name}] Using cron expression: {_jobSetting.CronExpression}");

                // Create a scope to resolve scoped services
                using (var scope = _serviceProvider.CreateScope())
                {
                    var scopedProvider = scope.ServiceProvider;

                    // Get the email service from the scoped provider
                    var emailService = scopedProvider.GetRequiredService<EmailSchedulerService>();

                    // Execute the email batch sending
                    emailService.SendEmailBatch();
                }

                _logger.LogInformation($"[{this.Name}] Job Completed at {DateTime.Now}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{this.Name}] Error executing recurring job: {ex.Message}");
                // Continue execution despite errors - don't rethrow
            }
        }
    }
}