using AutoMapper;
using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Entity;
using Dolfin.Mobile.API.Helper;
using Dolfin.Mobile.API.Models.Request;
using Dolfin.Utility.Enum;
using Dolfin.Utility.Models;
using Dolfin.Utility.Utils;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using static Dolfin.Utility.Enum.Enums;

namespace Dolfin.Mobile.API.Services
{
    public class PrefixService : BaseComponent<Prefix>, IPrefixService
    {
        private readonly StandardMessage _standardMessage;
        private readonly IUserService _userService;
        private readonly ILogger<PrefixService> _logger;
        private readonly IMapper _mapper;

        public PrefixService(DbContextOptions<DolfinDbContext> dbContextOptions, IUserService userService, ILoggerFactory loggerFactory, IMapper mapper) : base(dbContextOptions)
        {
            _standardMessage = new StandardMessage();
            _userService = userService;
            _logger = loggerFactory.CreateLogger<PrefixService>();
            _mapper = mapper;
        }

        public async Task<string> GenerateCode(string tableName, Guid branchId, DolfinDbContext dbContextRollback = null)
        {
            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();

            try
            {
                // Find the prefix for the specified table and branch
                var prefix = await dbContext.Prefix
                    .Where(p => p.TableName == tableName && p.BranchId == branchId && p.IsActive)
                    .FirstOrDefaultAsync();

                if (prefix == null)
                {
                    // Verify that the branch exists
                    var branchExists = await dbContext.Branch
                        .AnyAsync(b => b.Id == branchId && b.IsActive);

                    if (!branchExists)
                    {
                        throw new Exception($"Branch with ID {branchId} does not exist or is not active.");
                    }

                    // If no prefix exists, create a default one
                    var currentUser = await _userService.GetCurrentUserAsync();

                    prefix = new Prefix
                    {
                        TableName = tableName,
                        PrefixValue = tableName.Substring(0, Math.Min(3, tableName.Length)).ToUpper(),
                        LastNumber = 0,
                        PaddingLength = 4,
                        BranchId = branchId,
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = Guid.Parse(currentUser.Id)
                    };

                    dbContext.Prefix.Add(prefix);
                    await dbContext.SaveChangesAsync();
                }

                // Increment the last number
                prefix.LastNumber++;
                await dbContext.SaveChangesAsync();

                // Generate the code with the prefix and padded number
                string paddedNumber = prefix.LastNumber.ToString().PadLeft(prefix.PaddingLength, '0');
                return $"{prefix.PrefixValue}{paddedNumber}";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating code for table {TableName} and branch {BranchId}", tableName, branchId);

                // Return a fallback code in case of error
                return $"{tableName.Substring(0, Math.Min(3, tableName.Length)).ToUpper()}{DateTime.Now.ToString("yyyyMMddHHmmss")}";
            }
            finally
            {
                // Dispose the context if we created it
                if (isNewDbContext && dbContext != null)
                {
                    await dbContext.DisposeAsync();
                }
            }
        }

        public async Task<BaseResponse<PagedList<Prefix>>> GetPrefixList(Pagination pagination = null, CommonFilterList filterList = null, Guid? branchId = null)
        {
            var result = new BaseResponse<PagedList<Prefix>> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
                var currentUser = getCurrentUser.Item1;
                var userBranchId = currentUser.BranchId;

                // If branchId is not provided, use the current user's branch ID
                if (branchId == null && userBranchId != null)
                {
                    branchId = userBranchId;
                }

                Expression<Func<Prefix, bool>> predicate = x =>
                    (branchId == null || x.BranchId == branchId) &&
                    (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId))));

                if (filterList != null && filterList.FilterList != null && filterList.FilterList.Count > 0)
                    predicate = ExpressionExtensions.BuildPredicate(predicate, filterList);

                var query = GetDbContext().Set<Prefix>()
                    .Include(p => p.Branch)
                    .Where(predicate)
                    .AsQueryable();

                var response = await query.ToListAsync();

                result.Result = SharedFunctionHelper.StandardPagination(response, pagination);
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<PagedList<Prefix>, PagedList<Prefix>>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<Prefix>> GetPrefixByGuid(Guid prefixId)
        {
            var result = new BaseResponse<Prefix> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
                var currentUser = getCurrentUser.Item1;

                var query = GetDbContext().Set<Prefix>()
                    .Include(p => p.Branch)
                    .Where(x => x.Id == prefixId &&
                               (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                    .AsQueryable();

                var response = await query.ToListAsync();

                result.Result = response.FirstOrDefault();
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<Prefix, Prefix>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<ResultId>> InsertPrefix(PrefixRequest reqBody, DolfinDbContext dbContextRollback = null)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };

            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();
            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;
                try
                {
                    var getCurrentUser = await _userService.GetCurrentUserStdUpsertWithBranchAsync(reqBody);
                    var currentUser = getCurrentUser.Item1;
                    reqBody = getCurrentUser.Item2;

                    // Verify that the branch exists
                    var branchExists = await dbContext.Branch
                        .AnyAsync(b => b.Id == reqBody.BranchId && b.IsActive);

                    if (!branchExists)
                    {
                        throw new Exception($"Branch with ID {reqBody.BranchId} does not exist or is not active.");
                    }

                    // Check if a prefix already exists for this table and branch
                    var existingPrefix = await dbContext.Prefix
                        .Where(p => p.TableName == reqBody.TableName && p.BranchId == reqBody.BranchId && p.IsActive)
                        .FirstOrDefaultAsync();

                    if (existingPrefix != null)
                    {
                        throw new Exception($"A prefix for table {reqBody.TableName} and branch {reqBody.BranchId} already exists.");
                    }

                    var newPrefix = new Prefix
                    {
                        TableName = reqBody.TableName,
                        PrefixValue = reqBody.PrefixValue,
                        LastNumber = 0,
                        PaddingLength = reqBody.PaddingLength,
                        BranchId = (Guid)reqBody.BranchId, // Explicitly set BranchId
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = Guid.Parse(currentUser.Id)
                    };

                    dbContext.Prefix.Add(newPrefix);
                    await dbContext.SaveChangesAsync();

                    result.Result = new ResultId { Id = newPrefix.Id };

                    if (currentTransaction == null)
                        await transaction.CommitAsync();
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }
            return result;
        }

        public async Task<BaseResponse<ResultId>> UpdatePrefix(UpdatePrefixRequest reqBody)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdUpsertWithBranchAsync(reqBody);
                var currentUser = getCurrentUser.Item1;
                reqBody = getCurrentUser.Item2;

                var prefixResponse = await GetPrefixByGuid(reqBody.Id);
                if (!prefixResponse.IsSuccessful)
                    throw new Exception(prefixResponse.Exception);
                else if (prefixResponse == null || prefixResponse.Result?.Id == null)
                    throw new Exception($"Prefix with ID {reqBody.Id} not found.");
                else
                {
                    // Get the existing prefix entity
                    var existingPrefix = prefixResponse.Result;

                    // Get a database context
                    using var dbContext = GetDbContext();

                    // Only update the allowed properties directly
                    // TableName and BranchId are not allowed to be updated
                    existingPrefix.PrefixValue = reqBody.PrefixValue;
                    existingPrefix.PaddingLength = reqBody.PaddingLength;

                    // Update audit fields
                    existingPrefix.UpdatedAt = DateTime.UtcNow;
                    existingPrefix.UpdatedBy = Guid.Parse(currentUser.Id);

                    // Save the updated prefix
                    await UpdateAsync(existingPrefix);
                    result.Result = new ResultId { Id = existingPrefix.Id };
                }
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<NoResultResponse> DeletePrefix(Guid id)
        {
            var result = new NoResultResponse { IsSuccessful = true };
            try
            {
                var prefix = await GetPrefixByGuid(id);
                if (!prefix.IsSuccessful)
                    throw new Exception(prefix.Exception);
                if (prefix == null || prefix.Result?.Id == null)
                    throw new Exception($"Prefix with ID {id} not found.");

                var currentUser = await _userService.GetCurrentUserAsync();
                if (currentUser.UserRoles.Any(x => !x.RoleId.Equals(UserTypeEnum.Admin.GetAmbientValue().Value.ToString())))
                    throw new Exception($"Only administrators can delete prefixes.");

                var deletePrefix = prefix.Result;
                deletePrefix.IsActive = false;
                deletePrefix.UpdatedAt = DateTime.UtcNow;
                deletePrefix.UpdatedBy = Guid.Parse(currentUser.Id);
                await UpdateAsync(deletePrefix);
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<NoResult, NoResult>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }
    }
}
