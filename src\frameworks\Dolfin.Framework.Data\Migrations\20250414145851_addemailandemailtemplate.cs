﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Dolfin.Framework.Data.Migrations
{
    /// <inheritdoc />
    public partial class addemailandemailtemplate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedAt",
                table: "EmailAccount",
                type: "timestamp with time zone",
                nullable: false,
                defaultValueSql: "now()");

            migrationBuilder.AddColumn<Guid>(
                name: "CreatedBy",
                table: "EmailAccount",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("********-0000-0000-0000-********0000"));

            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                table: "EmailAccount",
                type: "boolean",
                nullable: false,
                defaultValueSql: "true");

            migrationBuilder.AddColumn<DateTime>(
                name: "UpdatedAt",
                table: "EmailAccount",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "UpdatedBy",
                table: "EmailAccount",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "Email",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Priority = table.Column<int>(type: "integer", nullable: false),
                    FromEmail = table.Column<string>(type: "character varying(500)", unicode: false, maxLength: 500, nullable: false),
                    FromName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    ToEmail = table.Column<string>(type: "character varying(500)", unicode: false, maxLength: 500, nullable: false),
                    ToName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    ReplyToEmail = table.Column<string>(type: "character varying(500)", unicode: false, maxLength: 500, nullable: false),
                    ReplyToName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Cc = table.Column<string>(type: "character varying(500)", unicode: false, maxLength: 500, nullable: false),
                    Bcc = table.Column<string>(type: "character varying(500)", unicode: false, maxLength: 500, nullable: false),
                    Subject = table.Column<string>(type: "text", nullable: false),
                    Body = table.Column<string>(type: "text", nullable: false),
                    AttachmentFilePath = table.Column<string>(type: "text", nullable: false),
                    AttachmentFileName = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    DontSendBeforeDateUtc = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    SentTries = table.Column<int>(type: "integer", nullable: false),
                    SentOnUtc = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    EmailAccountId = table.Column<int>(type: "integer", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValueSql: "true"),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "now()"),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Email", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Emails_EmailAccount",
                        column: x => x.EmailAccountId,
                        principalTable: "EmailAccount",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "EmailTemplate",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    BccEmailAddresses = table.Column<string>(type: "text", nullable: false),
                    Subject = table.Column<string>(type: "text", nullable: false),
                    Body = table.Column<string>(type: "text", nullable: false),
                    EmailAccountId = table.Column<int>(type: "integer", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValueSql: "true"),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "now()"),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EmailTemplate", x => x.Id);
                    table.ForeignKey(
                        name: "FK_EmailTemplate_EmailAccount",
                        column: x => x.EmailAccountId,
                        principalTable: "EmailAccount",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.UpdateData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("69ed35dc-8775-4641-9f1d-26d9d91e5f4b"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 14, 14, 58, 50, 200, DateTimeKind.Utc).AddTicks(2979));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("11fd5e73-51fa-428a-a919-960ee860b00f"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 14, 14, 58, 50, 200, DateTimeKind.Utc).AddTicks(3116));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("5d22874b-5834-40a2-81be-bf5100eb845d"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 14, 14, 58, 50, 200, DateTimeKind.Utc).AddTicks(3109));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("c219bf33-28d0-4595-a25b-8018bd87e67a"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 14, 14, 58, 50, 200, DateTimeKind.Utc).AddTicks(3106));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("e57e65d0-85a9-49c8-8463-3424a42e1f01"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 14, 14, 58, 50, 200, DateTimeKind.Utc).AddTicks(3112));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 14, 14, 58, 50, 200, DateTimeKind.Utc).AddTicks(3059));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("81ba4893-d891-4e3c-a5e7-ba13c697bf97"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 14, 14, 58, 50, 200, DateTimeKind.Utc).AddTicks(3065));

            migrationBuilder.CreateIndex(
                name: "IX_Email_EmailAccountId",
                table: "Email",
                column: "EmailAccountId");

            migrationBuilder.CreateIndex(
                name: "IX_EmailTemplate_EmailAccountId",
                table: "EmailTemplate",
                column: "EmailAccountId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Email");

            migrationBuilder.DropTable(
                name: "EmailTemplate");

            migrationBuilder.DropColumn(
                name: "CreatedAt",
                table: "EmailAccount");

            migrationBuilder.DropColumn(
                name: "CreatedBy",
                table: "EmailAccount");

            migrationBuilder.DropColumn(
                name: "IsActive",
                table: "EmailAccount");

            migrationBuilder.DropColumn(
                name: "UpdatedAt",
                table: "EmailAccount");

            migrationBuilder.DropColumn(
                name: "UpdatedBy",
                table: "EmailAccount");

            migrationBuilder.UpdateData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("69ed35dc-8775-4641-9f1d-26d9d91e5f4b"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 4, 19, 43, 39, 528, DateTimeKind.Utc).AddTicks(6760));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("11fd5e73-51fa-428a-a919-960ee860b00f"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 4, 19, 43, 39, 528, DateTimeKind.Utc).AddTicks(6897));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("5d22874b-5834-40a2-81be-bf5100eb845d"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 4, 19, 43, 39, 528, DateTimeKind.Utc).AddTicks(6893));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("c219bf33-28d0-4595-a25b-8018bd87e67a"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 4, 19, 43, 39, 528, DateTimeKind.Utc).AddTicks(6844));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("e57e65d0-85a9-49c8-8463-3424a42e1f01"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 4, 19, 43, 39, 528, DateTimeKind.Utc).AddTicks(6895));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 4, 19, 43, 39, 528, DateTimeKind.Utc).AddTicks(6808));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("81ba4893-d891-4e3c-a5e7-ba13c697bf97"),
                column: "CreatedAt",
                value: new DateTime(2025, 4, 4, 19, 43, 39, 528, DateTimeKind.Utc).AddTicks(6817));
        }
    }
}
