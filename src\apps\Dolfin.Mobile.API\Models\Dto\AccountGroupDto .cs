﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Mobile.API.Models.Dto
{
    public partial class AccountGroupDto
    {
        public AccountGroupDto()
        {
            //Customer = new HashSet<Customer>();
            //PaymentType = new HashSet<PaymentTypeDto>();
            //Transaction = new HashSet<Transaction>();
        }
        public required Guid Id { get; set; }
        public required string Code { get; set; }
        public required string Name { get; set; }
        public required string DisplayName { get; set; }
        //public virtual ICollection<Customer> Customer { get; }
        //public virtual ICollection<PaymentTypeDto> PaymentType { get; }
        //public virtual ICollection<Transaction> Transaction { get; }

    }
}
