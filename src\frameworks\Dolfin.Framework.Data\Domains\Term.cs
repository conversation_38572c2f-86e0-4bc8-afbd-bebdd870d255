﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Framework.Data.Domains
{
    public partial class Term : _BaseDomain
    {
        public Term()
        {
            Transaction = new HashSet<Transaction>();
        }
        public required string Code { get; set; }
        public required string Name { get; set; }
        public int Days { get; set; }
        public virtual ICollection<Transaction> Transaction { get; }

    }
}
