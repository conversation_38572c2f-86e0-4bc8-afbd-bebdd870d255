﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Mobile.API.Models.Dto
{
    public partial class PaymentTypeDto
    {
        public PaymentTypeDto()
        {
        }
        public Guid Id { get; set; }
        public required string Code { get; set; }
        public required string Name { get; set; }
        public required string AccountCode { get; set; }
        public Guid AccountGroupId { get; set; }
        public virtual AccountGroupDto? AccountGroup { get; set; }

    }
}
