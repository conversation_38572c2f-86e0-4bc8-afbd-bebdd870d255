<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <Import Project="Dependencies.AspNetCore.props" />

  <PropertyGroup>
    <JQueryVersion>3.7.1</JQueryVersion>
	<MailKitVersion>4.5.0</MailKitVersion>
	<NewtonsoftJsonVersion>13.0.3</NewtonsoftJsonVersion>
	<AutofacVersion>8.0.0</AutofacVersion>
	<AWSSDKSetupVersion>3.7.300</AWSSDKSetupVersion>
	<AWSSDKS3Version>3.7.307.22</AWSSDKS3Version>
	<MapsterVersion>7.4.0</MapsterVersion>
	<DataTablesVersion>2.0.2</DataTablesVersion>
	<NPOIVersion>2.7.0</NPOIVersion>
	<FileSystemAccessControlVersion>5.0.0</FileSystemAccessControlVersion>
	<iTextSharpVersion>3.4.18</iTextSharpVersion>
	<SixLaborsVersion>3.1.4</SixLaborsVersion>

	<JQueryValidationUnobtrusiveVersion>2.0.20710</JQueryValidationUnobtrusiveVersion>
	<MSCodeDesignVersion>3.1.4</MSCodeDesignVersion>
	<MSCodeAnalysisCommonVersion>4.0.0</MSCodeAnalysisCommonVersion>
	<SmartBreadcrumbsVersion>3.6.1</SmartBreadcrumbsVersion>
	<CronExpressionDescriptorVersion>2.15.0</CronExpressionDescriptorVersion>
	<HangfireVersion>1.7.17</HangfireVersion>
	<HangfireStorageVersion>1.7.0</HangfireStorageVersion>
	<SerilogVersion>1.0.0</SerilogVersion>
	<BuildWebCompilerVersion>1.12.405</BuildWebCompilerVersion>
	<TimeZoneConverterVersion>3.4.0</TimeZoneConverterVersion>
  </PropertyGroup>
</Project>
