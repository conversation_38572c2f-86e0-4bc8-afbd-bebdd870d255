﻿using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.WebUtilities;
using System.Text;
using System.Text.Encodings.Web;
using Dolfin.Framework.Data.Domains.CustomIdentity;
using Dolfin.Mobile.API.Constants;
using Dolfin.Mobile.API.Models.Dto;
using Dolfin.Mobile.API.Models.Request;
using Microsoft.AspNetCore.Identity.UI.Services;
using static Dolfin.Utility.Constant.Constant;
using Dolfin.Framework.Data.Domains;
using Dolfin.Utility.Utils;
using Dolfin.Framework.Data.Entity;
using Microsoft.EntityFrameworkCore;
using static System.Runtime.InteropServices.JavaScript.JSType;
using Dolfin.Utility.Enum;
using Dolfin.Utility.Models;
using static Dolfin.Utility.Enum.Enums;
using Microsoft.AspNetCore.Http;
using DocumentFormat.OpenXml.Spreadsheet;
using System.Transactions;
using DocumentFormat.OpenXml.InkML;
using Dolfin.Mobile.API.Helper;
using static Dolfin.Mobile.API.Constants.Constants;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Dolfin.Mobile.API.Models.Response;

using AutoMapper;
using System.Security.Claims;
using Microsoft.Extensions.Options;
using Dolfin.Mobile.API.Models;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Dolfin.Framework.Repository.Common;
using Dolfin.Mobile.API.Repositories.Interfaces;

namespace Dolfin.Mobile.API.Services

{
    public class AuthService : BaseComponent<ApplicationUser>, IAuthService
    {
        private readonly StandardMessage _standardMessage;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly RoleManager<ApplicationRole> _roleManager;
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly IEmailSender _emailSender;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IUserService _userService;
        private readonly ILogger<AuthService> _logger;
        private readonly IJwtService _jwtService;
        private readonly IMapper _mapper;
        private readonly IRefreshTokenRepository _refreshTokenRepository;
        private readonly JwtSettings _jwtSettings;

        public AuthService(
            DolfinDbContext dbContext,
            DbContextOptions<DolfinDbContext> dbContextOptions,
            UserManager<ApplicationUser> userManager,
            RoleManager<ApplicationRole> roleManager,
            SignInManager<ApplicationUser> signInManager,
            IEmailSender emailSender,
            IHttpContextAccessor httpContextAccessor,
            IUserService userService,
            ILogger<AuthService> logger,
            IJwtService jwtService,
            IMapper mapper,
            IRefreshTokenRepository refreshTokenRepository,
            IOptions<JwtSettings> jwtSettings) : base(dbContext, dbContextOptions)
        {
            _standardMessage = new StandardMessage();
            _userManager = userManager;
            _signInManager = signInManager;
            _emailSender = emailSender;
            _httpContextAccessor = httpContextAccessor;
            _userService = userService;
            _roleManager = roleManager;
            _refreshTokenRepository = refreshTokenRepository;
            _logger = logger;
            _jwtService = jwtService;
            _mapper = mapper;
            _jwtSettings = jwtSettings.Value;
        }

        public string GetContextRootPath()
        {
            //var context = _httpContextAccessor.HttpContext;
            //var contextController = context.GetRouteData()?.Values["controller"] ?? "";
            //var contextRootPath = $"{context.Request.Scheme}://{context.Request.Host}/api/{contextController}";
            return AdminConfiguration.FrontendPath;
        }

        public async Task<(BaseResponse<ResultMessage> result, string userId)> RegisterAsync(Register model, Guid? userId = null, DolfinDbContext dbContextRollback = null)
        {
            var result = new BaseResponse<ResultMessage> { IsSuccessful = true };

            List<IdentityError> _errors = new List<IdentityError>();
            var userTypeId = model.UserTypeId;
            IdentityResult identityResult = null;
            string? newUserId = null;

            try
            {
#if !DEBUG
                // when generate Admin and Admin create company then change !DEBUG to DEBUG
                if (userTypeId == null)
                {
                    result = _standardMessage.ErrorMessage<ResultMessage, ResultMessage>(
                        result,
                        Enums.StatusCode.BadRequest,
                        exception: new Exception("userTypeId is required.")
                    );

                    return (
                        result: result,
                        userId: null
                    );
                }
#else
                // for client create user use
                var userAccessValidatation = await _userService.UserAccessValidatation(true);
                if (!userAccessValidatation.IsSuccessful)
                {
                    result = _standardMessage.ErrorMessage<ResultMessage, ResultMessage>(
                        result,
                        Enums.StatusCode.Unauthorized,
                        exception: new Exception(userAccessValidatation.Exception)
                    );

                    return (
                        result: result,
                        userId: null
                    );
                }

                userTypeId = (Guid)userAccessValidatation.Result.Id;

                if (model.BranchId == null || model.CompanyId == null)
                {
                    result = _standardMessage.ErrorMessage<ResultMessage, ResultMessage>(
                        result,
                        Enums.StatusCode.BadRequest,
                        exception: new Exception("BranchId and CompanyId is required.")
                    );

                    return (
                        result: result,
                        userId: null
                    );
                }

                var validateMaxUserGenerate = await ValidateMaxUserGenerate((Guid)model.BranchId);
                if (!validateMaxUserGenerate)
                {
                    result = _standardMessage.ErrorMessage<ResultMessage, ResultMessage>(
                        result,
                        Enums.StatusCode.Unauthorized,
                        exception: new Exception(StandardErrorMessage.MaxUserGenerateByCompany)
                    );

                    return (
                        result: result,
                        userId: null
                    );
                }
#endif

                var dbContext = dbContextRollback ?? GetDbContext();
                var currentDateTime = DateTime.UtcNow;
                var user = new ApplicationUser
                {
                    UserName = string.IsNullOrEmpty(model.Username) ? model.Email : model.Username,
                    Email = model.Email,
                    PasswordExpireAt = currentDateTime.AddDays(AdminConfiguration.PasswordExpiredDay),
                    PhoneNo1 = model.PhoneNo1,
                    PhoneNo2 = model.PhoneNo2,
                    FaxNo1 = model.FaxNo1,
                    FaxNo2 = model.FaxNo2,
                    SerialNo = model.SerialNo,
                    CompanyId = model.CompanyId,
                    BranchId = model.BranchId,
                    LastLoginAt = currentDateTime,
                    IsActive = true,
                    CreatedAt = currentDateTime,
                    CreatedBy = (Guid)userTypeId
                };
                //if (model.BranchId != null)
                //    user.Branch = await dbContext.Branch.FindAsync(model.BranchId);
                //if (model.CompanyId != null)
                //    user.Company = await dbContext.Company.FindAsync(model.CompanyId);

#if DEBUG
                if (userTypeId == (Guid)UserTypeEnum.Admin.GetAmbientValue())
                    user.Id = userTypeId.ToString();
#endif

                var roleName = model.UserTypeId != null ? Enums.GetValueFromAmbient<UserTypeEnum>((Guid)model.UserTypeId).GetDescription() : UserTypeEnum.User.GetDescription();
                var role = await _roleManager.FindByNameAsync(roleName);
                if (role == null)
                {
                    result = _standardMessage.ErrorMessage<ResultMessage, ResultMessage>(
                        result,
                        Enums.StatusCode.BadRequest,
                        exception: new Exception("Role does not exist.")
                    );

                    return (
                        result: result,
                        userId: null
                    );
                }

                var createUserResult = await _userManager.CreateAsync(user, model.Password);
                if (createUserResult.Succeeded)
                {
                    newUserId = user.Id;
                    var userRoleResult = await _userManager.AddToRoleAsync(user, role.Name.ToString());

                    if (userRoleResult.Succeeded)
                    {
                        var code = await _userManager.GenerateEmailConfirmationTokenAsync(user);
                        var encodedCode = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(code));
                        var callbackUrl = $"{GetContextRootPath()}/confirm-email?userId={user.Id}&code={encodedCode}";

                        //await _emailSender.SendEmailAsync(model.Email, "Confirm your account",
                        //    $"Please confirm your account by <a href='{HtmlEncoder.Default.Encode(callbackUrl)}'>clicking here</a>.");

                        var template = await _emailSender.GetEmailTemplate(EMAIL_TEMPLATE.RESENDCONFIRMATIONEMAIL);
                        template.Body = template.Body.Replace("{url}", callbackUrl);
                        await _emailSender.InsertEmail(template, template.EmailAccount, user.Email, user?.Branch?.Name ?? user.Email);
                    }
                    else
                    {
                        throw new Exception("User Roles failed: " + string.Join(", ", userRoleResult.Errors.Select(e => e.Description)));
                    }
                }
                else
                {
                    // Handle failure and transaction will be rolled back
                    throw new Exception("User creation failed " + string.Join(", ", createUserResult.Errors.Select(e => e.Description)));
                }
                result.Result = new ResultMessage();
                result.Result.DisplayMessage = "Registration successful. Please check your email for confirmation.";
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<ResultMessage, ResultMessage>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }

            return (
                result: result,
                userId: newUserId
            );
        }

        public async Task<BaseResponse<ResultMessage>> UpdateAsync(Register model, string userId, DolfinDbContext dbContextRollback = null)
        {
            // Initialize the response with default values
            var result = new BaseResponse<ResultMessage> { IsSuccessful = true };

            try
            {
                var dbContext = dbContextRollback ?? GetDbContext();
                var currentDateTime = DateTime.UtcNow;
                var userTypeId = model.UserTypeId;

                var user = await _userManager.FindByIdAsync(userId);
                if (user == null)
                {
                    // Use standardMessage for consistent error handling
                    result = _standardMessage.ErrorMessage<ResultMessage, ResultMessage>(
                        result,
                        Enums.StatusCode.BadRequest,
                        exception: new Exception("User not found.")
                    );

                    return result;
                }

                user.BranchId = model.BranchId;
                user.CompanyId = model.CompanyId;
                user.UpdatedAt = currentDateTime;
                user.UpdatedBy = (Guid)userTypeId;
                // more to update..

                var updateResult = await _userManager.UpdateAsync(user);

                if (!updateResult.Succeeded)
                {
                    // Handle failure and transaction will be rolled back
                    throw new Exception("User update failed " + string.Join(", ", updateResult.Errors.Select(e => e.Description)));
                }

                // Update the response with success information
                result.Result = new ResultMessage();
                result.Result.DisplayMessage = "User update successfully";
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<ResultMessage, ResultMessage>(
                    result,
                    Enums.StatusCode.InternalServerError,
                    exception: ex
                );
            }
            return result;
        }

        public async Task<BaseResponse<AuthResultDto>> LoginAsync(Login model)
        {
            var result = new BaseResponse<AuthResultDto> { IsSuccessful = true };

            try
            {
                _logger.LogInformation("Login attempt for user {Email}", model.Email);

                // Sign in with email and password directly
                var signInResult = await _signInManager.PasswordSignInAsync(model.Email, model.Password, isPersistent: true, lockoutOnFailure: true);

                if (!signInResult.Succeeded)
                {
                    _logger.LogWarning("Failed login attempt for user {Email}", model.Email);

                    // Use standardMessage for consistent error handling
                    result = _standardMessage.ErrorMessage<AuthResultDto, AuthResultDto>(
                    result,
                        Enums.StatusCode.Unauthorized,
                        exception: new Exception("Invalid login attempt")
                    );

                    return result;
                }

                var user = await _userService.GetCurrentUserAsync();
                if (user == null)
                {
                    _logger.LogWarning("User not found after successful sign-in for {Email}", model.Email);

                    result = _standardMessage.ErrorMessage<AuthResultDto, AuthResultDto>(
                        result,
                        Enums.StatusCode.Unauthorized,
                        exception: new Exception("User not found")
                    );

                    return result;
                }

                var isAdminRoles = await _userManager.IsInRoleAsync(user, UserTypeEnum.Admin.GetDescription());
                DateTime companySubExpiredAt = DateTime.UtcNow;
                if (!isAdminRoles)
                {
                    if (user.CompanyId == null)
                    {
                        _logger.LogWarning("Company ID not found for user {UserId}", user.Id);

                        // Use standardMessage for consistent error handling
                        await LogoutAsync();
                        result = _standardMessage.ErrorMessage<AuthResultDto, AuthResultDto>(
                            result,
                            Enums.StatusCode.BadRequest,
                            exception: new Exception("Company not found")
                        );

                        return result;
                    }

                    var query = GetDbContext().Set<Company>().AsQueryable()
                        .Where(x => x.Id == user.CompanyId && x.IsActive);

                    var company = await query.FirstOrDefaultAsync();
                    if (company == null)
                    {
                        _logger.LogWarning("Active company not found for user {UserId} with company ID {CompanyId}",
                            user.Id, user.CompanyId);

                        await LogoutAsync();
                        // Use standardMessage for consistent error handling
                        result = _standardMessage.ErrorMessage<AuthResultDto, AuthResultDto>(
                            result,
                            Enums.StatusCode.BadRequest,
                            exception: new Exception("Company not found")
                        );

                        return result;
                    }

                    companySubExpiredAt = company.ExpiredAt;
                }

                if (!isAdminRoles && (!user.IsActive || user.PasswordExpireAt < DateTime.UtcNow || companySubExpiredAt < DateTime.UtcNow))
                {
                    _logger.LogWarning("User account is inactive or expired for user {UserId}", user.Id);

                    await LogoutAsync();

                    // Use standardMessage for consistent error handling
                    result = _standardMessage.ErrorMessage<AuthResultDto, AuthResultDto>(
                        result,
                        Enums.StatusCode.Unauthorized,
                        exception: new Exception("Invalid login attempt")
                    );

                    return result;
                }

                // Invalidate user cache before updating
                _userService.InvalidateUserCache(user.Id);
                _logger.LogInformation("Invalidated cache for user {UserId} using key {CacheKey} before login update",
                    user.Id, CacheKeys.User.GetCurrentUserKey(user.Id));

                // Update last login time
                user.LastLoginAt = DateTime.UtcNow;
                await _userManager.UpdateAsync(user);

                // Get user roles for token generation
                var roles = await _userManager.GetRolesAsync(user);

                // Generate tokens and create response using the standardized method
                var authResult = await _jwtService.GenerateTokensAndCreateResponseAsync(user, roles, "Login successful");
                result.Result = authResult;

                // If cookie authentication is enabled, sign in the user with ASP.NET Core Identity
                if (_jwtSettings.UseCookieAuthentication)
                {
                    await SignInCookies(user, roles, authResult.TokenId);
                }

                _logger.LogInformation("User {UserId} logged in successfully", user.Id);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login process");

                // Use standardMessage for consistent error handling
                result = _standardMessage.ErrorMessage<AuthResultDto, AuthResultDto>(
                    result,
                    Enums.StatusCode.InternalServerError,
                    exception: ex
                );

                return result;
            }
        }

        public async Task<BaseResponse<ResultMessage>> LogoutAsync()
        {
            var result = new BaseResponse<ResultMessage> { IsSuccessful = true };
            try
            {
                _logger.LogInformation("Logging out user");

                // Get the current user
                var user = await _userService.GetCurrentUserAsync();

                // Sign out from Identity
                await _signInManager.SignOutAsync();

                if (_httpContextAccessor.HttpContext != null)
                {
                    // Get refresh token from cookie
                    if (_httpContextAccessor.HttpContext.Request.Cookies.TryGetValue("X-Refresh-Token", out var refreshToken))
                    {
                        // Revoke the specific refresh token
                        await _refreshTokenRepository.RevokeAsync(refreshToken, "User logged out");
                    }

                    // Clear JWT token cookies
                    _httpContextAccessor.HttpContext.Response.Cookies.Delete("X-Access-Token");
                    _httpContextAccessor.HttpContext.Response.Cookies.Delete("X-Refresh-Token");

                    // If cookie authentication is enabled, sign out from cookie authentication
                    if (_jwtSettings.UseCookieAuthentication)
                    {
                        await _httpContextAccessor.HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
                        _logger.LogInformation("Signed out from cookie authentication");
                    }

                    _logger.LogInformation("JWT token cookies cleared");
                }
                else
                {
                    _logger.LogWarning("HttpContext is null, cannot clear JWT token cookies");
                }

                // If we have a user, revoke all their refresh tokens and invalidate cache
                if (user != null)
                {
                    var revokedCount = await _refreshTokenRepository.RevokeAllUserTokensAsync(user.Id, "User logged out");
                    _logger.LogInformation("Revoked {Count} refresh tokens for user {UserId}", revokedCount, user.Id);

                    // Invalidate user cache using the centralized cache key
                    _userService.InvalidateUserCache(user.Id);
                    _logger.LogInformation("Invalidated cache for user {UserId} using key {CacheKey}",
                        user.Id, CacheKeys.User.GetCurrentUserKey(user.Id));
                }

                result.Result = new ResultMessage();
                result.Result.DisplayMessage = "Logout successful";

                _logger.LogInformation("User logged out successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during logout process");

                result = _standardMessage.ErrorMessage<ResultMessage, ResultMessage>(
                    result,
                    Enums.StatusCode.InternalServerError,
                    exception: new Exception("Logout failed", ex)
                );
            }
            return result;
        }

        public async Task<BaseResponse<ResultMessage>> ConfirmEmailAsync(string userId, string code)
        {
            var result = new BaseResponse<ResultMessage> { IsSuccessful = true };

            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null)
                {
                    result = _standardMessage.ErrorMessage<ResultMessage, ResultMessage>(
                        result,
                        Enums.StatusCode.Unauthorized,
                        exception: new Exception("User not found")
                    );

                    return result;
                }

                var decodedCode = Encoding.UTF8.GetString(WebEncoders.Base64UrlDecode(code));
                var confirmEmailResponse = await _userManager.ConfirmEmailAsync(user, decodedCode);
                if (!confirmEmailResponse.Succeeded)
                {
                    throw new Exception("Error confirming email");
                }
                result.Result = new ResultMessage();
                result.Result.DisplayMessage = "Email confirmed successfully";
                return result;
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<ResultMessage, ResultMessage>(result, Enums.StatusCode.InternalServerError, exception: ex);
                return result;
            }
        }

        public async Task<BaseResponse<ResultMessage>> ResendConfirmationEmailAsync(string userId)
        {
            var result = new BaseResponse<ResultMessage> { IsSuccessful = true };

            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null)
                {
                    result = _standardMessage.ErrorMessage<ResultMessage, ResultMessage>(
                        result,
                        Enums.StatusCode.Unauthorized,
                        exception: new Exception("User not found")
                    );

                    return result;
                }

                var code = await _userManager.GenerateEmailConfirmationTokenAsync(user);
                var encodedCode = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(code));
                var callbackUrl = $"{GetContextRootPath()}/confirm-email?userId={user.Id}&code={encodedCode}";

                //await _emailSender.SendEmailAsync(user.Email, "Confirm your account",
                //    $"Please confirm your account by <a href='{HtmlEncoder.Default.Encode(callbackUrl)}'>clicking here</a>.");

                var template = await _emailSender.GetEmailTemplate(EMAIL_TEMPLATE.RESENDCONFIRMATIONEMAIL);
                template.Body = template.Body.Replace("{url}", callbackUrl);
                await _emailSender.InsertEmail(template, template.EmailAccount, user.Email, user?.Branch?.Name ?? user.Email);
                result.Result = new ResultMessage();
                result.Result.DisplayMessage = "Confirmation email resent.";
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<ResultMessage, ResultMessage>(result, Enums.StatusCode.InternalServerError, exception: ex);

            }
            return result;
        }

        public async Task<BaseResponse<ResultMessage>> ForgotPasswordAsync(ForgotPassword model)
        {
            var result = new BaseResponse<ResultMessage> { IsSuccessful = true };

            try
            {
                var user = await _userManager.FindByEmailAsync(model.Email);
                if (user == null || !(await _userManager.IsEmailConfirmedAsync(user)))
                {
                    // Use standardMessage for consistent error handling
                    result = _standardMessage.ErrorMessage<ResultMessage, ResultMessage>(
                        result,
                        Enums.StatusCode.BadRequest,
                        exception: new Exception("User not found or email not confirmed.")
                    );
                    return result;
                }
                else if (user.PasswordExpireAt < DateTime.UtcNow)
                {
                    await LogoutAsync();
                    // Use standardMessage for consistent error handling
                    result = _standardMessage.ErrorMessage<ResultMessage, ResultMessage>(
                        result,
                        Enums.StatusCode.BadRequest,
                        exception: new Exception("Password Expired, please connect admin.")
                    );
                    return result;
                }

                var code = await _userManager.GeneratePasswordResetTokenAsync(user);
                var encodedCode = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(code));
                var callbackUrl = $"{GetContextRootPath()}/reset-password?email={model.Email}&code={encodedCode}";

                //await _emailSender.SendEmailAsync(model.Email, "Reset your password",
                //    $"Please reset your password by <a href='{HtmlEncoder.Default.Encode(callbackUrl)}'>clicking here</a>.");

                var template = await _emailSender.GetEmailTemplate(EMAIL_TEMPLATE.RESETPASSWORD);
                template.Body = template.Body.Replace("{url}", callbackUrl);
                await _emailSender.InsertEmail(template, template.EmailAccount, user.Email, user?.Branch?.Name ?? user.Email);
                result.Result = new ResultMessage();
                result.Result.DisplayMessage = "Password reset link sent.";
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<ResultMessage, ResultMessage>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<ResultMessage>> ResetPasswordAsync(ResetPassword model)
        {
            var result = new BaseResponse<ResultMessage> { IsSuccessful = true };

            try
            {
                var user = await _userManager.FindByEmailAsync(model.Email);
                if (user == null)
                {
                    // Use standardMessage for consistent error handling
                    result = _standardMessage.ErrorMessage<ResultMessage, ResultMessage>(
                        result,
                        Enums.StatusCode.BadRequest,
                        exception: new Exception("User not found or email not confirmed.")
                    );
                    return result;
                }
                else if (user.PasswordExpireAt < DateTime.UtcNow)
                {
                    await LogoutAsync();
                    // Use standardMessage for consistent error handling
                    result = _standardMessage.ErrorMessage<ResultMessage, ResultMessage>(
                        result,
                        Enums.StatusCode.BadRequest,
                        exception: new Exception("Password Expired, please connect admin.")
                    );
                    return result;
                }

                var decodedCode = Encoding.UTF8.GetString(WebEncoders.Base64UrlDecode(model.Token));
                var resetPasswordResult = await _userManager.ResetPasswordAsync(user, decodedCode, model.NewPassword);

                if (!resetPasswordResult.Succeeded)
                {
                    throw new Exception("User reset password failed " + string.Join(", ", resetPasswordResult.Errors.Select(e => e.Description)));
                }
                else
                {
                    result.Result = new ResultMessage();
                    result.Result.DisplayMessage = "Password reset successful.";
                }            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<ResultMessage, ResultMessage>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<ResultMessage>> ChangePasswordAsync(string userName, ChangePassword model)
        {
            var result = new BaseResponse<ResultMessage> { IsSuccessful = true };

            try
            {
                var user = await _userManager.FindByNameAsync(userName);
                if (user == null)
                {
                    // Use standardMessage for consistent error handling
                    result = _standardMessage.ErrorMessage<ResultMessage, ResultMessage>(
                        result,
                        Enums.StatusCode.BadRequest,
                        exception: new Exception("User not found or email not confirmed.")
                    );
                    return result;
                }
                else if (user.PasswordExpireAt < DateTime.UtcNow)
                {
                    await LogoutAsync();
                    // Use standardMessage for consistent error handling
                    result = _standardMessage.ErrorMessage<ResultMessage, ResultMessage>(
                        result,
                        Enums.StatusCode.BadRequest,
                        exception: new Exception("Password Expired, please connect admin.")
                    );
                    return result;
                }

                var changePasswordResponse = await _userManager.ChangePasswordAsync(user, model.CurrentPassword, model.NewPassword);
                if (!changePasswordResponse.Succeeded)
                {
                    throw new Exception("User change password failed " + string.Join(", ", changePasswordResponse.Errors.Select(e => e.Description)));
                }
                result.Result = new ResultMessage();
                result.Result.DisplayMessage = "Password changed successfully.";

                return result;
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<ResultMessage, ResultMessage>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        private async Task<bool> ValidateMaxUserGenerate(Guid branchId)
        {
            bool validMaxUserGenerateByCompany = true;
            try
            {
                var branchUsersResult = await _userService.GetUserByBranchId(branchId);
                if (branchUsersResult != null && branchUsersResult.Count() > AdminConfiguration.MaxUserGenerateByCompany)
                {
                    validMaxUserGenerateByCompany = false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating max user generation for branch {BranchId}", branchId);
                throw; // Re-throw the original exception to preserve the stack trace
            }
            return validMaxUserGenerateByCompany;
        }

        /// <summary>
        /// Refresh an access token using a refresh token
        /// </summary>
        /// <param name="refreshToken">The refresh token</param>
        /// <param name="accessToken">The expired access token</param>
        /// <returns>A response containing a new access token and refresh token</returns>
        public async Task<BaseResponse<AuthResultDto>> RefreshTokenAsync(string refreshToken, string? accessToken)
        {
            var result = new BaseResponse<AuthResultDto> { IsSuccessful = true };

            try
            {
                _logger.LogInformation("Processing refresh token request");

                if (string.IsNullOrEmpty(refreshToken))
                {
                    _logger.LogWarning("Refresh token is null or empty");
                    return _standardMessage.ErrorMessage<AuthResultDto, AuthResultDto>(
                        result,
                        Enums.StatusCode.BadRequest,
                        exception: new Exception("Refresh token is required")
                    );
                }

                if (string.IsNullOrEmpty(accessToken))
                {
                    _logger.LogWarning("Access token is null or empty");
                    return _standardMessage.ErrorMessage<AuthResultDto, AuthResultDto>(
                        result,
                        Enums.StatusCode.BadRequest,
                        exception: new Exception("Access token is required")
                    );
                }

                // Get the refresh token from the database
                var storedRefreshToken = await _refreshTokenRepository.GetByTokenAsync(refreshToken);
                if (storedRefreshToken == null)
                {
                    _logger.LogWarning("Refresh token not found in database");
                    return _standardMessage.ErrorMessage<AuthResultDto, AuthResultDto>(
                        result,
                        Enums.StatusCode.BadRequest,
                        exception: new Exception("Invalid refresh token")
                    );
                }

                // Check if the token is valid
                if (storedRefreshToken.ExpiryDate < DateTime.UtcNow)
                {
                    _logger.LogWarning("Refresh token expired");
                    return _standardMessage.ErrorMessage<AuthResultDto, AuthResultDto>(
                        result,
                        Enums.StatusCode.BadRequest,
                        exception: new Exception("Refresh token expired")
                    );
                }

                if (storedRefreshToken.Invalidated)
                {
                    _logger.LogWarning("Refresh token invalidated");
                    return _standardMessage.ErrorMessage<AuthResultDto, AuthResultDto>(
                        result,
                        Enums.StatusCode.BadRequest,
                        exception: new Exception("Refresh token invalidated")
                    );
                }

                if (storedRefreshToken.Used)
                {
                    _logger.LogWarning("Refresh token already used");
                    return _standardMessage.ErrorMessage<AuthResultDto, AuthResultDto>(
                        result,
                        Enums.StatusCode.BadRequest,
                        exception: new Exception("Refresh token already used")
                    );
                }

                // Get the user from the refresh token
                var userId = storedRefreshToken.UserId;
                var user = await _userManager.FindByIdAsync(userId.ToString());
                if (user == null)
                {
                    _logger.LogWarning("User not found for refresh token");
                    return _standardMessage.ErrorMessage<AuthResultDto, AuthResultDto>(
                        result,
                        Enums.StatusCode.Unauthorized,
                        exception: new Exception("User not found")
                    );
                }

                // Mark the current refresh token as used
                storedRefreshToken.Used = true;
                await _refreshTokenRepository.UpdateAsync(storedRefreshToken);

                // Get user roles
                var roles = await _userManager.GetRolesAsync(user);

                // Generate tokens and create response using the standardized method
                var authResult = await _jwtService.GenerateTokensAndCreateResponseAsync(user, roles, "Token refreshed successfully");
                result.Result = authResult;

                _logger.LogInformation("Token refreshed successfully for user {UserId}", user.Id);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during token refresh");
                return _standardMessage.ErrorMessage<AuthResultDto, AuthResultDto>(
                    result,
                    Enums.StatusCode.InternalServerError,
                    exception: ex
                );
            }
        }

        /// <summary>
        /// Revoke a refresh token, preventing it from being used to obtain new access tokens
        /// </summary>
        /// <param name="token">The refresh token to revoke</param>
        /// <param name="reason">Optional reason for revocation</param>
        /// <returns>A response indicating success or failure</returns>
        public async Task<BaseResponse<ResultMessage>> RevokeTokenAsync(string token, string? reason = null)
        {
            var result = new BaseResponse<ResultMessage> { IsSuccessful = true };

            try
            {
                _logger.LogInformation("Processing token revocation request for token: {TokenPrefix}",
                    token?.Length > 6 ? string.Concat(token.AsSpan(0, 6), "...") : token);

                if (string.IsNullOrEmpty(token))
                {
                    _logger.LogWarning("Token is null or empty");
                    return _standardMessage.ErrorMessage<ResultMessage, ResultMessage>(
                        result,
                        Enums.StatusCode.BadRequest,
                        statusMessage: "Token is required",
                        exception: new Exception("Token is required")
                    );
                }

                // Attempt to revoke the token
                var success = await _refreshTokenRepository.RevokeAsync(token, reason);

                if (!success)
                {
                    _logger.LogWarning("Token revocation failed: Token not found or already invalidated");
                    return _standardMessage.ErrorMessage<ResultMessage, ResultMessage>(
                        result,
                        Enums.StatusCode.BadRequest,
                        statusMessage: "Invalid token or token already revoked",
                        exception: new Exception("Invalid token or token already revoked")
                    );
                }

                _logger.LogInformation("Token revoked successfully");
                result.StatusCode = (int)Enums.StatusCode.Success;
                result.StatusMessage = "Success";
                result.Result = new ResultMessage { DisplayMessage = "Token revoked successfully" };

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during token revocation");
                return _standardMessage.ErrorMessage<ResultMessage, ResultMessage>(
                    result,
                    Enums.StatusCode.InternalServerError,
                    exception: ex
                );
            }
        }

        private async Task SignInCookies(ApplicationUser user, IList<string> roles, string tokenId)
        {
            if (_httpContextAccessor.HttpContext != null)
            {
                _logger.LogInformation("Setting up cookie authentication for user {UserId}", user.Id);

                var claims = await _jwtService.GenerateTokenClaimsAsync(user, roles, tokenId);

                var claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
                var claimsPrincipal = new ClaimsPrincipal(claimsIdentity);

                // Set authentication properties
                var authProperties = new AuthenticationProperties
                {
                    IsPersistent = true,
                    ExpiresUtc = DateTimeOffset.UtcNow.AddMinutes(_jwtSettings.ExpirationMinutes),
                    AllowRefresh = true
                };

                // Sign in the user with cookie authentication
                await _httpContextAccessor.HttpContext.SignInAsync(
                    CookieAuthenticationDefaults.AuthenticationScheme,
                    claimsPrincipal,
                    authProperties);

                _logger.LogInformation("Cookie authentication set for user {UserId}", user.Id);
            }
        }
    }
}
