﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Mobile.API.Models.Dto
{
    public partial class CurrencyDto
    {
        public CurrencyDto()
        {
            //Company = new HashSet<Company>();
            //Customer = new HashSet<Customer>();
        }
        public required Guid Id { get; set; }
        public required string Code { get; set; }
        public required string Name { get; set; }
        public required string Symbol { get; set; }
        public decimal ExchangeRate { get; set; }
        public int Precision { get; set; }
        //public virtual ICollection<Company> Company { get; }
        //public virtual ICollection<Customer> Customer { get; }

    }
}
