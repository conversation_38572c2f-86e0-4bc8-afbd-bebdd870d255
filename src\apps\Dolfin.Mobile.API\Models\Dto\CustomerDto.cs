﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Mobile.API.Models.Dto
{
    public partial class CustomerDto
    {
        public CustomerDto()
        {
            //CustomerReferral = new HashSet<CustomerDto>();
            //Transaction = new HashSet<Transaction>();
        }
        public required Guid Id { get; set; }
        public required string Code { get; set; }
        public required string Name { get; set; }
        public string? Description { get; set; }
        public bool IsTaxExempt { get; set; }
        public bool IsPICEditable { get; set; }
        public required string DefaultPIC { get; set; }
        public required string ReferralCode { get; set; }
        public required string AccountCode { get; set; }
        public string? Remark { get; set; }
        public Guid DebtorTypeId { get; set; }
        public Guid ReferrerId { get; set; }
        public Guid AddressId { get; set; }
        public Guid CurrencyId { get; set; }
        public Guid AccountGroupId { get; set; }
        public virtual DebtorTypeDto? DebtorType { get; set; }
        public virtual CustomerDto? Referrer { get; set; }
        public virtual AddressDto? Address { get; set; }
        public virtual CurrencyDto? Currency { get; set; }
        public virtual AccountGroupDto? AccountGroup { get; set; }
        //public virtual ICollection<CustomerDto> CustomerReferral { get; }
        //public virtual ICollection<Transaction> Transaction { get; }

    }
}
