﻿using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Entity;
using Dolfin.Mobile.API.Models;
using Dolfin.Mobile.API.Models.Request;
using Dolfin.Utility.Models;
using static Dolfin.Utility.Enum.Enums;

namespace Dolfin.Mobile.API.Services
{
    public interface ICompanyService
    {
        Task<BaseResponse<Company>> GetCompanyProfile(Guid? companyId);
        Task<BaseResponse<Company>> GetCompanyByUserId(Guid? userId);
        Task<BaseResponse<ResultId>> InsertCompany(CompanyRequest reqBody, DolfinDbContext dbContextRollback = null);
        void InvalidateCompanyCache(Guid companyId);
        Task<BaseResponse<PagedList<Branch>>> GetBranchList(Pagination pagination = null, CommonFilterList filterList = null, Guid? companyId = null);
        Task<BaseResponse<Branch>> GetBranchByGuid(Guid branchId);
        Task<BaseResponse<ResultId>> UpdateBranch(UpdateBranchRequest reqBody, Guid? userId = null, DolfinDbContext dbContextRollback = null);
        Task<BaseResponse<ResultId>> InsertBranch(BranchRequest reqBody, Guid? userId = null, DolfinDbContext dbContextRollback = null);
        Task<BaseResponse<PagedList<TaxCategories>>> GetTaxCategoryList(Pagination pagination = null);
        Task<BaseResponse<TaxCategories>> GetTaxCategoryByGuid(Guid taxCategoryId);
        Task<BaseResponse<TaxRate>> GetTaxRateByGuid(Guid taxRateId);
        Task<BaseResponse<ResultId>> InsertTaxRate(TaxRateRequest reqBody);
        Task<BaseResponse<ResultId>> InsertTaxCategory(TaxCategoryRequest reqBody);

        // Prefix methods
        Task<string> GenerateCode(string tableName, Guid branchId, DolfinDbContext dbContextRollback = null);
        Task<BaseResponse<PagedList<Prefix>>> GetPrefixList(Pagination pagination = null, CommonFilterList filterList = null, Guid? branchId = null);
        Task<BaseResponse<Prefix>> GetPrefixByGuid(Guid prefixId);
        Task<BaseResponse<ResultId>> InsertPrefix(PrefixRequest reqBody, DolfinDbContext dbContextRollback = null);
        Task<BaseResponse<ResultId>> UpdatePrefix(UpdatePrefixRequest reqBody);
        Task<NoResultResponse> DeletePrefix(Guid id);
    }
}
