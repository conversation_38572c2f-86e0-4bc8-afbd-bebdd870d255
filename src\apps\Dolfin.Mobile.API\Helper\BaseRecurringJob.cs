﻿using CronExpressionDescriptor;
using Hangfire;
using Microsoft.AspNetCore.Http;
using System;
using System.Runtime.CompilerServices;

namespace Dolfin.Mobile.API.Helper
{
    public abstract class BaseRecurringJob : IRecurringJob
    {
        public virtual string Name => this.GetType().ToString();

        public abstract void Execute();

        //public abstract void Register();

        //public void Register(string cronExpression)
        //{
        //    RecurringJob.AddOrUpdate(() => Execute(), cronExpression, timeZone: TimeZoneInfo.CreateCustomTimeZone("MALAYSIA TIME", TimeSpan.FromHours(8), "MYT", "MYT"));
        //}

        //protected string InterpretCronExpression(string cronExpression)
        //{
        //    return ExpressionDescriptor.GetDescription(cronExpression);
        //}
    }
}
