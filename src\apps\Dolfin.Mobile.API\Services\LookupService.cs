using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Entity;
using Dolfin.Framework.Repository.Interfaces;
using Dolfin.Mobile.API.Constants;
using Dolfin.Mobile.API.Models.Request;
using Dolfin.Mobile.API.Repositories.Interfaces;
using Dolfin.Utility.Constant;
using Dolfin.Utility.Enum;
using Dolfin.Utility.Models;
using Dolfin.Utility.Utils;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Threading.Tasks;

namespace Dolfin.Mobile.API.Services
{
    /// <summary>
    /// Service implementation for lookup operations
    /// </summary>
    public class LookupService : ILookupService
    {
        private readonly ILookupGroupRepository _lookupGroupRepository;
        private readonly ILookupItemRepository _lookupItemRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICacheService _cacheService;
        private readonly IUserService _userService;
        private readonly ILogger<LookupService> _logger;
        private readonly StandardMessage _standardMessage;

        /// <summary>
        /// Constructor
        /// </summary>
        public LookupService(
            ILookupGroupRepository lookupGroupRepository,
            ILookupItemRepository lookupItemRepository,
            IUnitOfWork unitOfWork,
            ICacheService cacheService,
            IUserService userService,
            ILogger<LookupService> logger)
        {
            _lookupGroupRepository = lookupGroupRepository;
            _lookupItemRepository = lookupItemRepository;
            _unitOfWork = unitOfWork;
            _cacheService = cacheService;
            _userService = userService;
            _logger = logger;
            _standardMessage = new StandardMessage();
        }

        #region Lookup Group Methods

        /// <inheritdoc />
        public async Task<BaseResponse<PagedList<LookupGroup>>> GetLookupGroupList(Pagination pagination = null, CommonFilterList filterList = null, Guid? companyId = null)
        {
            var result = new BaseResponse<PagedList<LookupGroup>> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync(companyId);
                var currentUser = getCurrentUser.Item1;
                companyId = getCurrentUser.Item2;

                var lookupGroups = await _lookupGroupRepository.GetAllAsync(pagination, filterList, companyId);
                result.Result = lookupGroups;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in {Method}", MethodBase.GetCurrentMethod().Name);
                result = _standardMessage.ErrorMessage<PagedList<LookupGroup>, PagedList<LookupGroup>>(
                    result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        /// <inheritdoc />
        public async Task<BaseResponse<LookupGroup>> GetLookupGroupById(Guid lookupGroupId)
        {
            var result = new BaseResponse<LookupGroup> { IsSuccessful = true };
            try
            {
                var lookupGroup = await _lookupGroupRepository.GetWithItemsAsync(lookupGroupId);
                if (lookupGroup == null)
                {
                    result.IsSuccessful = false;
                    result.StatusCode = (int)Enums.StatusCode.NotFound;
                    result.StatusMessage = "Lookup group not found";
                    return result;
                }

                result.Result = lookupGroup;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in {Method}", MethodBase.GetCurrentMethod().Name);
                result = _standardMessage.ErrorMessage<LookupGroup, LookupGroup>(
                    result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        /// <inheritdoc />
        public async Task<BaseResponse<LookupGroup>> GetLookupGroupByCode(string code, Guid? companyId = null)
        {
            var result = new BaseResponse<LookupGroup> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync(companyId);
                var currentUser = getCurrentUser.Item1;
                companyId = getCurrentUser.Item2;

                var lookupGroup = await _lookupGroupRepository.GetByCodeAsync(code, companyId);
                if (lookupGroup == null)
                {
                    result.IsSuccessful = false;
                    result.StatusCode = (int)Enums.StatusCode.NotFound;
                    result.StatusMessage = "Lookup group not found";
                    return result;
                }

                result.Result = lookupGroup;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in {Method}", MethodBase.GetCurrentMethod().Name);
                result = _standardMessage.ErrorMessage<LookupGroup, LookupGroup>(
                    result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        /// <inheritdoc />
        public async Task<BaseResponse<ResultId>> CreateLookupGroup(LookupGroupRequest request)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync(request.CompanyId);
                var currentUser = getCurrentUser.Item1;
                var companyId = getCurrentUser.Item2;

                // Check if code already exists
                if (await _lookupGroupRepository.CodeExistsAsync(request.Code, companyId))
                {
                    result.IsSuccessful = false;
                    result.StatusCode = (int)Enums.StatusCode.BadRequest;
                    result.StatusMessage = "Lookup group code already exists";
                    return result;
                }

                // Create new lookup group
                var lookupGroup = new LookupGroup
                {
                    Code = request.Code,
                    Name = request.Name,
                    Description = request.Description,
                    DisplayOrder = request.DisplayOrder,
                    CompanyId = companyId,
                    IsSystem = false,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Parse(currentUser.Id)
                };

                await _lookupGroupRepository.AddAsync(lookupGroup);
                await _unitOfWork.SaveChangesAsync();

                // Clear cache
                ClearLookupGroupCache(lookupGroup.Id, lookupGroup.Code, companyId);

                result.Result = new ResultId { Id = lookupGroup.Id };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in {Method}", MethodBase.GetCurrentMethod().Name);
                result = _standardMessage.ErrorMessage<ResultId, ResultId>(
                    result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        /// <inheritdoc />
        public async Task<BaseResponse<ResultId>> UpdateLookupGroup(UpdateLookupGroupRequest request)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync(request.CompanyId);
                var currentUser = getCurrentUser.Item1;
                var companyId = getCurrentUser.Item2;

                // Get existing lookup group
                var lookupGroup = await _lookupGroupRepository.GetByIdAsync(request.Id);
                if (lookupGroup == null)
                {
                    result.IsSuccessful = false;
                    result.StatusCode = (int)Enums.StatusCode.NotFound;
                    result.StatusMessage = "Lookup group not found";
                    return result;
                }

                // Check if system-defined
                if (lookupGroup.IsSystem)
                {
                    result.IsSuccessful = false;
                    result.StatusCode = (int)Enums.StatusCode.BadRequest;
                    result.StatusMessage = "Cannot modify system-defined lookup group";
                    return result;
                }

                // Check if code already exists (excluding current group)
                if (await _lookupGroupRepository.CodeExistsAsync(request.Code, companyId, request.Id))
                {
                    result.IsSuccessful = false;
                    result.StatusCode = (int)Enums.StatusCode.BadRequest;
                    result.StatusMessage = "Lookup group code already exists";
                    return result;
                }

                // Update lookup group
                lookupGroup.Code = request.Code;
                lookupGroup.Name = request.Name;
                lookupGroup.Description = request.Description;
                lookupGroup.DisplayOrder = request.DisplayOrder;
                lookupGroup.UpdatedAt = DateTime.UtcNow;
                lookupGroup.UpdatedBy = Guid.Parse(currentUser.Id);

                _lookupGroupRepository.Update(lookupGroup);
                await _unitOfWork.SaveChangesAsync();

                // Clear cache
                ClearLookupGroupCache(lookupGroup.Id, lookupGroup.Code, companyId);

                result.Result = new ResultId { Id = lookupGroup.Id };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in {Method}", MethodBase.GetCurrentMethod().Name);
                result = _standardMessage.ErrorMessage<ResultId, ResultId>(
                    result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        /// <inheritdoc />
        public async Task<NoResultResponse> DeleteLookupGroup(Guid lookupGroupId)
        {
            var result = new NoResultResponse { IsSuccessful = true };
            try
            {
                // Get existing lookup group
                var lookupGroup = await _lookupGroupRepository.GetByIdAsync(lookupGroupId);
                if (lookupGroup == null)
                {
                    result.IsSuccessful = false;
                    result.StatusCode = (int)Enums.StatusCode.NotFound;
                    result.StatusMessage = "Lookup group not found";
                    return result;
                }

                // Check if system-defined
                if (lookupGroup.IsSystem)
                {
                    result.IsSuccessful = false;
                    result.StatusCode = (int)Enums.StatusCode.BadRequest;
                    result.StatusMessage = "Cannot delete system-defined lookup group";
                    return result;
                }

                // Soft delete
                lookupGroup.IsActive = false;
                lookupGroup.UpdatedAt = DateTime.UtcNow;
                lookupGroup.UpdatedBy = Constant.AdminConfiguration.SystemUser;

                _lookupGroupRepository.Update(lookupGroup);
                await _unitOfWork.SaveChangesAsync();

                // Clear cache
                ClearLookupGroupCache(lookupGroup.Id, lookupGroup.Code, lookupGroup.CompanyId);

                result.Result = new NoResult();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in {Method}", MethodBase.GetCurrentMethod().Name);
                result = _standardMessage.ErrorMessage<NoResult, NoResult>(
                    result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        #endregion

        #region Lookup Item Methods

        /// <inheritdoc />
        public async Task<BaseResponse<PagedList<LookupItem>>> GetLookupItemList(Guid lookupGroupId, Pagination pagination = null, CommonFilterList filterList = null)
        {
            var result = new BaseResponse<PagedList<LookupItem>> { IsSuccessful = true };
            try
            {
                // Check if lookup group exists
                var lookupGroup = await _lookupGroupRepository.GetByIdAsync(lookupGroupId);
                if (lookupGroup == null)
                {
                    result.IsSuccessful = false;
                    result.StatusCode = (int)Enums.StatusCode.NotFound;
                    result.StatusMessage = "Lookup group not found";
                    return result;
                }

                var lookupItems = await _lookupItemRepository.GetByGroupAsync(lookupGroupId, pagination, filterList);
                result.Result = lookupItems;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in {Method}", MethodBase.GetCurrentMethod().Name);
                result = _standardMessage.ErrorMessage<PagedList<LookupItem>, PagedList<LookupItem>>(
                    result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        /// <inheritdoc />
        public async Task<BaseResponse<IEnumerable<LookupItem>>> GetAllLookupItemsByGroup(Guid lookupGroupId)
        {
            var result = new BaseResponse<IEnumerable<LookupItem>> { IsSuccessful = true };
            try
            {
                // Check if lookup group exists
                var lookupGroup = await _lookupGroupRepository.GetByIdAsync(lookupGroupId);
                if (lookupGroup == null)
                {
                    result.IsSuccessful = false;
                    result.StatusCode = (int)Enums.StatusCode.NotFound;
                    result.StatusMessage = "Lookup group not found";
                    return result;
                }

                var lookupItems = await _lookupItemRepository.GetAllByGroupAsync(lookupGroupId);
                result.Result = lookupItems;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in {Method}", MethodBase.GetCurrentMethod().Name);
                result = _standardMessage.ErrorMessage<IEnumerable<LookupItem>, IEnumerable<LookupItem>>(
                    result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        /// <inheritdoc />
        public async Task<BaseResponse<LookupItem>> GetLookupItemById(Guid lookupItemId)
        {
            var result = new BaseResponse<LookupItem> { IsSuccessful = true };
            try
            {
                var lookupItem = await _lookupItemRepository.GetByIdAsync(lookupItemId);
                if (lookupItem == null)
                {
                    result.IsSuccessful = false;
                    result.StatusCode = (int)Enums.StatusCode.NotFound;
                    result.StatusMessage = "Lookup item not found";
                    return result;
                }

                result.Result = lookupItem;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in {Method}", MethodBase.GetCurrentMethod().Name);
                result = _standardMessage.ErrorMessage<LookupItem, LookupItem>(
                    result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        /// <inheritdoc />
        public async Task<BaseResponse<ResultId>> CreateLookupItem(LookupItemRequest request)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };
            try
            {
                var currentUser = await _userService.GetCurrentUserAsync();

                // Check if lookup group exists
                var lookupGroup = await _lookupGroupRepository.GetByIdAsync(request.LookupGroupId);
                if (lookupGroup == null)
                {
                    result.IsSuccessful = false;
                    result.StatusCode = (int)Enums.StatusCode.NotFound;
                    result.StatusMessage = "Lookup group not found";
                    return result;
                }

                // Check if code already exists in the group
                if (await _lookupItemRepository.CodeExistsAsync(request.Code, request.LookupGroupId))
                {
                    result.IsSuccessful = false;
                    result.StatusCode = (int)Enums.StatusCode.BadRequest;
                    result.StatusMessage = "Lookup item code already exists in this group";
                    return result;
                }

                // Create new lookup item
                var lookupItem = new LookupItem
                {
                    Code = request.Code,
                    Value = request.Value,
                    Description = request.Description,
                    DisplayOrder = request.DisplayOrder,
                    AdditionalData = request.AdditionalData,
                    LookupGroupId = request.LookupGroupId,
                    IsSystem = false,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Parse(currentUser.Id)
                };

                await _lookupItemRepository.AddAsync(lookupItem);
                await _unitOfWork.SaveChangesAsync();

                // Clear cache
                ClearLookupItemCache(lookupItem.Id, lookupItem.LookupGroupId);

                result.Result = new ResultId { Id = lookupItem.Id };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in {Method}", MethodBase.GetCurrentMethod().Name);
                result = _standardMessage.ErrorMessage<ResultId, ResultId>(
                    result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        /// <inheritdoc />
        public async Task<BaseResponse<ResultId>> UpdateLookupItem(UpdateLookupItemRequest request)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };
            try
            {
                var currentUser = await _userService.GetCurrentUserAsync();

                // Get existing lookup item
                var lookupItem = await _lookupItemRepository.GetByIdAsync(request.Id);
                if (lookupItem == null)
                {
                    result.IsSuccessful = false;
                    result.StatusCode = (int)Enums.StatusCode.NotFound;
                    result.StatusMessage = "Lookup item not found";
                    return result;
                }

                // Check if system-defined
                if (lookupItem.IsSystem)
                {
                    result.IsSuccessful = false;
                    result.StatusCode = (int)Enums.StatusCode.BadRequest;
                    result.StatusMessage = "Cannot modify system-defined lookup item";
                    return result;
                }

                // Check if lookup group exists
                var lookupGroup = await _lookupGroupRepository.GetByIdAsync(request.LookupGroupId);
                if (lookupGroup == null)
                {
                    result.IsSuccessful = false;
                    result.StatusCode = (int)Enums.StatusCode.NotFound;
                    result.StatusMessage = "Lookup group not found";
                    return result;
                }

                // Check if code already exists in the group (excluding current item)
                if (await _lookupItemRepository.CodeExistsAsync(request.Code, request.LookupGroupId, request.Id))
                {
                    result.IsSuccessful = false;
                    result.StatusCode = (int)Enums.StatusCode.BadRequest;
                    result.StatusMessage = "Lookup item code already exists in this group";
                    return result;
                }

                // Update lookup item
                lookupItem.Code = request.Code;
                lookupItem.Value = request.Value;
                lookupItem.Description = request.Description;
                lookupItem.DisplayOrder = request.DisplayOrder;
                lookupItem.AdditionalData = request.AdditionalData;
                lookupItem.LookupGroupId = request.LookupGroupId;
                lookupItem.UpdatedAt = DateTime.UtcNow;
                lookupItem.UpdatedBy = Guid.Parse(currentUser.Id);

                _lookupItemRepository.Update(lookupItem);
                await _unitOfWork.SaveChangesAsync();

                // Clear cache
                ClearLookupItemCache(lookupItem.Id, lookupItem.LookupGroupId);

                result.Result = new ResultId { Id = lookupItem.Id };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in {Method}", MethodBase.GetCurrentMethod().Name);
                result = _standardMessage.ErrorMessage<ResultId, ResultId>(
                    result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        /// <inheritdoc />
        public async Task<NoResultResponse> DeleteLookupItem(Guid lookupItemId)
        {
            var result = new NoResultResponse { IsSuccessful = true };
            try
            {
                // Get existing lookup item
                var lookupItem = await _lookupItemRepository.GetByIdAsync(lookupItemId);
                if (lookupItem == null)
                {
                    result.IsSuccessful = false;
                    result.StatusCode = (int)Enums.StatusCode.NotFound;
                    result.StatusMessage = "Lookup item not found";
                    return result;
                }

                // Check if system-defined
                if (lookupItem.IsSystem)
                {
                    result.IsSuccessful = false;
                    result.StatusCode = (int)Enums.StatusCode.BadRequest;
                    result.StatusMessage = "Cannot delete system-defined lookup item";
                    return result;
                }

                // Soft delete
                lookupItem.IsActive = false;
                lookupItem.UpdatedAt = DateTime.UtcNow;
                lookupItem.UpdatedBy = Constant.AdminConfiguration.SystemUser;

                _lookupItemRepository.Update(lookupItem);
                await _unitOfWork.SaveChangesAsync();

                // Clear cache
                ClearLookupItemCache(lookupItem.Id, lookupItem.LookupGroupId);

                result.Result = new NoResult();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in {Method}", MethodBase.GetCurrentMethod().Name);
                result = _standardMessage.ErrorMessage<NoResult, NoResult>(
                    result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Clear cache for a lookup group
        /// </summary>
        private void ClearLookupGroupCache(Guid lookupGroupId, string code, Guid? companyId)
        {
            _cacheService.Remove(CacheKeys.Lookup.GetLookupGroupKey(lookupGroupId));
            _cacheService.Remove(CacheKeys.Lookup.GetLookupGroupByCodeKey(code));
            _cacheService.Remove(CacheKeys.Lookup.GetLookupGroupListKey());

            if (companyId.HasValue)
            {
                _cacheService.Remove(CacheKeys.Lookup.GetLookupGroupListByCompanyKey(companyId.Value));
            }
        }

        /// <summary>
        /// Clear cache for a lookup item
        /// </summary>
        private void ClearLookupItemCache(Guid lookupItemId, Guid lookupGroupId)
        {
            _cacheService.Remove(CacheKeys.Lookup.GetLookupItemKey(lookupItemId));
            _cacheService.Remove(CacheKeys.Lookup.GetLookupItemListByGroupKey(lookupGroupId));
            _cacheService.Remove(CacheKeys.Lookup.GetLookupGroupKey(lookupGroupId));
        }

        #endregion
    }
}
