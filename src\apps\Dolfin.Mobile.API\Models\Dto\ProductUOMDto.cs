﻿using Dolfin.Framework.Data.Domains;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Mobile.API.Models.Dto
{
    public partial class ProductUOMDto
    {
        public ProductUOMDto() {
            //InventoryItem = new HashSet<InventoryItem>();
            //TransactionItem = new HashSet<TransactionItem>();
        }
        public Guid Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string? Description { get; set; }
        public decimal FractionTotal { get; set; }
        public decimal Fraction { get; set; }
        public string Barcode { get; set; }
        public bool IsMainUom { get; set; }
        public bool IsPriceFollowUomMainId { get; set; }
        public decimal Cost { get; set; }
        public decimal? PreviousCost { get; set; }
        public decimal? OrderMinQty { get; set; }
        public decimal? OrderMaxQty { get; set; }
        public bool PriceEditable { get; set; }
        public decimal? MinEditPrice { get; set; }
        public decimal? MaxEditPrice { get; set; }
        public Guid UomPrimaryId { get; set; }
        public Guid UomSecondaryId { get; set; }
        public Guid ProductId { get; set; }
        public virtual UOMDto? UomPrimary { get; set; }
        public virtual UOMDto? UomSecondary { get; set; }
        //public virtual Product? Product { get; set; }
        public virtual ProductPriceDto? EffectivedProductPrice { get; set; }
        //public virtual ICollection<InventoryItem> InventoryItem { get; }
        //public virtual ICollection<TransactionItem> TransactionItem { get; }
    }
}
