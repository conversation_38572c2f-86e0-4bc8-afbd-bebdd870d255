﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Dolfin.Framework.Data.Migrations
{
    /// <inheritdoc />
    public partial class transactionwithbranch : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Transaction_Company",
                table: "Transaction");

            migrationBuilder.RenameColumn(
                name: "CompanyId",
                table: "Transaction",
                newName: "BranchId");

            migrationBuilder.RenameIndex(
                name: "IX_Transaction_CompanyId",
                table: "Transaction",
                newName: "IX_Transaction_BranchId");

            migrationBuilder.UpdateData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("69ed35dc-8775-4641-9f1d-26d9d91e5f4b"),
                column: "CreatedAt",
                value: new DateTime(2025, 3, 30, 7, 40, 57, 889, DateTimeKind.Utc).AddTicks(2072));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("11fd5e73-51fa-428a-a919-960ee860b00f"),
                column: "CreatedAt",
                value: new DateTime(2025, 3, 30, 7, 40, 57, 889, DateTimeKind.Utc).AddTicks(2166));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("5d22874b-5834-40a2-81be-bf5100eb845d"),
                column: "CreatedAt",
                value: new DateTime(2025, 3, 30, 7, 40, 57, 889, DateTimeKind.Utc).AddTicks(2161));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("c219bf33-28d0-4595-a25b-8018bd87e67a"),
                column: "CreatedAt",
                value: new DateTime(2025, 3, 30, 7, 40, 57, 889, DateTimeKind.Utc).AddTicks(2159));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("e57e65d0-85a9-49c8-8463-3424a42e1f01"),
                column: "CreatedAt",
                value: new DateTime(2025, 3, 30, 7, 40, 57, 889, DateTimeKind.Utc).AddTicks(2163));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"),
                column: "CreatedAt",
                value: new DateTime(2025, 3, 30, 7, 40, 57, 889, DateTimeKind.Utc).AddTicks(2122));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("81ba4893-d891-4e3c-a5e7-ba13c697bf97"),
                column: "CreatedAt",
                value: new DateTime(2025, 3, 30, 7, 40, 57, 889, DateTimeKind.Utc).AddTicks(2128));

            migrationBuilder.AddForeignKey(
                name: "FK_Transaction_Branch",
                table: "Transaction",
                column: "BranchId",
                principalTable: "Branch",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Transaction_Branch",
                table: "Transaction");

            migrationBuilder.RenameColumn(
                name: "BranchId",
                table: "Transaction",
                newName: "CompanyId");

            migrationBuilder.RenameIndex(
                name: "IX_Transaction_BranchId",
                table: "Transaction",
                newName: "IX_Transaction_CompanyId");

            migrationBuilder.UpdateData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("69ed35dc-8775-4641-9f1d-26d9d91e5f4b"),
                column: "CreatedAt",
                value: new DateTime(2025, 3, 29, 10, 44, 18, 163, DateTimeKind.Utc).AddTicks(587));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("11fd5e73-51fa-428a-a919-960ee860b00f"),
                column: "CreatedAt",
                value: new DateTime(2025, 3, 29, 10, 44, 18, 163, DateTimeKind.Utc).AddTicks(699));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("5d22874b-5834-40a2-81be-bf5100eb845d"),
                column: "CreatedAt",
                value: new DateTime(2025, 3, 29, 10, 44, 18, 163, DateTimeKind.Utc).AddTicks(693));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("c219bf33-28d0-4595-a25b-8018bd87e67a"),
                column: "CreatedAt",
                value: new DateTime(2025, 3, 29, 10, 44, 18, 163, DateTimeKind.Utc).AddTicks(689));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("e57e65d0-85a9-49c8-8463-3424a42e1f01"),
                column: "CreatedAt",
                value: new DateTime(2025, 3, 29, 10, 44, 18, 163, DateTimeKind.Utc).AddTicks(696));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"),
                column: "CreatedAt",
                value: new DateTime(2025, 3, 29, 10, 44, 18, 163, DateTimeKind.Utc).AddTicks(641));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("81ba4893-d891-4e3c-a5e7-ba13c697bf97"),
                column: "CreatedAt",
                value: new DateTime(2025, 3, 29, 10, 44, 18, 163, DateTimeKind.Utc).AddTicks(648));

            migrationBuilder.AddForeignKey(
                name: "FK_Transaction_Company",
                table: "Transaction",
                column: "CompanyId",
                principalTable: "Company",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
