﻿using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Entity;
using Dolfin.Mobile.API.Models.Request;
using Dolfin.Utility.Models;

namespace Dolfin.Mobile.API.Services
{
    public interface IInventoryService
    {
        #region Inventory
        Task<BaseResponse<PagedList<Inventory>>> GetInventoryList(Pagination pagination = null, CommonFilterList filterList = null, Guid? branchId = null);

        Task<BaseResponse<List<Inventory>>> GetInventoryListByCompanyBranch(Guid companyId, Guid? branchId); 
        Task<BaseResponse<Inventory>> GetInventoryByGuid(Guid inventoryId);
        Task<BaseResponse<ResultId>> InsertInventory(InventoryRequest reqBody, DolfinDbContext dbContextRollback = null);
        Task<BaseResponse<ResultId>> UpdateInventory(UpdateInventoryRequest reqBody, DolfinDbContext dbContextRollback = null);
        Task<NoResultResponse> DeleteInventory(Guid id);
        #endregion

        Task<BaseResponse<ResultId>> InsertInventoryProduct(InventoryProductRequest reqBody, DolfinDbContext dbContextRollback = null);

    }
}
