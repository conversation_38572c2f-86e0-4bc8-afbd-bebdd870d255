using System.ComponentModel.DataAnnotations;

namespace Dolfin.Mobile.API.Models.Request
{
    /// <summary>
    /// Request model for creating a new prefix
    /// </summary>
    public class PrefixRequest : PrefixBaseRequest
    {
        /// <summary>
        /// The name of the table for which the prefix is being created
        /// </summary>
        [Required]
        public required string TableName { get; set; }
        
        /// <summary>
        /// The branch ID to associate with this prefix
        /// </summary>
        public required Guid? BranchId { get; set; }
    }
}
