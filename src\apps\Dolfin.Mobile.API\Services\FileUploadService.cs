﻿﻿using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Entity;
using Dolfin.Framework.Data.Model;
using Dolfin.Framework.Data.Utils;
using Dolfin.Mobile.API.Models.Request;
using Dolfin.Mobile.API.Models.Response;
using Dolfin.Utility.Enum;
using Dolfin.Utility.Models;
using Dolfin.Utility.Utils;
using Microsoft.EntityFrameworkCore;
using Org.BouncyCastle.Asn1.Ocsp;
using System.Linq.Expressions;
using static Dolfin.Mobile.API.Constants.Constants;

namespace Dolfin.Mobile.API.Services
{
    public class FileUploadService : BaseComponent<FileUpload>, IFileUploadService
    {
        private readonly AmazonS3Service _amazonS3Service;
        private readonly StandardMessage _standardMessage;
        private readonly ILogger<FileUploadService> _logger;
        private readonly IUserService _userService;

        public FileUploadService(
            DbContextOptions<DolfinDbContext> dbContextOptions,
            AmazonS3Service amazonS3Service,
            IUserService userService,
            ILogger<FileUploadService> logger) : base(dbContextOptions)
        {
            _amazonS3Service = amazonS3Service;
            _userService = userService;
            _standardMessage = new StandardMessage();
            _logger = logger;
        }

        public async Task<BaseResponse<FileUploadResponse>> UploadFile(FileUploadRequest request, Guid companyId)
        {
            var result = new BaseResponse<FileUploadResponse> { IsSuccessful = true };
            var dbContext = GetDbContext();
            var currentTransaction = dbContext.Database.CurrentTransaction;
            var transaction = currentTransaction == null ? await dbContext.Database.BeginTransactionAsync() : currentTransaction;

            try
            {
                // Validate that module is provided
                if (string.IsNullOrEmpty(request.Module))
                {
                    throw new ArgumentException("Module is required and cannot be empty");
                }

                // Get current user
                var currentUser = await _userService.GetCurrentUserAsync();
                if (currentUser == null)
                {
                    throw new Exception("User not authenticated");
                }

                // Determine the folder path based on the module
                string directoryTemplate = GetDirectoryTemplate(GetConstantValueByName(request.Module));
                string fileType = Enums.FILE_TYPE.Files.GetDescription();

                // If the file is an image, use the Images file type
                if (request.File.ContentType.StartsWith("image/"))
                {
                    fileType = Enums.FILE_TYPE.Images.GetDescription();
                }

                string folderPath = AmazonS3Service.ConstructDirectory(
                    directoryTemplate,
                    companyId.ToString(),
                    fileType,
                    GetConstantValueByName(request.Module),
                    request.UniqueKey.ToString());

                // Prepare the upload request
                var uploadRequest = new UploadFileRequest
                {
                    File = request.File,
                    FileName = string.IsNullOrWhiteSpace(request.FileName) ? request.File.FileName : request.FileName,
                    FolderPath = folderPath,
                    DeleteFileUrl = request.DeleteFileUrl
                };

                // Upload the file to S3
                var uploadResult = await _amazonS3Service.UploadObject(uploadRequest);

                if (!uploadResult.Success)
                {
                    // Use the detailed error message if available
                    string errorMessage = !string.IsNullOrEmpty(uploadResult.ErrorDetails)
                        ? uploadResult.ErrorDetails
                        : $"Failed to upload file: {uploadResult.FileName}";

                    throw new Exception(errorMessage);
                }

                // Get a pre-signed URL for the uploaded file
                string preSignedUrl = _amazonS3Service.GetPreSignedUrl(uploadResult.FullPath);

                // Create file upload record in database
                var newFileUpload = new FileUpload
                {
                    CompanyId = companyId,
                    FileUrl = uploadResult.FullPath,
                    FileName = request.File.FileName,
                    MimeType = uploadResult.MimeType,
                    Size = (long)uploadResult.Size,
                    Module = GetConstantValueByName(request.Module),
                    ReferenceId = request.UniqueKey,
                    Description = request.FileName, // Use custom filename as description if provided
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Parse(currentUser.Id)
                };

                // Save to database using the dbContext directly
                dbContext.FileUpload.Add(newFileUpload);
                await dbContext.SaveChangesAsync();

                // Create the response
                result.Result = new FileUploadResponse
                {
                    Id = newFileUpload.Id,
                    FileName = request.File.FileName,
                    FileUrl = uploadResult.FullPath,
                    MimeType = uploadResult.MimeType,
                    Size = (long)uploadResult.Size,
                    Bucket = uploadResult.Bucket,
                    PreSignedUrl = preSignedUrl
                };

                // Commit transaction if we started it
                if (currentTransaction == null)
                    await transaction.CommitAsync();
            }
            catch (Exception ex)
            {
                // Rollback transaction if we started it
                if (currentTransaction == null)
                {
                    await transaction.RollbackAsync();
                }

                _logger.LogError(ex, "Error uploading file");
                result = _standardMessage.ErrorMessage<FileUploadResponse, FileUploadResponse>(
                    result,
                    Enums.StatusCode.InternalServerError,
                    exception: ex);
            }
            finally
            {
                // Dispose transaction if we started it
                if (currentTransaction == null)
                {
                    await transaction.DisposeAsync();
                }
            }

            return result;
        }

        public async Task<BaseResponse<string>> GetPreSignedUrl(string fileUrl)
        {
            var result = new BaseResponse<string> { IsSuccessful = true };

            try
            {
                if (string.IsNullOrEmpty(fileUrl))
                {
                    throw new ArgumentException("File URL cannot be null or empty");
                }

                string preSignedUrl = _amazonS3Service.GetPreSignedUrl(fileUrl);
                result.Result = preSignedUrl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating pre-signed URL");
                result = _standardMessage.ErrorMessage<string, string>(
                    result,
                    Enums.StatusCode.InternalServerError,
                    exception: ex);
            }

            return result;
        }

        public async Task<NoResultResponse> DeleteFile(string fileUrl)
        {
            var result = new NoResultResponse { IsSuccessful = true };
            var dbContext = GetDbContext();
            var currentTransaction = dbContext.Database.CurrentTransaction;
            var transaction = currentTransaction == null ? await dbContext.Database.BeginTransactionAsync() : currentTransaction;

            try
            {
                // Get current user
                var currentUser = await _userService.GetCurrentUserAsync();
                if (currentUser == null)
                {
                    throw new Exception("User not authenticated");
                }

                if (string.IsNullOrEmpty(fileUrl))
                {
                    throw new ArgumentException("File URL cannot be null or empty");
                }

                // Delete from S3
                var deleteRequest = new DeleteFileRequest
                {
                    FileUrl = fileUrl
                };

                var deleteResult = await _amazonS3Service.RemoveObject(deleteRequest);

                if (!deleteResult.Success)
                {
                    throw new Exception($"Failed to delete file from S3: {fileUrl}");
                }

                // Find and update the database record (soft delete)
                // Get current user's company ID for filtering
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
                var companyId = getCurrentUser.Item2;

                if (companyId == null)
                {
                    throw new Exception("Company ID is required");
                }

                // Use the existing dbContext to query for the file
                var fileRecord = await dbContext.FileUpload
                    .FirstOrDefaultAsync(f =>
                        f.FileUrl == fileUrl &&
                        f.IsActive &&
                        f.CompanyId == companyId.Value);

                if (fileRecord != null)
                {
                    // Soft delete
                    fileRecord.IsActive = false;
                    fileRecord.UpdatedAt = DateTime.UtcNow;
                    fileRecord.UpdatedBy = Guid.Parse(currentUser.Id);

                    // Update directly using the dbContext
                    dbContext.Entry(fileRecord).State = EntityState.Modified;
                    await dbContext.SaveChangesAsync();
                }

                // Commit transaction if we started it
                if (currentTransaction == null)
                {
                    await transaction.CommitAsync();
                }
            }
            catch (Exception ex)
            {
                // Rollback transaction if we started it
                if (currentTransaction == null)
                {
                    await transaction.RollbackAsync();
                }

                _logger.LogError(ex, "Error deleting file");
                result = _standardMessage.ErrorMessage<NoResult, NoResult>(
                    result,
                    Enums.StatusCode.InternalServerError,
                    exception: ex);
            }
            finally
            {
                // Dispose transaction if we started it
                if (currentTransaction == null)
                {
                    await transaction.DisposeAsync();
                }
            }

            return result;
        }

        public async Task<BaseResponse<List<FileUploadResponse>>> GetFilesByReferenceId(Guid referenceId, string module)
        {
            var result = new BaseResponse<List<FileUploadResponse>> { IsSuccessful = true };

            try
            {
                // Validate that module is provided
                if (string.IsNullOrEmpty(module))
                {
                    throw new ArgumentException("Module is required and cannot be empty");
                }

                // Get current user to filter by company ID
                var currentUser = await _userService.GetCurrentUserAsync();
                if (currentUser == null)
                {
                    throw new Exception("User not authenticated");
                }

                // Get the user's company ID
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
                var companyId = getCurrentUser.Item2;

                if (companyId == null)
                {
                    throw new Exception("Company ID is required");
                }

                // Get a database context
                using var dbContext = GetDbContext();

                // Convert module name to constant value
                string moduleValue = GetConstantValueByName(module);

                // Create the query for files with company ID and module filter
                var query = dbContext.FileUpload.AsNoTracking()
                    .Where(f =>
                        f.ReferenceId == referenceId &&
                        f.IsActive &&
                        f.CompanyId == companyId.Value &&
                        f.Module == moduleValue);

                // Execute the query
                var files = await query.ToListAsync();

                // Map to response objects
                var fileResponses = new List<FileUploadResponse>();
                foreach (var file in files)
                {
                    // Get pre-signed URL for each file
                    string preSignedUrl = _amazonS3Service.GetPreSignedUrl(file.FileUrl);

                    fileResponses.Add(new FileUploadResponse
                    {
                        Id = file.Id,
                        FileName = file.FileName,
                        FileUrl = file.FileUrl,
                        MimeType = file.MimeType,
                        Size = file.Size ?? 0,
                        Bucket = "", // Not stored in DB
                        PreSignedUrl = preSignedUrl
                    });
                }

                result.Result = fileResponses;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting files by reference ID");
                result = _standardMessage.ErrorMessage<List<FileUploadResponse>, List<FileUploadResponse>>(
                    result,
                    Enums.StatusCode.InternalServerError,
                    exception: ex);
            }

            return result;
        }

        private string GetDirectoryTemplate(string module)
        {
            return module.ToLower() switch
            {
                UPLOAD_MODULE.PRODUCT => UPLOAD_DIRECTORY.PRODUCT,
                UPLOAD_MODULE.PROFILE => UPLOAD_DIRECTORY.PROFILE
            };
        }
    }
}
