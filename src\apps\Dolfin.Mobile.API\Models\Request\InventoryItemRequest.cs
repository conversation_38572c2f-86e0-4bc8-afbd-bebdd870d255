﻿using static Dolfin.Utility.Enum.Enums;

namespace Dolfin.Mobile.API.Models.Request
{
    public partial class InventoryItemRequest
    {
        public DateTime? ExpireAt { get; set; }
        public DateTime? ManufacturingAt { get; set; }
        public int? ManufacturingYear { get; set; }
        public decimal Cost { get; set; }
        public decimal StockQuantity { get; set; }
        public Guid ProductUOMId { get; set; }
        public Guid ProductId { get; set; }
        public Guid InventoryProductId { get; set; }
    }
}
