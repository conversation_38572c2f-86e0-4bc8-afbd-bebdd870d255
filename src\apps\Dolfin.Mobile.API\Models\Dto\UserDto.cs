﻿using Dolfin.Framework.Data.Domains;

namespace Dolfin.Mobile.API.Models.Dto
{
    public class UserDto
    {
        public UserDto()
        {
            //Transaction = new HashSet<Transaction>();
            //UserRole = new HashSet<UserRole>();
        }

        public Guid Id { get; set; }
        public required string Username { get; set; }
        public required string FullName { get; set; }
        public required string Email { get; set; }
        public required string PhoneNo1 { get; set; }
        public string? PhoneNo2 { get; set; }
        public string? FaxNo1 { get; set; }
        public string? FaxNo2 { get; set; }
        public string? SerialNo { get; set; }
        public bool EmailConfirmed { get; set; }
        public DateTime LastLoginAt { get; set; }
        public int AccessFailedCount { get; set; }
        //public bool Locked { get; set; }
        //public DateTime? LockedAt { get; set; }
        //public DateTime? LockedEnd { get; set; }
        //public Guid UserTypeId { get; set; }
        public Guid? BranchId { get; set; }
        public Guid? CompanyId { get; set; }
        public BranchDto? Branch { get; set; }
        public CompanyDto? Company { get; set; }
        //public virtual ICollection<Transaction> Transaction { get; }
        //public virtual ICollection<UserRole> UserRole { get; }
    }
}
