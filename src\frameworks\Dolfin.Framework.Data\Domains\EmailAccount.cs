﻿using Dolfin.Framework.Data.Domains;
using System;
using System.Collections.Generic;

namespace Dolfin.Mobile.API.Models
{
    public partial class EmailAccount: _BaseDomain
    {
        public EmailAccount() {
            Emails = new HashSet<Email>();
            EmailTemplate = new HashSet<EmailTemplate>();
        }
        public string Email { get; set; }
        public string DisplayName { get; set; }
        public string Host { get; set; }
        public int Port { get; set; }
        public string Username { get; set; }
        public string Password { get; set; }
        public bool EnableSsl { get; set; }
        public bool UseDefaultCredentials { get; set; }
        public bool IsDefault { get; set; }
        public string Protocol { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public Guid CreatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public Guid? UpdatedBy { get; set; }
        public virtual ICollection<Email> Emails { get; }
        public virtual ICollection<EmailTemplate> EmailTemplate { get; }
    }
}
