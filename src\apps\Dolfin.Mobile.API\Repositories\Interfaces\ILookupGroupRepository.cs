using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Repository.Interfaces;
using Dolfin.Utility.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Dolfin.Mobile.API.Repositories.Interfaces
{
    /// <summary>
    /// Repository interface for lookup group operations
    /// </summary>
    public interface ILookupGroupRepository : IRepository<LookupGroup>
    {
        /// <summary>
        /// Get a lookup group by its code
        /// </summary>
        /// <param name="code">The lookup group code</param>
        /// <param name="companyId">Optional company ID to filter by</param>
        /// <returns>The lookup group if found, null otherwise</returns>
        Task<LookupGroup> GetByCodeAsync(string code, Guid? companyId = null);

        /// <summary>
        /// Get all lookup groups
        /// </summary>
        /// <param name="pagination">Pagination parameters</param>
        /// <param name="filterList">Filter parameters</param>
        /// <param name="companyId">Optional company ID to filter by</param>
        /// <returns>A paged list of lookup groups</returns>
        Task<PagedList<LookupGroup>> GetAllAsync(Pagination pagination, CommonFilterList filterList, Guid? companyId = null);

        /// <summary>
        /// Get a lookup group with its items
        /// </summary>
        /// <param name="lookupGroupId">The lookup group ID</param>
        /// <returns>The lookup group with its items if found, null otherwise</returns>
        Task<LookupGroup> GetWithItemsAsync(Guid lookupGroupId);

        /// <summary>
        /// Check if a lookup group code already exists
        /// </summary>
        /// <param name="code">The lookup group code</param>
        /// <param name="companyId">Optional company ID to filter by</param>
        /// <param name="excludeId">Optional ID to exclude from the check (for updates)</param>
        /// <returns>True if the code exists, false otherwise</returns>
        Task<bool> CodeExistsAsync(string code, Guid? companyId = null, Guid? excludeId = null);
    }
}
