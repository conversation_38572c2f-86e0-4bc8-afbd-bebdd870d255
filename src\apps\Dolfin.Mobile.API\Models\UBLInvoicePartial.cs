using System.Xml.Serialization;
using System.Collections.Generic;

namespace Dolfin.Utility.Models.UBLInvoice
{
    // Partial class to extend the Invoice class with missing properties
    public partial class Invoice
    {
        [XmlElement("cac:TaxTotal")]
        public Dolfin.Mobile.API.Models.TaxTotal TaxTotal { get; set; }

        [XmlElement("cac:LegalMonetaryTotal")]
        public Dolfin.Mobile.API.Models.LegalMonetaryTotal LegalMonetaryTotal { get; set; }

        [XmlElement("cac:InvoiceLine")]
        public List<Dolfin.Mobile.API.Models.InvoiceLine> InvoiceLines { get; set; }
    }
}
