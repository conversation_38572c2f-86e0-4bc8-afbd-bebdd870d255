﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Dolfin.Utility.Enum.Enums;

namespace Dolfin.Framework.Data.Domains
{
	public partial class EInvoiceErrorLog : _BaseDomain
	{
		public EInvoiceErrorLog()
		{
		}

		public Guid EInvoiceId { get; set; }
		public string? SubmissionUID { get; set; }
		public string ErrorMessage { get; set; } = string.Empty;
		public string? ErrorCode { get; set; }

		public virtual EInvoice? EInvoice { get; set; }
	}
}
