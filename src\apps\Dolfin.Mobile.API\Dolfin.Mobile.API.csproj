﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);1591</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="CronExpressionDescriptor" Version="2.40.0" />
    <PackageReference Include="Hangfire.AspNetCore" Version="1.8.18" />
    <PackageReference Include="Hangfire.Core" Version="1.8.18" />
    <PackageReference Include="Hangfire.PostgreSql" Version="1.20.12" />
    <PackageReference Include="MailKit" Version="*******" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="8.0.0" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.9.0" />
    <PackageReference Include="MimeKit" Version="4.7.1" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
	<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.6">
		<PrivateAssets>all</PrivateAssets>
		<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	</PackageReference>
	<PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.9.0" />
	<PackageReference Include="System.Security.Cryptography.Xml" Version="9.0.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\Dolfin.Util\Dolfin.Utility.csproj" />
    <ProjectReference Include="..\..\frameworks\Dolfin.Framework.Data\Dolfin.Framework.Data.csproj" />
    <ProjectReference Include="..\..\frameworks\Dolfin.Framework.Repository\Dolfin.Framework.Repository.csproj" />
  </ItemGroup>

  <!-- Exclude appsettings files from publish output -->
  <ItemGroup>
    <Content Update="appsettings.json" CopyToPublishDirectory="Never" />
    <Content Update="appsettings.Development.json" CopyToPublishDirectory="Never" />
  </ItemGroup>

</Project>
