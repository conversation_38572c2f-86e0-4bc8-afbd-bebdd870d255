﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Framework.Data.Domains
{
    public partial class ProductCategory : _BaseDomain
    {
        public ProductCategory()
        {
            Product = new HashSet<Product>();
        }
        public required string Code { get; set; }
        public required string Name { get; set; }
        public string? Description { get; set; }
        public Guid CompanyId { get; set; }
        public int? DisplayOrder { get; set; }
        public bool Published { get; set; }
        public virtual Company Company { get; set; }
        public virtual ICollection<Product> Product { get; }
    }
}
