using System.Xml.Serialization;
using System.Collections.Generic;
using Dolfin.Utility.Models.UBLInvoice;

namespace Dolfin.Mobile.API.Models
{
    // Extension classes for UBLInvoice model to add missing classes

    public class InvoiceLine
    {
        [XmlElement("cbc:ID")]
        public string ID { get; set; }

        [XmlElement("cbc:InvoicedQuantity")]
        public string InvoicedQuantity { get; set; }

        [XmlElement("cbc:LineExtensionAmount")]
        public string LineExtensionAmount { get; set; }

        [XmlElement("cac:TaxTotal")]
        public TaxTotal TaxTotal { get; set; }

        [XmlElement("cac:Item")]
        public Item Item { get; set; }

        [XmlElement("cac:Price")]
        public Price Price { get; set; }
    }

    public class Item
    {
        [XmlElement("cbc:Name")]
        public string Name { get; set; }

        [XmlElement("cbc:Description")]
        public string Description { get; set; }

        [XmlElement("cac:SellersItemIdentification")]
        public SellersItemIdentification SellersItemIdentification { get; set; }
    }

    public class SellersItemIdentification
    {
        [XmlElement("cbc:ID")]
        public string ID { get; set; }
    }

    public class Price
    {
        [XmlElement("cbc:PriceAmount")]
        public string PriceAmount { get; set; }
    }

    public class TaxTotal
    {
        [XmlElement("cbc:TaxAmount")]
        public string TaxAmount { get; set; }

        [XmlElement("cac:TaxSubtotal")]
        public List<TaxSubtotal> TaxSubtotals { get; set; }
    }

    public class TaxSubtotal
    {
        [XmlElement("cbc:TaxableAmount")]
        public string TaxableAmount { get; set; }

        [XmlElement("cbc:TaxAmount")]
        public string TaxAmount { get; set; }

        [XmlElement("cac:TaxCategory")]
        public TaxCategory TaxCategory { get; set; }
    }

    public class TaxCategory
    {
        [XmlElement("cbc:ID")]
        public string ID { get; set; }

        [XmlElement("cbc:Percent")]
        public string Percent { get; set; }

        [XmlElement("cac:TaxScheme")]
        public TaxScheme TaxScheme { get; set; }
    }

    public class TaxScheme
    {
        [XmlElement("cbc:ID")]
        public string ID { get; set; }
    }

    public class LegalMonetaryTotal
    {
        [XmlElement("cbc:LineExtensionAmount")]
        public string LineExtensionAmount { get; set; }

        [XmlElement("cbc:TaxExclusiveAmount")]
        public string TaxExclusiveAmount { get; set; }

        [XmlElement("cbc:TaxInclusiveAmount")]
        public string TaxInclusiveAmount { get; set; }

        [XmlElement("cbc:PayableAmount")]
        public string PayableAmount { get; set; }
    }

    // Extension for Invoice class to add missing properties
    public static partial class InvoiceExtensions
    {
        public static void AddMissingProperties(this Invoice invoice)
        {
            // Add properties that are missing in the original Invoice class
            // This is a placeholder for any future extensions
        }
    }
}
