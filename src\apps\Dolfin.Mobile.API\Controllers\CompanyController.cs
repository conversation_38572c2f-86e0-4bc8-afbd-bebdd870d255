﻿using AutoMapper;
using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Domains.CustomIdentity;
using Dolfin.Mobile.API.Constants;
using Dolfin.Mobile.API.Infrastructure;
using Dolfin.Mobile.API.Models;
using Dolfin.Mobile.API.Models.Dto;
using Dolfin.Mobile.API.Models.Request;
using Dolfin.Mobile.API.Services;
using Dolfin.Utility.Enum;
using Dolfin.Utility.Models;
using Dolfin.Utility.Utils;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Reflection;
using static Dolfin.Mobile.API.Constants.Constants;
using ValidationException = Dolfin.Utility.Utils.ExceptionHandler.ValidationException;

namespace Dolfin.Mobile.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class CompanyController : ControllerCore
    {
        private StandardMessage _standardMessage;
        private readonly ILogger<CompanyController> _logger;
        private readonly ICompanyService _companyService;
        private readonly IUserService _userService;
        private readonly IFileUploadService _fileUploadService;
        private readonly IMapper _mapper;

        public CompanyController(
            ILogger<CompanyController> logger,
            ICompanyService companyService,
            IUserService userService,
            IFileUploadService fileUploadService,
            IMapper mapper)
        {
            _standardMessage = new StandardMessage();
            _logger = logger;
            _mapper = mapper;
            _companyService = companyService;
            _userService = userService;
            _fileUploadService = fileUploadService;
        }



        [HttpGet("Profile")]
        [RequirePermission(Permissions.Company.View)]
        public async Task<IActionResult> Get(Guid? companyId)
        {
            // Get the company profile from the service
            var response = await _companyService.GetCompanyProfile(companyId);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
                return ActionResultResponse<CompanyDto, Company>(_mapper, response);
            }

            // Map the company to DTO
            var companyDto = _mapper.Map<CompanyDto>(response.Result);

            try
            {
                // If the company has a logo, get the logo file data
                if (response.Result != null && !string.IsNullOrEmpty(response.Result.Logo))
                {
                    // Get logo file for this company using the FileUploadService
                    var logoResponse = await _fileUploadService.GetFilesByReferenceId(
                        response.Result.Id,
                        GetConstantName(UPLOAD_MODULE.PROFILE));

                    if (logoResponse.IsSuccessful && logoResponse.Result != null && logoResponse.Result.Count > 0)
                    {
                        // Map the first logo file to FileUploadDto and add to the company's LogoFile property
                        companyDto.LogoFile = _mapper.Map<FileUploadDto>(logoResponse.Result.FirstOrDefault());
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the error but don't fail the entire request
                _logger.LogError(ex, $"Error fetching logo for company {response.Result?.Id}");
            }

            // Create a new response with the enhanced DTO
            var enhancedResponse = new BaseResponse<CompanyDto>
            {
                IsSuccessful = response.IsSuccessful,
                Result = companyDto,
                Exception = response.Exception
            };

            return ActionResultResponse<CompanyDto, CompanyDto>(_mapper, enhancedResponse);
        }

        [HttpGet("UserId")]
        [RequirePermission(Permissions.Company.View)]
        public async Task<IActionResult> GetCompanyByUserId(Guid? userId)
        {
            // Get the company profile from the service
            var response = await _companyService.GetCompanyByUserId(userId);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
                return ActionResultResponse<CompanyDto, Company>(_mapper, response);
            }

            // Map the company to DTO
            var companyDto = _mapper.Map<CompanyDto>(response.Result);

            try
            {
                // If the company has a logo, get the logo file data
                if (response.Result != null && !string.IsNullOrEmpty(response.Result.Logo))
                {
                    // Get logo file for this company using the FileUploadService
                    var logoResponse = await _fileUploadService.GetFilesByReferenceId(
                        response.Result.Id,
                        GetConstantName(UPLOAD_MODULE.PROFILE));

                    if (logoResponse.IsSuccessful && logoResponse.Result != null && logoResponse.Result.Count > 0)
                    {
                        // Map the first logo file to FileUploadDto and add to the company's LogoFile property
                        companyDto.LogoFile = _mapper.Map<FileUploadDto>(logoResponse.Result.FirstOrDefault());
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the error but don't fail the entire request
                _logger.LogError(ex, $"Error fetching logo for company {response.Result?.Id}");
            }

            // Create a new response with the enhanced DTO
            var enhancedResponse = new BaseResponse<CompanyDto>
            {
                IsSuccessful = response.IsSuccessful,
                Result = companyDto,
                Exception = response.Exception
            };

            return ActionResultResponse<CompanyDto, CompanyDto>(_mapper, enhancedResponse);
        }

        [HttpPost("Create")]
        [RequirePermission(Permissions.Company.Create)]
        public async Task<IActionResult> CreateCompany([FromBody] CompanyRequest companyRequest)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };
            try
            {
                if ((companyRequest.DefaultSalesTaxNoId == null || companyRequest.DefaultSalesTaxNoId == Guid.Empty) && (companyRequest.DefaultServiceTaxNoId == null || companyRequest.DefaultServiceTaxNoId == Guid.Empty))
                {
                    throw new ValidationException("Default Sales Tax ID and Default Service Tax Id either one is required.");
                }
                else if (companyRequest.DefaultSalesTaxNoId != null && companyRequest.DefaultSalesTaxNoId != Guid.Empty && companyRequest.DefaultServiceTaxNoId != null && companyRequest.DefaultServiceTaxNoId != Guid.Empty)
                {
                    throw new ValidationException("Default Sales Tax ID and Default Service Tax Id only one is required.");
                }

                result = await _companyService.InsertCompany(companyRequest);
                if (!result.IsSuccessful)
                {
                    _logger.LogError(result.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
                    throw new Exception(result.Exception);
                }
            }
            catch (ValidationException ex)
            {
                result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.BadRequest, exception: ex);
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return ActionResultResponse<ResultId, ResultId>(_mapper, result);
        }

        [HttpPost("Branch/Create")]
        [RequirePermission(Permissions.Company.ManageBranches)]
        public async Task<IActionResult> CreateBranch([FromBody] BranchRequest branchRequest)
        {
            var response = await _companyService.InsertBranch(branchRequest);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<ResultId, ResultId>(_mapper, response);
        }

        [HttpPut("Branch/Update/{branchId}")]
        [RequirePermission(Permissions.Company.ManageBranches)]
        public async Task<IActionResult> UpdateBranch(Guid branchId, [FromBody] UpdateBranchRequest branchRequest)
        {
            branchRequest.Id = branchId;
            var response = await _companyService.UpdateBranch(branchRequest);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<ResultId, ResultId>(_mapper, response);
        }


        [HttpGet("TaxCategory")]
        [RequirePermission(Permissions.Company.View)]
        public async Task<IActionResult> GetTaxCategory()
        {
            var response = await _companyService.GetTaxCategoryList();
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            Pagination pagination = new Pagination();
            return ActionResultResponse<List<TaxCategoryDto>, PagedList<TaxCategories>>(_mapper, response, pagination, PagedList<TaxCategories>.PagedMetadata(response));
        }

        [HttpGet("TaxCategory/Get/{taxCategoryId}")]
        [RequirePermission(Permissions.Company.View)]
        public async Task<IActionResult> GetTaxCategory(Guid taxCategoryId)
        {
            var response = await _companyService.GetTaxCategoryByGuid(taxCategoryId);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<TaxCategoryDto, TaxCategories>(_mapper, response);
        }


        [HttpPost("TaxRate/Create")]
        [RequirePermission(Permissions.Company.Update)]
        public async Task<IActionResult> CreateTaxRate([FromBody] TaxRateRequest taxRateRequest)
        {
            var response = await _companyService.InsertTaxRate(taxRateRequest);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<ResultId, ResultId>(_mapper, response);
        }


        [HttpPost("TaxCategory/Create")]
        [RequirePermission(Permissions.Company.Update)]
        public async Task<IActionResult> CreateTaxCategory([FromBody] TaxCategoryRequest taxCategoryRequest)
        {
            var response = await _companyService.InsertTaxCategory(taxCategoryRequest);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<ResultId, ResultId>(_mapper, response);
        }

        #region Prefix Endpoints
        [HttpGet("Branch/Prefix")]
        [RequirePermission(Permissions.Company.View)]
        public async Task<IActionResult> GetPrefixList([FromQuery] Pagination pagination, [FromQuery] CommonFilterList filterList, [FromQuery] Guid? branchId)
        {
            var response = await _companyService.GetPrefixList(pagination, filterList, branchId);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<List<PrefixDto>, PagedList<Prefix>>(_mapper, response, pagination, PagedList<Prefix>.PagedMetadata(response));
        }

        [HttpGet("Branch/Prefix/{prefixId}")]
        [RequirePermission(Permissions.Company.View)]
        public async Task<IActionResult> GetPrefixById(Guid prefixId)
        {
            var response = await _companyService.GetPrefixByGuid(prefixId);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<PrefixDto, Prefix>(_mapper, response);
        }

        [HttpPost("Branch/Prefix/Create")]
        [RequirePermission(Permissions.Company.ManageBranches)]
        public async Task<IActionResult> CreatePrefix([FromBody] PrefixRequest prefixRequest)
        {
            var response = await _companyService.InsertPrefix(prefixRequest);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<ResultId, ResultId>(_mapper, response);
        }

        [HttpPut("Branch/Prefix/Update/{prefixId}")]
        [RequirePermission(Permissions.Company.ManageBranches)]
        public async Task<IActionResult> UpdatePrefix(Guid prefixId, [FromBody] UpdatePrefixRequest prefixRequest)
        {
            prefixRequest.Id = prefixId;
            var response = await _companyService.UpdatePrefix(prefixRequest);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<ResultId, ResultId>(_mapper, response);
        }

        [HttpDelete("Branch/Prefix/Delete/{prefixId}")]
        [RequirePermission(Permissions.Company.ManageBranches)]
        public async Task<IActionResult> DeletePrefix(Guid prefixId)
        {
            var userAccessValidatation = await _userService.UserAccessValidatation(true);
            if (!userAccessValidatation.IsSuccessful)
                return ActionResultResponse<NoResult, ResultId>(_mapper, userAccessValidatation);

            var response = await _companyService.DeletePrefix(prefixId);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<NoResult, NoResult>(_mapper, response);
        }

        [HttpGet("Branch/Prefix/GenerateCode")]
        [RequirePermission(Permissions.Company.View)]
        public async Task<IActionResult> GenerateCode([FromQuery] string tableName, [FromQuery] Guid branchId)
        {
            try
            {
                var code = await _companyService.GenerateCode(tableName, branchId);
                return Ok(new { Code = code });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error occurred in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
                return StatusCode(500, new { Error = ex.Message });
            }
        }
        #endregion
    }
}
