using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System;

namespace Dolfin.Mobile.API.Helper
{
    /// <summary>
    /// Utility class for token-related operations
    /// </summary>
    public static class TokenUtils
    {
        /// <summary>
        /// Extracts the access token from the Authorization header or from cookies
        /// </summary>
        /// <param name="httpContext">The HTTP context</param>
        /// <param name="logger">Optional logger for logging token extraction</param>
        /// <returns>The access token if found, null otherwise</returns>
        public static string GetAccessToken(HttpContext httpContext, ILogger logger = null)
        {
            string accessToken = null;

            // Try to get token from Authorization header first
            string authHeader = httpContext.Request.Headers["Authorization"];
            if (!string.IsNullOrEmpty(authHeader) && authHeader.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase))
            {
                accessToken = authHeader.Substring("Bearer ".Length).Trim();
                logger?.LogInformation("Access token extracted from Authorization header");
            }
            // Fallback to cookie if header is not present
            else if (httpContext.Request.Cookies.TryGetValue("X-Access-Token", out var tokenFromCookie))
            {
                accessToken = tokenFromCookie;
                logger?.LogInformation("Access token extracted from cookie");
            }
            else
            {
                logger?.LogWarning("Access token not found in Authorization header or cookies");
            }

            return accessToken;
        }

        /// <summary>
        /// Extracts the refresh token from the request body or from cookies
        /// </summary>
        /// <param name="httpContext">The HTTP context</param>
        /// <param name="refreshTokenFromBody">The refresh token from the request body, if available</param>
        /// <param name="logger">Optional logger for logging token extraction</param>
        /// <returns>The refresh token if found, null otherwise</returns>
        public static string GetRefreshToken(HttpContext httpContext, string refreshTokenFromBody, ILogger logger = null)
        {
            string refreshToken = null;

            // Try to use the token from the request body first
            if (!string.IsNullOrEmpty(refreshTokenFromBody))
            {
                refreshToken = refreshTokenFromBody;
                logger?.LogInformation("Refresh token extracted from request body");
            }
            // Fallback to cookie if body token is not present
            else if (httpContext.Request.Cookies.TryGetValue("X-Refresh-Token", out var tokenFromCookie))
            {
                refreshToken = tokenFromCookie;
                logger?.LogInformation("Refresh token extracted from cookie");
            }
            else
            {
                logger?.LogWarning("Refresh token not found in request body or cookies");
            }

            return refreshToken;
        }
    }
}
