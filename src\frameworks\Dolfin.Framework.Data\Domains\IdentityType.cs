﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Framework.Data.Domains
{
    public partial class IdentityType : _BaseDomain
    {
        public IdentityType()
        {
			Customer = new HashSet<Customer>();
        }
        public required string Code { get; set; }
        public required string Name { get; set; }
        public string Description { get; set; }
        public string? Format { get; set; }
        public virtual ICollection<Customer> Customer { get; }

    }
}
