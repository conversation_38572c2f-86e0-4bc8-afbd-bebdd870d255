﻿using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Entity;
using Dolfin.Mobile.API.Services;
using Dolfin.Utility.Utils;
using Microsoft.EntityFrameworkCore;

namespace Dolfin.Mobile.API.Scheduler
{
    public class JobConfigService: BaseComponent<JobConfig>
    {
        private readonly StandardMessage _standardMessage;

        public JobConfigService(DolfinDbContext dbContext, DbContextOptions<DolfinDbContext> dbContextOptions) : base(dbContext, dbContextOptions)
        {
            _standardMessage = new StandardMessage();
        }


        public async Task<JobConfig> CreateUpdateJobConfig(string code)
        {
            try
            {
                var jobConfig = await GetJobConfigByCode(code);
                if (jobConfig == null)
                {
                    var newJobConfig = new JobConfig();
                    newJobConfig.Code = code;
                    jobConfig = await CreateJobConfig(newJobConfig);
                }
                else
                {
                    jobConfig = await UpdateJobConfig(jobConfig);
                }

                return jobConfig;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public async Task<bool> CheckIsCreatedByEarlyOrThisMonth(string code)
        {
            var isCreated = false;
            try
            {
                var jobConfig = await GetJobConfigByCode(code);
                if (jobConfig != null)
                {
                    var monthOfDate = DateTime.UtcNow.Date;
                    monthOfDate = new DateTime(monthOfDate.Year, monthOfDate.Month, 1);

                    DateTime jobScheduleDate = new DateTime(jobConfig.LastProcessDate.Year, jobConfig.LastProcessDate.Month, 1);
                    int result = DateTime.Compare(monthOfDate, jobScheduleDate);

                    if (result <= 0)
                    {
                        isCreated = true;
                    }
                }
                return isCreated;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        private async Task<JobConfig> GetJobConfig(int id)
        {
            try
            {
                var q = GetDbContext().Set<JobConfig>().AsQueryable();
                q = q.Where(x => x.Id == id);
                return await q.FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        private async Task<JobConfig> GetJobConfigByCode(string code)
        {
            try
            {
                var q = GetDbContext().Set<JobConfig>().AsQueryable();
                q = q.Where(x => x.Code == code);
                return await q.FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        private async Task<JobConfig> CreateJobConfig(JobConfig newJobConfig)
        {
            try
            {
                newJobConfig.LastProcessDate = DateTime.UtcNow;
                await CreateAsync(newJobConfig);
                return newJobConfig;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        private async Task<JobConfig> UpdateJobConfig(JobConfig updateJobConfig)
        {
            try
            {
                var jobConfig = await GetJobConfig(updateJobConfig.Id);
                jobConfig.LastProcessDate = DateTime.UtcNow;

                await UpdateAsync(jobConfig);
                return jobConfig;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }
}
