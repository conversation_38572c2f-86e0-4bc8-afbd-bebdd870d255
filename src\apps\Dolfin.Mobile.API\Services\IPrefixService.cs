using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Entity;
using Dolfin.Mobile.API.Models.Request;
using Dolfin.Utility.Models;

namespace Dolfin.Mobile.API.Services
{
    public interface IPrefixService
    {
        Task<string> GenerateCode(string tableName, Guid branchId, DolfinDbContext dbContextRollback = null);
        Task<BaseResponse<PagedList<Prefix>>> GetPrefixList(Pagination pagination = null, CommonFilterList filterList = null, Guid? branchId = null);
        Task<BaseResponse<Prefix>> GetPrefixByGuid(Guid prefixId);
        Task<BaseResponse<ResultId>> InsertPrefix(PrefixRequest reqBody, DolfinDbContext dbContextRollback = null);
        Task<BaseResponse<ResultId>> UpdatePrefix(UpdatePrefixRequest reqBody);
        Task<NoResultResponse> DeletePrefix(Guid id);
    }
}
