using Dolfin.Framework.Repository.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Dolfin.Framework.Repository.Implementations
{
    /// <summary>
    /// Generic Unit of Work implementation for managing transactions and repositories
    /// </summary>
    /// <typeparam name="TContext">DbContext type</typeparam>
    public class UnitOfWork<TContext> : IUnitOfWork where TContext : DbContext
    {
        protected readonly TContext _context;
        protected readonly IServiceProvider _serviceProvider;
        protected IDbContextTransaction _transaction;
        protected readonly Dictionary<Type, object> _repositories;
        protected bool _disposed;

        /// <summary>
        /// Constructor
        /// </summary>
        public UnitOfWork(
            TContext context,
            IServiceProvider serviceProvider)
        {
            _context = context;
            _serviceProvider = serviceProvider;
            _repositories = new Dictionary<Type, object>();
        }

        /// <inheritdoc />
        public IRepository<T> Repository<T>() where T : class
        {
            var type = typeof(T);
            
            if (!_repositories.ContainsKey(type))
            {
                // Use the service provider to resolve the repository
                // This allows the DI container to handle the dependencies
                var repository = _serviceProvider.GetService(typeof(IRepository<T>));
                
                if (repository == null)
                {
                    throw new InvalidOperationException($"Repository for type {type.Name} is not registered in the service container.");
                }
                
                _repositories[type] = repository;
            }
            
            return (IRepository<T>)_repositories[type];
        }

        /// <inheritdoc />
        public async Task<int> SaveChangesAsync()
        {
            return await _context.SaveChangesAsync();
        }

        /// <inheritdoc />
        public async Task BeginTransactionAsync()
        {
            _transaction = await _context.Database.BeginTransactionAsync();
        }

        /// <inheritdoc />
        public async Task CommitTransactionAsync()
        {
            try
            {
                await _context.SaveChangesAsync();
                await _transaction.CommitAsync();
            }
            finally
            {
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        /// <inheritdoc />
        public async Task RollbackTransactionAsync()
        {
            try
            {
                await _transaction.RollbackAsync();
            }
            finally
            {
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        /// <inheritdoc />
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Dispose pattern implementation
        /// </summary>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // Dispose managed resources
                    _context.Dispose();
                    _transaction?.Dispose();
                }

                _disposed = true;
            }
        }
    }
}
