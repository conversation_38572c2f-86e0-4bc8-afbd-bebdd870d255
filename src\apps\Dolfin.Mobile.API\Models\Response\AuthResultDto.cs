﻿﻿using Dolfin.Mobile.API.Models.Dto;

namespace Dolfin.Mobile.API.Models.Response
{
    /// <summary>
    /// Authentication result data transfer object
    /// </summary>
    public class AuthResultDto
    {
        /// <summary>
        /// Token ID
        /// </summary>
        public string TokenId { get; set; }

        /// <summary>
        /// JWT access token
        /// </summary>
        public string Token { get; set; }

        /// <summary>
        /// Refresh token for obtaining a new access token
        /// </summary>
        public string RefreshToken { get; set; }

        /// <summary>
        /// Token expiration time in seconds
        /// </summary>
        public int ExpiresIn { get; set; }

        /// <summary>
        /// User information
        /// </summary>
        public UserDto User { get; set; }

        /// <summary>
        /// Display message for the client
        /// </summary>
        public string DisplayMessage { get; set; }
    }
}
