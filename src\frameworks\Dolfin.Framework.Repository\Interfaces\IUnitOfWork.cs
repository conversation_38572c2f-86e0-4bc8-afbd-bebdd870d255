using System;
using System.Threading.Tasks;

namespace Dolfin.Framework.Repository.Interfaces
{
    /// <summary>
    /// Unit of Work interface for managing transactions and repositories
    /// </summary>
    public interface IUnitOfWork : IDisposable
    {
        /// <summary>
        /// Get repository for entity type
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <returns>Repository for entity type</returns>
        IRepository<T> Repository<T>() where T : class;
        
        /// <summary>
        /// Save changes to database
        /// </summary>
        /// <returns>Number of affected rows</returns>
        Task<int> SaveChangesAsync();
        
        /// <summary>
        /// Begin a transaction
        /// </summary>
        Task BeginTransactionAsync();
        
        /// <summary>
        /// Commit the current transaction
        /// </summary>
        Task CommitTransactionAsync();
        
        /// <summary>
        /// Rollback the current transaction
        /// </summary>
        Task RollbackTransactionAsync();
    }
}
