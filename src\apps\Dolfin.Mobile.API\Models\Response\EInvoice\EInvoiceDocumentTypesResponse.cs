using System.Text.Json.Serialization;

namespace Dolfin.Mobile.API.Models
{
    public class EInvoiceDocumentTypesResponse
    {
        [JsonPropertyName("result")]
        public List<EInvoiceDocumentType> Result { get; set; } = new List<EInvoiceDocumentType>();
    }

    public class EInvoiceDocumentType
    {
        [JsonPropertyName("id")]
        public int Id { get; set; }

        [JsonPropertyName("invoiceTypeCode")]
        public string InvoiceTypeCode { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("description")]
        public string Description { get; set; }

        [JsonPropertyName("activeFrom")]
        public DateTime ActiveFrom { get; set; }

        [JsonPropertyName("activeTo")]
        public DateTime? ActiveTo { get; set; }

        [JsonPropertyName("documentTypeVersions")]
        public List<DocumentTypeVersion> DocumentTypeVersions { get; set; } = new List<DocumentTypeVersion>();
    }

    public class DocumentTypeVersion
    {
        [JsonPropertyName("id")]
        public int Id { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("description")]
        public string Description { get; set; }

        [JsonPropertyName("activeFrom")]
        public DateTime ActiveFrom { get; set; }

        [JsonPropertyName("activeTo")]
        public DateTime? ActiveTo { get; set; }

        [JsonPropertyName("versionNumber")]
        public string VersionNumber { get; set; }

        [JsonPropertyName("status")]
        public string Status { get; set; }
    }
}
