using AutoMapper;
using DocumentFormat.OpenXml.Spreadsheet;
using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Domains.CustomIdentity;
using Dolfin.Mobile.API.Constants;
using Dolfin.Mobile.API.Infrastructure;
using Dolfin.Mobile.API.Models;
using Dolfin.Mobile.API.Models.Dto;
using Dolfin.Mobile.API.Models.Request;
using Dolfin.Mobile.API.Services;
using Dolfin.Utility.Enum;
using Dolfin.Utility.Models;
using Dolfin.Utility.Utils;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
//using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.Extensions.Logging;
using Org.BouncyCastle.Asn1.Ocsp;
using System.Reflection;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace Dolfin.Mobile.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class UserController : ControllerCore
    {
        private StandardMessage _standardMessage;
        private readonly ILogger<UserController> _logger;
        private readonly IUserService _userService;
        private readonly IAuthService _authService;
        private IMapper _mapper;

        public UserController(ILogger<UserController> logger, IUserService userService, IAuthService authService, IMapper mapper)
        {
            _standardMessage = new StandardMessage();
            _logger = logger;
            _userService = userService;
            _authService = authService;
            _mapper = mapper;
        }

        [HttpGet("Profile")]
        public async Task<IActionResult> Get(string? userId)
        {
            //if (!_userResolver.IsAuthenticated && !customerId.HasValue)
            //{
            //    return IncompleteDataResultResponse();
            //}

            //string userGuid = null;
            //int? userResolveCustomerId = null;
            //if (_userResolver.IsAuthenticated)
            //{
            //    userResolveCustomerId = _userResolver.CustomerId;
            //}

            //if (!customerId.HasValue)
            //{
            //    userGuid = _userResolver.UserGuid;
            //}

            var userAccessValidatation = await _userService.UserAccessValidatation();
            if (!userAccessValidatation.IsSuccessful)
                return ActionResultResponse<NoResult, ResultId>(_mapper, userAccessValidatation);

            var response = await _userService.GetUserProfile(userId);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            //else
            //{
            //    if ((!_userResolver.IsAuthenticated || (customerId.HasValue && _userResolver.IsAuthenticated && _userResolver.CustomerId != customerId)) && response.Result != null)
            //    {
            //        response.Result.Password = null;
            //        response.Result.PasswordHash = null;
            //        response.Result.PasswordSalt = null;
            //        response.Result.Email = null;
            //        response.Result.NormalizedEmail = null;
            //        response.Result.PhoneNumber = null;
            //        response.Result.UserTypeId = null;
            //        response.Result.UserType = null;
            //        response.Result.AuthTypeId = null;
            //        response.Result.AuthType = null;
            //        response.Result.AuthUserRoles = null;
            //    }
            //}
            return ActionResultResponse<UserDto, ApplicationUser>(_mapper, response);
        }

        //[HttpGet]
        //public async Task<IActionResult> GetUserList([FromQuery] bool? isActive = null)
        //{
        //    var data = await _userService.GetUser(isActive);
        //    return Ok(data);
        //}

        //[HttpPost("Login")]
        //public async Task<IActionResult> Login([FromBody] LoginRequestLocal loginModel)
        //{
        //    //_identityUser.PhoneNumber
        //    var response = await _userService.ValidateCredentials(loginModel);
        //    if (!response.IsSuccessful)
        //    {
        //        _loginAccount = new LoginAccount();
        //        _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
        //    }
        //    else
        //    {
        //        _loginAccount.UserId = response.Result.Id;
        //        var userResponse = await _userService.GetUserByGuid((Guid)response.Result.Id);
        //        _loginAccount.Username = userResponse.Username;
        //        _loginAccount.UserTypeId = userResponse.UserRole.Select(x => x.RoleId).ToList().FirstOrDefault();
        //        _loginAccount.CompanyId = userResponse.CompanyId;
        //        _loginAccount.BranchId = userResponse.BranchId;
        //    }
        //    return ActionResultResponse<ResultId, ResultId>(_mapper, response);
        //}

        //[HttpGet("Logout")]
        //public async Task<IActionResult> Logout()
        //{
        //    var response = new BaseResponse<NoResult> { IsSuccessful = true };
        //    _loginAccount = new LoginAccount();

        //    return ActionResultResponse<NoResult, NoResult>(_mapper, response);
        //}

        //[HttpPost("ChangePassword")]
        //public async Task<IActionResult> ChangePassword([FromBody] ChangePasswordRequest changePassword)
        //{
        //    var userAccessValidatation = _userService.UserAccessValidatation();
        //    if (!userAccessValidatation.IsSuccessful)
        //        return ActionResultResponse<NoResult, NoResult>(_mapper, userAccessValidatation);

        //    var response = await _userService.ChangePassword((Guid)_loginAccount.UserId, changePassword.OldPassword, changePassword.NewPassword);
        //    if (!response.IsSuccessful)
        //    {
        //        _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
        //    }
        //    return ActionResultResponse<NoResult, NoResult>(_mapper, response);
        //}

        //[HttpPost("Create")]
        //public async Task<IActionResult> Create([FromBody] UserRequest reqBody)
        //{
        //    var userAccessValidatation = await _userService.UserAccessValidatation();
        //    if (!userAccessValidatation.IsSuccessful)
        //        return ActionResultResponse<ResultId, NoResult>(_mapper, userAccessValidatation);

        //    var newRegister = _mapper.Map<Register>(reqBody);
        //    var response = await _authService.RegisterAsync(newRegister, Guid.Parse((await _userService.GetCurrentUserAsync()).Id));
        //    if (!response.Succeeded)
        //    {
        //        _logger.LogError(response.Errors.First().Description, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
        //    }

        //    return Ok("Registration successful. Please check your email for confirmation.");
        //    //return ActionResultResponse<ResultId, ResultId>(_mapper, response.);
        //}

        [HttpPost("Delete")]
        [RequirePermission(Permissions.User.Delete)]
        public async Task<IActionResult> Delete(string userId)
        {
            var userAccessValidatation = await _userService.UserAccessValidatation(true);
            if (!userAccessValidatation.IsSuccessful)
                return ActionResultResponse<NoResult, ResultId>(_mapper, userAccessValidatation);

            var response = await _userService.DeleteUser(userId);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<NoResult, NoResult>(_mapper, response);
        }

        //[HttpPost("Current")]
        //public async Task<IActionResult> GetCurrentUser()
        //{
        //    var user = await _userService.GetCurrentUserAsync(User);
        //    if (user == null)
        //    {
        //        return NotFound();
        //    }
        //    return Ok(new
        //    {
        //        user.Id,
        //        user.Email,
        //        user.UserName
        //    });
        //}
    }
}
