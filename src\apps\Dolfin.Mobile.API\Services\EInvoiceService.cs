﻿using Dolfin.Framework.Data.Domains;
using Dolfin.Mobile.API.Helper;
using Dolfin.Mobile.API.Services;
using Dolfin.Utility.Enum;
using Dolfin.Utility.Models;
using Dolfin.Utility.Utils;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System.Net.Http.Headers;
using System.Net;
using Dolfin.Mobile.API.Models;
using Dolfin.Framework.Data.Entity;
using Microsoft.EntityFrameworkCore;
using static Dolfin.Mobile.API.Constants.Constants;
using static Dolfin.Utility.Enum.Enums;
using AutoMapper;
using System.Text;
using System.Text.Json;
using JsonSerializer = System.Text.Json.JsonSerializer;
using Microsoft.Extensions.Logging;
using System.Security.Cryptography;

namespace Dolfin.Mobile.API.Services
{
    public class EInvoiceService : BaseComponent<EInvoice>, IEinvoiceService
    {
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IUserService _userService;
        private readonly EInvoiceSettings _settings;
        private readonly StandardMessage _standardMessage;
        private readonly ILogger<EInvoiceService> _logger;
        private IMapper _mapper;

        public EInvoiceService(DbContextOptions<DolfinDbContext> dbContextOptions, IHttpClientFactory httpClientFactory, IUserService userService, IOptions<EInvoiceSettings> settings, StandardMessage standardMessage, IMapper mapper, ILogger<EInvoiceService> logger) : base(dbContextOptions)
        {
            _mapper = mapper;
            _httpClientFactory = httpClientFactory;
            _userService = userService;
            _settings = settings.Value;
            _standardMessage = standardMessage;
            _logger = logger;
        }

        #region Authentication
        public async Task<BaseResponse<EInvoiceAuthResponse>> LoginTaxpayerSystem(EInvoiceAuthRequest? loginAuthSystem = null)
        {
            var result = new BaseResponse<EInvoiceAuthResponse> { IsSuccessful = true };

            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
                var currentUser = getCurrentUser.Item1;
                var companyId = getCurrentUser.Item2;

                var query = GetDbContext().Set<Company>().AsQueryable()
                    .Where(x => (companyId == null || x.Id == companyId) && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))));

                var response = await query.FirstOrDefaultAsync();
                if (response?.EInvoiceClientId == null || response?.EInvoiceClientSecret == null)
                {
                    throw new Exception("EInvoiceTokenDateTime and EInvoiceClientSecret should not be empty");
                }
                if (response?.EInvoiceToken == null || response?.EInvoiceTokenDateTime == null || response.EInvoiceTokenDateTime <= DateTime.UtcNow)
                {
                    // call lhdn get login bearer
                    HttpRequestMessage request;
                    if (loginAuthSystem.IsIntermediary)
                    {
                        // Login as Intermediary System
                        request = new HttpRequestMessage(HttpMethod.Post, $"{_settings.BaseUrl}{EINVOICE.LOGINTAXSYSTEM}")
                        {
                            Content = new FormUrlEncodedContent(new[]
                            {
                                new KeyValuePair<string, string>("grant_type", "client_credentials"),
                                new KeyValuePair<string, string>("client_id", response.EInvoiceClientId),
                                new KeyValuePair<string, string>("client_secret", response.EInvoiceClientSecret),
                                new KeyValuePair<string, string>("scope", "InvoicingAPI"),
                            })
                        };
                        request.Headers.Add("onBehalfOf", response.TinNo);
                        request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                    }
                    else
                    {
                        // Login as Taxpayer System
                        request = new HttpRequestMessage(HttpMethod.Post, $"{_settings.BaseUrl}{EINVOICE.LOGINTAXSYSTEM}")
                        {
                            Content = new FormUrlEncodedContent(new[]
                            {
                                new KeyValuePair<string, string>("grant_type", "client_credentials"),
                                new KeyValuePair<string, string>("client_id", response.EInvoiceClientId),
                                new KeyValuePair<string, string>("client_secret", response.EInvoiceClientSecret),
                                new KeyValuePair<string, string>("scope", "InvoicingAPI"),
                            })
                        };
                    }

                    var client = _httpClientFactory.CreateClient();
                    var httpResponse = await client.SendAsync(request);
                    var httpResponseMessage = httpResponse.EnsureSuccessStatusCode();
                    if (httpResponseMessage.IsSuccessStatusCode)
                    {
                        var responseContent = await httpResponseMessage.Content.ReadAsStringAsync();
                        result.Result = JsonSerializer.Deserialize<EInvoiceAuthResponse>(responseContent, new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true
                        });
                        await UpdateCompanyAsync(response, result.Result.access_token, Guid.Parse(currentUser.Id));
                    }
                    else
                    {
                        var responseContent = await httpResponseMessage.Content.ReadAsStringAsync();
                        var eInvoiceErrorLogRequest = new EInvoiceErrorLogRequest
                        {
                            ErrorCode = ((int)httpResponseMessage.StatusCode).ToString(),
                            ErrorMessage = responseContent
                        };
                        await InsertEInvoiceErrorLog(eInvoiceErrorLogRequest, Guid.Parse(currentUser.Id));

                        throw new HttpRequestException(
                            $"API request failed with status {httpResponse.StatusCode}: {responseContent}",
                            null, // inner exception
                            httpResponse.StatusCode // preserves HTTP status
                        );
                    }
                }
                else
                {
                    // retrieve existing token
                    result.Result ??= new EInvoiceAuthResponse();
                    result.Result.access_token = response.EInvoiceToken.ToString();
                }

                return result; // await ProcessApiResponse(httpResponseMessage, result);
            }
            catch (HttpRequestException httpEx)
            {
                // Handle HTTP request exceptions specifically
                return _standardMessage.ErrorMessage<EInvoiceAuthResponse, EInvoiceAuthResponse>(
                    result,
                    GetStatusCode(httpEx), // Use appropriate status code for external service failures
                    exception: new Exception(httpEx.Message)
                );
            }
            catch (Exception ex)
            {
                // Handle general exceptions
                return _standardMessage.ErrorMessage<EInvoiceAuthResponse, EInvoiceAuthResponse>(
                    result,
                    StatusCode.InternalServerError,
                    exception: ex
                );
            }
        }
        #endregion

        public async Task<BaseResponse<EInvoiceSearchTinResponse>> SearchTin(EInvoiceSearchTinRequest reqBody)
        {
            var result = new BaseResponse<EInvoiceSearchTinResponse> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
                var currentUser = getCurrentUser.Item1;
                var companyId = getCurrentUser.Item2;

                var query = GetDbContext().Set<Company>().AsQueryable()
                    .Where(x => (companyId == null || x.Id == companyId) && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))));

                var response = await query.FirstOrDefaultAsync();
                if (response?.EInvoiceClientId == null || response?.EInvoiceClientSecret == null)
                {
                    throw new Exception("EInvoiceTokenDateTime and EInvoiceClientSecret should not be empty");
                }

                var client = _httpClientFactory.CreateClient();
                if (response?.EInvoiceTokenDateTime == null || response.EInvoiceTokenDateTime <= DateTime.UtcNow)
                {
                    var authRequest = new EInvoiceAuthRequest
                    {
                        ClientId = response.EInvoiceClientId,
                        ClientSecret = response.EInvoiceClientSecret
                    };
                    var loginTaxpayerSystemResponse = await LoginTaxpayerSystem(authRequest);
                    if (!loginTaxpayerSystemResponse.IsSuccessful)
                    {
                        throw new HttpRequestException(
                            loginTaxpayerSystemResponse.Exception,
                            null,
                            (HttpStatusCode)(loginTaxpayerSystemResponse.StatusCode ?? 500)
                        );
                    }
                    else
                    {
                        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", loginTaxpayerSystemResponse.Result.access_token);
                    }
                }
                else
                {
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", response.EInvoiceToken);
                }

                //var uriBuilder = new UriBuilder($"{_settings.BaseUrl}/api/v1.0/taxpayer/validate/{tinCode}");
                //var query = HttpUtility.ParseQueryString(uriBuilder.Query);
                //query["idType"] = idType;
                //query["idValue"] = idValue;
                //uriBuilder.Query = query.ToString();

                var httpResponse = await client.GetAsync($"{_settings.BaseUrl}{EINVOICE.SEARCHTIN.Replace("{idType}", reqBody.IdType).Replace("{idValue}", reqBody.IdValue)}");

                var httpResponseMessage = httpResponse.EnsureSuccessStatusCode();
                if (httpResponseMessage.IsSuccessStatusCode)
                {
                    var responseContent = await httpResponseMessage.Content.ReadAsStringAsync();
                    result.Result = JsonSerializer.Deserialize<EInvoiceSearchTinResponse>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                }
                else
                {
                    var responseContent = await httpResponseMessage.Content.ReadAsStringAsync();
                    var eInvoiceErrorLogRequest = new EInvoiceErrorLogRequest
                    {
                        ErrorCode = ((int)httpResponseMessage.StatusCode).ToString(),
                        ErrorMessage = responseContent
                    };
                    await InsertEInvoiceErrorLog(eInvoiceErrorLogRequest, Guid.Parse(currentUser.Id));

                    throw new HttpRequestException(
                        $"API request failed with status {httpResponse.StatusCode}: {responseContent}",
                        null, // inner exception
                        httpResponse.StatusCode // preserves HTTP status
                    );
                }

                return result; // await ProcessApiResponse(httpResponseMessage, result);
            }
            catch (HttpRequestException httpEx)
            {
                // Handle HTTP request exceptions specifically
                return _standardMessage.ErrorMessage<EInvoiceSearchTinResponse, EInvoiceSearchTinResponse>(
                    result,
                    GetStatusCode(httpEx), // Use appropriate status code for external service failures
                    exception: new Exception(httpEx.Message)
                );
            }
            catch (Exception ex)
            {
                // Handle general exceptions
                return _standardMessage.ErrorMessage<EInvoiceSearchTinResponse, EInvoiceSearchTinResponse>(
                    result,
                    StatusCode.InternalServerError,
                    exception: ex
                );
            }
        }

        public async Task<BaseResponse<EInvoiceValidateTinResponse>> ValidateTin(EInvoiceValidateTinRequest reqBody)
        {
            var result = new BaseResponse<EInvoiceValidateTinResponse> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
                var currentUser = getCurrentUser.Item1;
                var companyId = getCurrentUser.Item2;

                var query = GetDbContext().Set<Company>().AsQueryable()
                    .Where(x => (companyId == null || x.Id == companyId) && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))));

                var response = await query.FirstOrDefaultAsync();

                var client = _httpClientFactory.CreateClient();
                if (response?.EInvoiceClientId == null || response?.EInvoiceClientSecret == null)
                {
                    throw new Exception("EInvoiceTokenDateTime and EInvoiceClientSecret should not be empty");
                }
                if (response?.EInvoiceTokenDateTime == null || response.EInvoiceTokenDateTime <= DateTime.UtcNow)
                {
                    var authRequest = new EInvoiceAuthRequest
                    {
                        ClientId = response.EInvoiceClientId,
                        ClientSecret = response.EInvoiceClientSecret
                    };
                    var loginTaxpayerSystemResponse = await LoginTaxpayerSystem(authRequest);
                    if (!loginTaxpayerSystemResponse.IsSuccessful)
                        throw new HttpRequestException(
                            loginTaxpayerSystemResponse.Exception,
                            null,
                            (HttpStatusCode)(loginTaxpayerSystemResponse.StatusCode ?? 500)
                        );
                    else
                    {
                        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", loginTaxpayerSystemResponse.Result.access_token);
                    }
                }
                else
                {
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", response.EInvoiceToken);
                }

                var queryCustomer = GetDbContext().Set<Customer>().AsQueryable()
                    .Where(x => (reqBody.CustomerId == null || x.Id == reqBody.CustomerId) && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))));

                var responseCustomer = await queryCustomer.FirstOrDefaultAsync();

                string tinNumber = null;
                string idType = responseCustomer.IdentityType.Name;
                string idValue = responseCustomer.IdentityNo;
                if (responseCustomer.TinVerifyStatus != TinVerifyStatusEnum.Verified)
                {
                    var newEInvoiceSearchTinRequest = new EInvoiceSearchTinRequest
                    {
                        IdType = idType,
                        IdValue = idValue
                    };
                    var eInvoiceSearchTinResponse = await SearchTin(newEInvoiceSearchTinRequest);
                    if (!eInvoiceSearchTinResponse.IsSuccessful)
                    {
                        var tinVerifyStatusEnum = TinVerifyStatusEnum.NotFound;
                        if (eInvoiceSearchTinResponse.StatusCode == 400)
                        {
                            tinVerifyStatusEnum = TinVerifyStatusEnum.BadArgument;
                        }
                        await UpdateCustomerAsync(responseCustomer, null, tinVerifyStatusEnum, Guid.Parse(currentUser.Id));

                        throw new HttpRequestException(
                            eInvoiceSearchTinResponse.Exception,
                            null,
                            (HttpStatusCode)(eInvoiceSearchTinResponse.StatusCode ?? 500)
                        );
                    }
                    else
                    {
                        tinNumber = eInvoiceSearchTinResponse.Result.tin;
                        await UpdateCustomerAsync(responseCustomer, tinNumber, TinVerifyStatusEnum.Verified, Guid.Parse(currentUser.Id));
                    }
                }
                else
                {
                    tinNumber = responseCustomer.TinNo;

                }

                var httpResponse = await client.GetAsync($"{_settings.BaseUrl}{EINVOICE.TAXPAYERTIN.Replace("{tin}", tinNumber).Replace("{idType}", idType).Replace("{idValue}", idValue)}");

                var httpResponseMessage = httpResponse.EnsureSuccessStatusCode();
                if (httpResponseMessage.IsSuccessStatusCode)
                {
                    var responseContent = await httpResponseMessage.Content.ReadAsStringAsync();
                    result.Result = JsonSerializer.Deserialize<EInvoiceValidateTinResponse>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }
                else
                {
                    var responseContent = await httpResponseMessage.Content.ReadAsStringAsync();
                    var eInvoiceErrorLogRequest = new EInvoiceErrorLogRequest
                    {
                        ErrorCode = ((int)httpResponseMessage.StatusCode).ToString(),
                        ErrorMessage = responseContent
                    };
                    await InsertEInvoiceErrorLog(eInvoiceErrorLogRequest, Guid.Parse(currentUser.Id));

                    throw new HttpRequestException(
                        $"API request failed with status {httpResponse.StatusCode}: {responseContent}",
                        null, // inner exception
                        httpResponse.StatusCode // preserves HTTP status
                    );
                }
                return result; // await ProcessApiResponse(httpResponseMessage, result);
            }
            catch (HttpRequestException httpEx)
            {
                // Handle HTTP request exceptions specifically
                return _standardMessage.ErrorMessage<EInvoiceValidateTinResponse, EInvoiceValidateTinResponse>(
                    result,
                    GetStatusCode(httpEx), // Use appropriate status code for external service failures
                    exception: new Exception(httpEx.Message)
                );
            }
            catch (Exception ex)
            {
                // Handle general exceptions
                return _standardMessage.ErrorMessage<EInvoiceValidateTinResponse, EInvoiceValidateTinResponse>(
                    result,
                    StatusCode.InternalServerError,
                    exception: ex
                );
            }
        }

        public async Task<BaseResponse<EInvoiceCancelRejectResponse>> CancelDocument(EInvoiceCancelRequest reqBody)
        {
            var result = new BaseResponse<EInvoiceCancelRejectResponse> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
                var currentUser = getCurrentUser.Item1;
                var companyId = getCurrentUser.Item2;

                var query = GetDbContext().Set<Company>().AsQueryable()
                    .Where(x => (companyId == null || x.Id == companyId) && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))));

                var response = await query.FirstOrDefaultAsync();
                if (response?.EInvoiceClientId == null || response?.EInvoiceClientSecret == null)
                {
                    throw new Exception("EInvoiceTokenDateTime and EInvoiceClientSecret should not be empty");
                }

                #region Get Login Token
                var client = _httpClientFactory.CreateClient();
                if (response?.EInvoiceTokenDateTime == null || response.EInvoiceTokenDateTime <= DateTime.UtcNow)
                {
                    var authRequest = new EInvoiceAuthRequest
                    {
                        ClientId = response.EInvoiceClientId,
                        ClientSecret = response.EInvoiceClientSecret
                    };
                    var loginTaxpayerSystemResponse = await LoginTaxpayerSystem(authRequest);
                    if (!loginTaxpayerSystemResponse.IsSuccessful)
                    {
                        throw new HttpRequestException(
                            loginTaxpayerSystemResponse.Exception,
                            null,
                            (HttpStatusCode)(loginTaxpayerSystemResponse.StatusCode ?? 500)
                        );
                    }
                    else
                    {
                        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", loginTaxpayerSystemResponse.Result.access_token);
                    }
                }
                else
                {
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", response.EInvoiceToken);
                }
                #endregion

                var queryEInvoice = GetDbContext().Set<EInvoice>().AsQueryable()
                   .Where(x => (x.DocumentUUID == reqBody.DocumentUUID) && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))));

                var responseEInvoice = await queryEInvoice.FirstOrDefaultAsync();

                HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Put, $"{_settings.BaseUrl}{EINVOICE.CANCELREJECTDOC.Replace("{uuid}", reqBody.DocumentUUID.ToString())}")
                {
                    Content = new FormUrlEncodedContent(new[]
                    {
                        new KeyValuePair<string, string>("status", "cancelled"),
                        new KeyValuePair<string, string>("reason", reqBody.Reason)
                    })
                };
                request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                client = _httpClientFactory.CreateClient();
                var httpResponse = await client.SendAsync(request);

                var httpResponseMessage = httpResponse.EnsureSuccessStatusCode();
                if (httpResponseMessage.IsSuccessStatusCode)
                {
                    var responseContent = await httpResponseMessage.Content.ReadAsStringAsync();
                    result.Result = JsonSerializer.Deserialize<EInvoiceCancelRejectResponse>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    await UpdateEInvoiceAsync(responseEInvoice, EInvoiceStatusEnum.Cancelled, reqBody.Reason, null, Guid.Parse(currentUser.Id));
                }
                else
                {
                    var responseContent = await httpResponseMessage.Content.ReadAsStringAsync();
                    var eInvoiceErrorLogRequest = new EInvoiceErrorLogRequest
                    {
                        EInvoiceId = responseEInvoice.Id,
                        ErrorCode = ((int)httpResponseMessage.StatusCode).ToString(),
                        ErrorMessage = responseContent
                    };
                    await InsertEInvoiceErrorLog(eInvoiceErrorLogRequest, Guid.Parse(currentUser.Id));

                    throw new HttpRequestException(
                        $"API request failed with status {httpResponse.StatusCode}: {responseContent}",
                        null, // inner exception
                        httpResponse.StatusCode // preserves HTTP status
                    );
                }

                return result; // await ProcessApiResponse(httpResponseMessage, result);
            }
            #region exception handling
            catch (HttpRequestException httpEx)
            {
                // Handle HTTP request exceptions specifically
                return _standardMessage.ErrorMessage<EInvoiceCancelRejectResponse, EInvoiceCancelRejectResponse>(
                    result,
                    GetStatusCode(httpEx), // Use appropriate status code for external service failures
                    exception: new Exception(httpEx.Message)
                );
            }
            catch (Exception ex)
            {
                // Handle general exceptions
                return _standardMessage.ErrorMessage<EInvoiceCancelRejectResponse, EInvoiceCancelRejectResponse>(
                    result,
                    StatusCode.InternalServerError,
                    exception: ex
                );
            }
            #endregion
        }

        public async Task<BaseResponse<EInvoiceCancelRejectResponse>> RejectDocument(EInvoiceRejectRequest reqBody)
        {
            var result = new BaseResponse<EInvoiceCancelRejectResponse> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
                var currentUser = getCurrentUser.Item1;
                var companyId = getCurrentUser.Item2;

                var query = GetDbContext().Set<Company>().AsQueryable()
                    .Where(x => (companyId == null || x.Id == companyId) && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))));

                var response = await query.FirstOrDefaultAsync();
                if (response?.EInvoiceClientId == null || response?.EInvoiceClientSecret == null)
                {
                    throw new Exception("EInvoiceTokenDateTime and EInvoiceClientSecret should not be empty");
                }

                #region Get Login Token
                var client = _httpClientFactory.CreateClient();
                if (response?.EInvoiceTokenDateTime == null || response.EInvoiceTokenDateTime <= DateTime.UtcNow)
                {
                    var authRequest = new EInvoiceAuthRequest
                    {
                        ClientId = response.EInvoiceClientId,
                        ClientSecret = response.EInvoiceClientSecret
                    };
                    var loginTaxpayerSystemResponse = await LoginTaxpayerSystem(authRequest);
                    if (!loginTaxpayerSystemResponse.IsSuccessful)
                    {
                        throw new HttpRequestException(
                            loginTaxpayerSystemResponse.Exception,
                            null,
                            (HttpStatusCode)(loginTaxpayerSystemResponse.StatusCode ?? 500)
                        );
                    }
                    else
                    {
                        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", loginTaxpayerSystemResponse.Result.access_token);
                    }
                }
                else
                {
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", response.EInvoiceToken);
                }
                #endregion

                var queryEInvoice = GetDbContext().Set<EInvoice>().AsQueryable()
                   .Where(x => (x.DocumentUUID == reqBody.DocumentUUID && x.DocumentToken == reqBody.DocumentToken) && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))));

                var responseEInvoice = await queryEInvoice.FirstOrDefaultAsync();

                HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Put, $"{_settings.BaseUrl}{EINVOICE.CANCELREJECTDOC.Replace("{uuid}", reqBody.DocumentUUID.ToString())}")
                {
                    Content = new FormUrlEncodedContent(new[]
                    {
                        new KeyValuePair<string, string>("status", "rejected"),
                        new KeyValuePair<string, string>("reason", reqBody.Reason)
                    })
                };
                request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                client = _httpClientFactory.CreateClient();
                var httpResponse = await client.SendAsync(request);

                var httpResponseMessage = httpResponse.EnsureSuccessStatusCode();
                if (httpResponseMessage.IsSuccessStatusCode)
                {
                    var responseContent = await httpResponseMessage.Content.ReadAsStringAsync();
                    result.Result = JsonSerializer.Deserialize<EInvoiceCancelRejectResponse>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    await UpdateEInvoiceAsync(responseEInvoice, null, null, reqBody.Reason, Guid.Parse(currentUser.Id));
                }
                else
                {
                    var responseContent = await httpResponseMessage.Content.ReadAsStringAsync();
                    var eInvoiceErrorLogRequest = new EInvoiceErrorLogRequest
                    {
                        EInvoiceId = responseEInvoice.Id,
                        ErrorCode = ((int)httpResponseMessage.StatusCode).ToString(),
                        ErrorMessage = responseContent
                    };
                    await InsertEInvoiceErrorLog(eInvoiceErrorLogRequest, Guid.Parse(currentUser.Id));

                    throw new HttpRequestException(
                        $"API request failed with status {httpResponse.StatusCode}: {responseContent}",
                        null, // inner exception
                        httpResponse.StatusCode // preserves HTTP status
                    );
                }

                return result; // await ProcessApiResponse(httpResponseMessage, result);
            }
            #region exception handling
            catch (HttpRequestException httpEx)
            {
                // Handle HTTP request exceptions specifically
                return _standardMessage.ErrorMessage<EInvoiceCancelRejectResponse, EInvoiceCancelRejectResponse>(
                    result,
                    GetStatusCode(httpEx), // Use appropriate status code for external service failures
                    exception: new Exception(httpEx.Message)
                );
            }
            catch (Exception ex)
            {
                // Handle general exceptions
                return _standardMessage.ErrorMessage<EInvoiceCancelRejectResponse, EInvoiceCancelRejectResponse>(
                    result,
                    StatusCode.InternalServerError,
                    exception: ex
                );
            }
            #endregion
        }

        public async Task<BaseResponse<EInvoiceDocumentTypesResponse>> GetAllDocumentTypes()
        {
            var result = new BaseResponse<EInvoiceDocumentTypesResponse> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
                var currentUser = getCurrentUser.Item1;
                var companyId = getCurrentUser.Item2;

                var query = GetDbContext().Set<Company>().AsQueryable()
                    .Where(x => (companyId == null || x.Id == companyId) && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))));

                var response = await query.FirstOrDefaultAsync();
                if (response?.EInvoiceClientId == null || response?.EInvoiceClientSecret == null)
                {
                    throw new Exception("EInvoiceClientId and EInvoiceClientSecret should not be empty");
                }

                #region Get Login Token
                var client = _httpClientFactory.CreateClient();
                if (response?.EInvoiceTokenDateTime == null || response.EInvoiceTokenDateTime <= DateTime.UtcNow)
                {
                    var authRequest = new EInvoiceAuthRequest
                    {
                        ClientId = response.EInvoiceClientId,
                        ClientSecret = response.EInvoiceClientSecret
                    };
                    var loginTaxpayerSystemResponse = await LoginTaxpayerSystem(authRequest);
                    if (!loginTaxpayerSystemResponse.IsSuccessful)
                    {
                        throw new HttpRequestException(
                            loginTaxpayerSystemResponse.Exception,
                            null,
                            (HttpStatusCode)(loginTaxpayerSystemResponse.StatusCode ?? 500)
                        );
                    }
                    else
                    {
                        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", loginTaxpayerSystemResponse.Result.access_token);
                    }
                }
                else
                {
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", response.EInvoiceToken);
                }
                #endregion

                // Call LHDN API to get document types
                HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Get, $"{_settings.BaseUrl}{EINVOICE.DOCUMENTTYPES}");
                request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                var httpResponse = await client.SendAsync(request);
                var httpResponseMessage = httpResponse.EnsureSuccessStatusCode();

                if (httpResponseMessage.IsSuccessStatusCode)
                {
                    var responseContent = await httpResponseMessage.Content.ReadAsStringAsync();
                    result.Result = JsonSerializer.Deserialize<EInvoiceDocumentTypesResponse>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true,
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                    });

                    // If the result is null, initialize it
                    if (result.Result == null)
                    {
                        result.Result = new EInvoiceDocumentTypesResponse();
                    }

                    // If the Result property is null, initialize it
                    if (result.Result.Result == null)
                    {
                        result.Result.Result = new List<EInvoiceDocumentType>();
                    }

                    // Filter document types to only include those with version 1.0
                    if (result.Result.Result != null && result.Result.Result.Any())
                    {
                        foreach (var docType in result.Result.Result)
                        {
                            // Filter document type versions to only include version 1.0
                            if (docType.DocumentTypeVersions != null && docType.DocumentTypeVersions.Any())
                            {
                                docType.DocumentTypeVersions = docType.DocumentTypeVersions
                                    .Where(v => v.VersionNumber == "1.0")
                                    .ToList();
                            }
                        }

                        // Remove document types that don't have version 1.0
                        result.Result.Result = result.Result.Result
                            .Where(dt => dt.DocumentTypeVersions != null && dt.DocumentTypeVersions.Any())
                            .ToList();
                    }
                }
                else
                {
                    var responseContent = await httpResponseMessage.Content.ReadAsStringAsync();
                    var eInvoiceErrorLogRequest = new EInvoiceErrorLogRequest
                    {
                        ErrorCode = ((int)httpResponseMessage.StatusCode).ToString(),
                        ErrorMessage = responseContent
                    };
                    await InsertEInvoiceErrorLog(eInvoiceErrorLogRequest, Guid.Parse(currentUser.Id));

                    throw new HttpRequestException(
                        $"API request failed with status {httpResponse.StatusCode}: {responseContent}",
                        null,
                        httpResponse.StatusCode
                    );
                }

                return result;
            }
            catch (HttpRequestException httpEx)
            {
                return _standardMessage.ErrorMessage<EInvoiceDocumentTypesResponse, EInvoiceDocumentTypesResponse>(
                    result,
                    GetStatusCode(httpEx),
                    exception: new Exception(httpEx.Message)
                );
            }
            catch (Exception ex)
            {
                return _standardMessage.ErrorMessage<EInvoiceDocumentTypesResponse, EInvoiceDocumentTypesResponse>(
                    result,
                    StatusCode.InternalServerError,
                    exception: ex
                );
            }
        }

        public async Task<BaseResponse<EInvoiceSubmitDocumentResponse>> SubmitDocument(EInvoiceSubmitDocumentRequest reqBody)
        {
            var result = new BaseResponse<EInvoiceSubmitDocumentResponse> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
                var currentUser = getCurrentUser.Item1;
                var companyId = getCurrentUser.Item2;

                // Get company information
                var query = GetDbContext().Set<Company>().AsQueryable()
                    .Where(x => (companyId == null || x.Id == companyId) && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))));

                var company = await query.FirstOrDefaultAsync();
                if (company?.EInvoiceClientId == null || company?.EInvoiceClientSecret == null)
                {
                    throw new Exception("EInvoiceClientId and EInvoiceClientSecret should not be empty");
                }

                // Get transaction data
                var transaction = await GetDbContext().Set<Transaction>()
                    .Include(t => t.Branch).ThenInclude(b => b.Company)
                    .Include(t => t.Branch).ThenInclude(b => b.Address)
                    .Include(t => t.Customer)
                    .Include(t => t.BillingAddress)
                    .Include(t => t.ShippingAddress)
                    .Include(t => t.TransactionItem).ThenInclude(i => i.Product)
                    .Include(t => t.TransactionItem).ThenInclude(i => i.SalesTaxNo)
                    .Include(t => t.TransactionItem).ThenInclude(i => i.ServiceTaxNo)
                    .FirstOrDefaultAsync(t => t.Id == reqBody.TrxId && (t.IsEInvoiceCreated ?? false) && t.IsActive);

                if (transaction == null)
                {
                    throw new Exception($"Transaction with ID {reqBody.TrxId} not found");
                }

                // Set default document type if not provided
                string documentType = reqBody.DocumentType ?? "Invoice";
                string documentTypeVersion = reqBody.DocumentTypeVersion ?? "1.0"; // Use version 1.0 as required by LHDN
                string invoiceTypeCode = reqBody.InvoiceTypeCode ?? "01"; // Default to standard invoice

                // Generate XML document with invoice period
                string xmlDocument = LhdnXmlHelper.GenerateInvoiceXml(
                    transaction,
                    documentType,
                    documentTypeVersion,
                    invoiceTypeCode);

                // Canonicalize XML for consistent hashing
                string canonicalizedXml = LhdnXmlHelper.CanonicalizeXml(xmlDocument);

                // Convert to Base64
                string base64Document = LhdnXmlHelper.XmlToBase64(xmlDocument);

                // Calculate document hash according to LHDN requirements
                string documentHash = LhdnXmlHelper.CalculateHash(xmlDocument);

                // Validate document hash
                if (!LhdnXmlHelper.ValidateDocumentHash(documentHash))
                {
                    _logger.LogWarning($"Generated document hash for transaction {transaction.TrxNo} does not meet LHDN requirements: {documentHash}");
                    // Generate a fallback hash if the primary method fails
                    documentHash = BitConverter.ToString(SHA256.HashData(Encoding.UTF8.GetBytes(xmlDocument))).Replace("-", "").ToLowerInvariant();
                    _logger.LogInformation($"Using fallback hash method for transaction {transaction.TrxNo}: {documentHash}");
                }
                else
                {
                    _logger.LogInformation($"Document hash for transaction {transaction.TrxNo}: {documentHash}");
                }

                // Create EInvoice record
                var eInvoice = new EInvoice
                {
                    CompanyId = company.Id,
                    InternalNo = Guid.NewGuid().ToString(),
                    InvoiceNo = transaction.TrxNo,
                    TransactionId = transaction.Id,
                    CustomerId = transaction.CustomerId,
                    DocumentType = documentType,
                    DocumentTypeVersion = documentTypeVersion,
                    DocumentXml = xmlDocument,
                    DocumentBase64 = base64Document,
                    DocumentHash = documentHash,
                    DocumentToken = Guid.NewGuid().ToString(),
                    Status = EInvoiceStatusEnum.Pending,
                    RetryCount = 0,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Parse(currentUser.Id)
                };

                // Save EInvoice record
                await CreateAsync(eInvoice);

                // Update Transaction IsEInvoiceCreated flag to true
                transaction.IsEInvoiceCreated = true;
                transaction.UpdatedBy = Guid.Parse(currentUser.Id);
                transaction.UpdatedAt = DateTime.UtcNow;
                await UpdateAsync(transaction);

                // TODO: Move to hangfire
                //#region Get Login Token
                //var client = _httpClientFactory.CreateClient();
                //if (company?.EInvoiceTokenDateTime == null || company.EInvoiceTokenDateTime <= DateTime.UtcNow)
                //{
                //    var authRequest = new EInvoiceAuthRequest
                //    {
                //        ClientId = company.EInvoiceClientId,
                //        ClientSecret = company.EInvoiceClientSecret
                //    };
                //    var loginTaxpayerSystemResponse = await LoginTaxpayerSystem(authRequest);
                //    if (!loginTaxpayerSystemResponse.IsSuccessful)
                //    {
                //        throw new HttpRequestException(
                //            loginTaxpayerSystemResponse.Exception,
                //            null,
                //            (HttpStatusCode)(loginTaxpayerSystemResponse.StatusCode ?? 500)
                //        );
                //    }
                //    else
                //    {
                //        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", loginTaxpayerSystemResponse.Result.access_token);
                //    }
                //}
                //else
                //{
                //    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", company.EInvoiceToken);
                //}
                //#endregion

                //// Prepare request body based on the sample format
                //var documentRequest = new
                //{
                //    documents = new[]
                //    {
                //        new
                //        {
                //            format = "XML",
                //            documentHash = documentHash,
                //            codeNumber = transaction.TrxNo,
                //            document = base64Document
                //        }
                //    }
                //};

                //var jsonContent = JsonSerializer.Serialize(documentRequest);
                //var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                //// Submit document to LHDN API
                //HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Post, $"{_settings.BaseUrl}{EINVOICE.SUBMITDOCUMENT}")
                //{
                //    Content = content
                //};
                //request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                //var httpResponse = await client.SendAsync(request);

                //if (httpResponse.IsSuccessStatusCode)
                //{
                //    var responseContent = await httpResponse.Content.ReadAsStringAsync();
                //    result.Result = JsonSerializer.Deserialize<EInvoiceSubmitDocumentResponse>(responseContent, new JsonSerializerOptions
                //    {
                //        PropertyNameCaseInsensitive = true
                //    });

                //    // Update EInvoice record with response data
                //    eInvoice.SubmissionUID = result.Result.SubmissionUID;
                //    eInvoice.DocumentUUID = result.Result.DocumentUUID;
                //    eInvoice.DocumentLongId = result.Result.DocumentLongId;
                //    eInvoice.DocumentToken = result.Result.DocumentToken;
                //    eInvoice.Status = EInvoiceStatusEnum.Submitted;
                //    eInvoice.SubmittedDate = DateTime.UtcNow;
                //    eInvoice.ResponseCode = result.Result.ResponseCode;
                //    eInvoice.ResponseMessage = result.Result.ResponseMessage;
                //    eInvoice.QrCodeUrl = result.Result.QrCodeUrl;
                //    eInvoice.DocumentUrl = result.Result.DocumentUrl;
                //    eInvoice.UpdatedAt = DateTime.UtcNow;
                //    eInvoice.UpdatedBy = Guid.Parse(currentUser.Id);

                //    await UpdateAsync(eInvoice);
                //}
                //else
                //{
                //    var responseContent = await httpResponse.Content.ReadAsStringAsync();
                //    var eInvoiceErrorLogRequest = new EInvoiceErrorLogRequest
                //    {
                //        EInvoiceId = eInvoice.Id,
                //        ErrorCode = ((int)httpResponse.StatusCode).ToString(),
                //        ErrorMessage = responseContent
                //    };
                //    await InsertEInvoiceErrorLog(eInvoiceErrorLogRequest, Guid.Parse(currentUser.Id));

                //    // Update EInvoice record with error information
                //    eInvoice.Status = EInvoiceStatusEnum.Invalid;
                //    eInvoice.ResponseCode = ((int)httpResponse.StatusCode).ToString();
                //    eInvoice.ResponseMessage = responseContent;
                //    eInvoice.RetryCount = (eInvoice.RetryCount ?? 0) + 1;
                //    eInvoice.UpdatedAt = DateTime.UtcNow;
                //    eInvoice.UpdatedBy = Guid.Parse(currentUser.Id);

                //    await UpdateAsync(eInvoice);

                //    throw new HttpRequestException(
                //        $"API request failed with status {httpResponse.StatusCode}: {responseContent}",
                //        null,
                //        httpResponse.StatusCode
                //    );
                //}

                return result;
            }
            catch (HttpRequestException httpEx)
            {
                return _standardMessage.ErrorMessage<EInvoiceSubmitDocumentResponse, EInvoiceSubmitDocumentResponse>(
                    result,
                    GetStatusCode(httpEx),
                    exception: new Exception(httpEx.Message)
                );
            }
            catch (Exception ex)
            {
                return _standardMessage.ErrorMessage<EInvoiceSubmitDocumentResponse, EInvoiceSubmitDocumentResponse>(
                    result,
                    StatusCode.InternalServerError,
                    exception: ex
                );
            }
        }

        #region private function
        private async Task<BaseResponse<ResultId>> UpdateEInvoiceAsync(EInvoice updateEInvoice, EInvoiceStatusEnum? status, string? cancelReason, string? rejectReason, Guid currentUserId)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };
            try
            {
                var currentDatetime = DateTime.UtcNow;
                if (cancelReason != null)
                {
                    updateEInvoice.CancelReason = cancelReason;
                    updateEInvoice.CancelledDate = currentDatetime;
                }
                if (rejectReason != null)
                {
                    updateEInvoice.RejectReason = rejectReason;
                    updateEInvoice.RejectedDate = currentDatetime;
                }
                if (status != null)
                    updateEInvoice.Status = (EInvoiceStatusEnum)status;
                updateEInvoice.IsActive = true;
                updateEInvoice.UpdatedAt = currentDatetime;
                updateEInvoice.UpdatedBy = currentUserId;
                var company = await UpdateAsync(updateEInvoice);
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        private async Task<BaseResponse<ResultId>> UpdateCompanyAsync(Company updateCompany, string eInvoiceToken, Guid currentUserId)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };
            try
            {
                updateCompany.EInvoiceToken = eInvoiceToken;
                updateCompany.EInvoiceTokenDateTime = DateTime.UtcNow.AddMinutes(55);
                updateCompany.IsActive = true;
                updateCompany.UpdatedAt = DateTime.UtcNow;
                updateCompany.UpdatedBy = currentUserId;
                var company = await UpdateAsync(updateCompany);
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        private async Task<BaseResponse<ResultId>> UpdateCustomerAsync(Customer updateCustomer, string tinNumber, TinVerifyStatusEnum tinVerifyStatusEnum, Guid currentUserId)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };
            try
            {
                updateCustomer.TinNo = tinNumber;
                updateCustomer.TinVerifyStatus = tinVerifyStatusEnum;
                updateCustomer.IsActive = true;
                updateCustomer.UpdatedAt = DateTime.UtcNow;
                updateCustomer.UpdatedBy = currentUserId;
                var company = await UpdateAsync(updateCustomer);
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        private async Task<BaseResponse<ResultId>> InsertEInvoiceErrorLog(EInvoiceErrorLogRequest reqBody, Guid currentUserId)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };
            try
            {
                var newEInvoiceErrorLog = _mapper.Map<EInvoiceErrorLog>(reqBody);
                newEInvoiceErrorLog.IsActive = true;
                newEInvoiceErrorLog.CreatedAt = DateTime.UtcNow;
                newEInvoiceErrorLog.CreatedBy = currentUserId;
                var eInvoiceErrorLog = await CreateAsync(newEInvoiceErrorLog);
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        private async Task<BaseResponse<T>> ProcessApiResponse<T>(HttpResponseMessage response, BaseResponse<T> result)
            where T : class
        {
            var content = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                return _standardMessage.ErrorMessage<EInvoice, T>(
                    result,
                    (Enums.StatusCode)response.StatusCode,
                    exception: new HttpRequestException(content));
            }

            if (typeof(T) == typeof(NoResult))
            {
                return result;
            }

            result.Result = JsonConvert.DeserializeObject<T>(content);
            return result;
        }

        private BaseResponse<T> PropagateAuthError<T>(BaseResponse<EInvoiceAuthResponse> authResponse)
            where T : class
        {
            return _standardMessage.ErrorMessage<EInvoice, T>(
                new BaseResponse<T>(),
                (Enums.StatusCode)authResponse.StatusCode,
                exception: new UnauthorizedAccessException(authResponse.Exception));
        }
        #endregion
    }
}