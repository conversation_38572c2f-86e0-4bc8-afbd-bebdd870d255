﻿﻿using System;

namespace Dolfin.Mobile.API.Models.Dto
{
    /// <summary>
    /// DTO for file upload information
    /// </summary>
    public class FileUploadDto
    {
        /// <summary>
        /// File Id
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// The original filename
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// The full path/URL to the uploaded file
        /// </summary>
        public string FileUrl { get; set; }

        /// <summary>
        /// The file's MIME type
        /// </summary>
        public string MimeType { get; set; }

        /// <summary>
        /// The file size in bytes
        /// </summary>
        public long Size { get; set; }

        /// <summary>
        /// A pre-signed URL for temporary access to the file
        /// </summary>
        public string PreSignedUrl { get; set; }
    }
}
