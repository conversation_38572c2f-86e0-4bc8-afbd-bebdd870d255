﻿using AutoMapper;
using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Entity;
using Dolfin.Mobile.API.Connector;
using Dolfin.Mobile.API.Models;
using Dolfin.Mobile.API.Models.Request;
using Dolfin.Mobile.API.Services;
using Dolfin.Utility.Enum;
using Dolfin.Utility.Models;
using Dolfin.Utility.Utils;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.Extensions.Options;
using static Dolfin.Mobile.API.Constants.Constants;
using static Dolfin.Utility.Constant.Constant;
using static Dolfin.Utility.Enum.Enums;

namespace Dolfin.Mobile.API.Scheduler
{
    public class EmailSchedulerService : BaseComponent<Email>, IEmailSchedulerService
    {
        private readonly JobConfigService _jobConfig;
        private readonly IEmailConnector _emailConnector;
        private readonly SendEmailJobSetting _sendEmailJobSetting;
        //private readonly EmailComponent _emailComponent;

        public EmailSchedulerService(DolfinDbContext dbContext, DbContextOptions<DolfinDbContext> dbContextOptions, JobConfigService jobConfig, IEmailConnector emailConnector, IOptions<SendEmailJobSetting> sendEmailJobSetting) : base(dbContext, dbContextOptions)
        {
            _jobConfig = jobConfig;
            _emailConnector = emailConnector;
            _sendEmailJobSetting = sendEmailJobSetting.Value;
        }

        public void SendEmailById(Guid emailId)
        {
            BackgroundJob.Enqueue(() => SendEmailBatch(_sendEmailJobSetting.BatchSize, emailId));
        }

        public void SendEmailBatch()
        {
            BackgroundJob.Enqueue(() => SendEmailBatch(_sendEmailJobSetting.BatchSize, null));
        }

        public void SendEmailBatch(int BatchSize, Guid? emailId)
        {
            var maxTries = 3;
            SendEmailBatch(BatchSize, emailId, null, null, null, null, true, true, maxTries, false);
        }

        private async Task SendEmailBatch(int BatchSize, Guid? emailId, string fromEmail,
            string toEmail, DateTime? createdFromUtc, DateTime? createdToUtc,
            bool loadNotSentItemsOnly, bool loadOnlyItemsToBeSent, int maxSendTries,
            bool loadNewest)
        {
            var queuedEmails = GetEmails(BatchSize, emailId, fromEmail, toEmail, createdFromUtc, createdToUtc, loadNotSentItemsOnly, loadOnlyItemsToBeSent, maxSendTries, loadNewest);
            foreach (var queuedEmail in queuedEmails)
            {
                var bcc = String.IsNullOrWhiteSpace(queuedEmail.Bcc)
                            ? null
                            : queuedEmail.Bcc.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries);
                var cc = String.IsNullOrWhiteSpace(queuedEmail.Cc)
                            ? null
                            : queuedEmail.Cc.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries);

                var emailAccount = GetEmailAccount(queuedEmail.EmailAccountId);

                try
                {
                    EmailMessage emailMessage = new EmailMessage();
                    emailMessage.Subject = queuedEmail.Subject;
                    emailMessage.Content = queuedEmail.Body;

                    //FROM
                    emailMessage.FromAddresses.Add(new EmailAddress()
                    {
                        Name = queuedEmail.FromName,
                        Address = queuedEmail.FromEmail
                    });

                    //TO
                    emailMessage.ToAddresses.Add(new EmailAddress()
                    {
                        Name = queuedEmail.ToName,
                        Address = queuedEmail.ToEmail
                    });

                    //BCC
                    if (bcc != null)
                    {
                        foreach (var address in bcc.Where(bccValue => !String.IsNullOrWhiteSpace(bccValue)))
                        {
                            emailMessage.BCCAddresses.Add(new EmailAddress()
                            {
                                Address = address.Trim()
                            });
                        }
                    }

                    //CC
                    if (cc != null)
                    {
                        foreach (var address in cc.Where(bccValue => !String.IsNullOrWhiteSpace(bccValue)))
                        {
                            emailMessage.CCAddresses.Add(new EmailAddress()
                            {
                                Address = address.Trim()
                            });
                        }
                    }

                    _emailConnector.Send(emailMessage, emailAccount);

                    queuedEmail.SentOnUtc = DateTime.UtcNow;
                }
                catch (Exception exc)
                {
                    //logger.LogInformation(string.Format("Error sending e-mail. {0}", exc.Message), exc);
                }
                finally
                {
                    queuedEmail.SentTries = queuedEmail.SentTries + 1;
                    await UpdateQueuedEmail(queuedEmail);
                    _jobConfig.CreateUpdateJobConfig(JOBCONFIG_CODE.EMAIL);
                }
            }
        }

        private List<Email> GetEmails(int BatchSize, Guid? queueId, string fromEmail,
            string toEmail, DateTime? createdFromUtc, DateTime? createdToUtc,
            bool loadNotSentItemsOnly, bool loadOnlyItemsToBeSent, int maxSendTries,
            bool loadNewest)
        {
            //sample send email template
            //_emailComponent.InsertPasswordRecoveryEmail(1, EmailTemplateSystemNames.CustomerPasswordRecoveryMessage);

            var query = GetDbContext().Set<Email>().AsQueryable();

            fromEmail = (fromEmail ?? String.Empty).Trim();
            toEmail = (toEmail ?? String.Empty).Trim();

            if (queueId != null && queueId != Guid.Empty)
                query = query.Where(qe => qe.Id == queueId);
            if (!String.IsNullOrEmpty(fromEmail))
                query = query.Where(qe => qe.FromEmail.Contains(fromEmail));
            if (!String.IsNullOrEmpty(toEmail))
                query = query.Where(qe => qe.ToEmail.Contains(toEmail));
            if (createdFromUtc.HasValue)
                query = query.Where(qe => qe.CreatedAt >= createdFromUtc);
            if (createdToUtc.HasValue)
                query = query.Where(qe => qe.CreatedAt <= createdToUtc);
            if (loadNotSentItemsOnly)
                query = query.Where(qe => !qe.SentOnUtc.HasValue);
            if (loadOnlyItemsToBeSent)
            {
                var nowUtc = DateTime.UtcNow;
                query = query.Where(qe => !qe.DontSendBeforeDateUtc.HasValue || qe.DontSendBeforeDateUtc.Value <= nowUtc);
            }
            query = query.Where(qe => qe.SentTries < maxSendTries);
            query = loadNewest ?
                //load the newest records
                query.OrderByDescending(qe => qe.CreatedAt) :
                //load by priority
                query.OrderByDescending(qe => qe.Priority).ThenBy(qe => qe.CreatedAt);

            if (BatchSize != 0)
                query = query.Take(BatchSize);

            return query.ToList();
        }

        private async Task UpdateQueuedEmail(Email queuedEmail)
        {
            if (queuedEmail == null)
                throw new ArgumentNullException("queuedEmail");

            await UpdateAsync(queuedEmail);
        }

        private EmailAccount GetEmailAccount(Guid id)
        {
            if (id == Guid.Empty)
                throw new ArgumentNullException("account id");

            var emailAccount = GetDbContext().Set<EmailAccount>().AsQueryable().Where(e => e.Id == id).FirstOrDefault();

            if (emailAccount == null)
                throw new ArgumentNullException("emailAccount");

            return emailAccount;
        }
    }
}
