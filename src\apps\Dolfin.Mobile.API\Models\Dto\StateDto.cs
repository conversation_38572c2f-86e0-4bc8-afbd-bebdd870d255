﻿using System;
using System.Collections.Generic;

namespace Dolfin.Mobile.API.Models.Dto
{
    public partial class StateDto
    {
        public StateDto()
        {
        }
        public required Guid Id { get; set; }
        public required string Name { get; set; }
        public required string Abbreviation { get; set; }
        public bool Published { get; set; }
        public int? DisplayOrder { get; set; }
        public Guid CountryId { get; set; }

        public virtual CountryDto? Country { get; set; }
    }
}
