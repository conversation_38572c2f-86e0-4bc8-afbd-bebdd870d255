using Dolfin.Framework.Data.Domains;

namespace Dolfin.Mobile.API.Models.Dto
{
    /// <summary>
    /// DTO for Prefix entity
    /// </summary>
    public class PrefixDto
    {
        /// <summary>
        /// The unique identifier for the prefix
        /// </summary>
        public Guid Id { get; set; }
        
        /// <summary>
        /// The name of the table for which the prefix is used
        /// </summary>
        public required string TableName { get; set; }
        
        /// <summary>
        /// The prefix value used for code generation
        /// </summary>
        public required string PrefixValue { get; set; }
        
        /// <summary>
        /// The last number used for code generation
        /// </summary>
        public int LastNumber { get; set; }
        
        /// <summary>
        /// The padding length for the numeric part
        /// </summary>
        public int PaddingLength { get; set; }
        
        /// <summary>
        /// The branch ID associated with this prefix
        /// </summary>
        public Guid BranchId { get; set; }
        
        /// <summary>
        /// The branch associated with this prefix
        /// </summary>
        public virtual BranchDto? Branch { get; set; }
        
        /// <summary>
        /// Whether the prefix is active
        /// </summary>
        public bool IsActive { get; set; }
        
        /// <summary>
        /// When the prefix was created
        /// </summary>
        public DateTime CreatedAt { get; set; }
        
        /// <summary>
        /// Who created the prefix
        /// </summary>
        public Guid CreatedBy { get; set; }
        
        /// <summary>
        /// When the prefix was last updated
        /// </summary>
        public DateTime? UpdatedAt { get; set; }
        
        /// <summary>
        /// Who last updated the prefix
        /// </summary>
        public Guid? UpdatedBy { get; set; }
    }
}
