﻿INSERT INTO "UOMCategories" ("Id", "Code", "Name", "IsActive", "Created<PERSON>y", "CreatedAt") 
VALUES
    ('8ac17a71-ea44-4717-aec2-fb34aca8cddb', 'QTY', 'Quantity', true, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', NOW()),
    ('292c029d-b514-4686-8f7a-7f4141088fa5', 'LNG', 'Length', true, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', NOW()),
    ('4132e334-8b31-491f-a603-254faf35864e', 'AREA', 'Area', true, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', NOW()),
    ('b4aedf88-d131-4ef8-b8fc-c99fb6269f91', 'VOL', 'Volume', true, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', NOW()),
    ('6beef2b4-cce3-42d4-8bc2-67e6f9364071', 'WT', 'Weight', true, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', NOW()),
    ('9faf27e0-2ee1-4aa0-b58e-6b1b1bb5bb6c', 'TIME', 'Time', true, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', NOW());

INSERT INTO "UOM" ("Id", "Code", "Name", "Description", "UOMCategoryId", "IsActive", "CreatedBy", "CreatedAt") 
VALUES
-- Quantity
('c7ed2fff-c424-4a8b-9f3b-49bdd10f0892', 'PCS', 'Pieces', 'Single items, tools, parts', (SELECT "Id" FROM "UOMCategories" WHERE "Code" = 'QTY'), true, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', NOW()),
('836e5382-8824-45e6-8414-777e4fc6c5e6', 'EA', 'Each', 'Each item', (SELECT "Id" FROM "UOMCategories" WHERE "Code" = 'QTY'), true, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', NOW()),
('7cfae69b-0485-4b4b-b387-662dad293a19', 'PKT', 'Packet', 'Packet of items', (SELECT "Id" FROM "UOMCategories" WHERE "Code" = 'QTY'), true, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', NOW()),
('538cb9e8-47be-45bd-9de2-4c7b94e219ce', 'CTN', 'Carton', 'Carton box', (SELECT "Id" FROM "UOMCategories" WHERE "Code" = 'QTY'), true, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', NOW()),
('0b615832-acb7-4893-a724-87c197b8b5c1', 'BOX', 'Box', 'Box of items', (SELECT "Id" FROM "UOMCategories" WHERE "Code" = 'QTY'), true, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', NOW()),
('ae502a1b-6b7f-4c68-bc5b-013d9ba76147', 'BAG', 'Bag', 'Bag of goods', (SELECT "Id" FROM "UOMCategories" WHERE "Code" = 'QTY'), true, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', NOW()),
('1608a716-73da-4bf9-a578-9499b16ef60c', 'ROLL', 'Roll', 'Rolled items', (SELECT "Id" FROM "UOMCategories" WHERE "Code" = 'QTY'), true, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', NOW()),
('53723ad5-66dc-4135-82d8-3e99a27cf766', 'SET', 'Set', 'Set of items', (SELECT "Id" FROM "UOMCategories" WHERE "Code" = 'QTY'), true, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', NOW()),
('c7a39d49-67c4-40d3-8e3e-213653c9f676', 'DOZ', 'Dozen', 'Group of 12', (SELECT "Id" FROM "UOMCategories" WHERE "Code" = 'QTY'), true, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', NOW()),
('6e20b4e7-e02d-4da8-bde6-9a715b0dbb6e', 'PAIR', 'Pair', 'Two items', (SELECT "Id" FROM "UOMCategories" WHERE "Code" = 'QTY'), true, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', NOW()),

-- Length
('0580cdbb-cf2f-4377-b505-8bedc8549d24', 'METER', 'Meter', 'Length measurement', (SELECT "Id" FROM "UOMCategories" WHERE "Code" = 'LNG'), true, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', NOW()),
('4baae87c-76f0-4499-9036-b75780bbda9f', 'CM', 'Centimeter', 'Length measurement in centimeters', (SELECT "Id" FROM "UOMCategories" WHERE "Code" = 'LNG'), true, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', NOW()),
('710becc4-8650-42b7-a408-8c69dce27941', 'MM', 'Millimeter', 'Length measurement in millimeters', (SELECT "Id" FROM "UOMCategories" WHERE "Code" = 'LNG'), true, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', NOW()),
('dd25b7a1-67bd-4ecd-ab5d-3203f4506207', 'INCH', 'Inch', 'Imperial length measurement', (SELECT "Id" FROM "UOMCategories" WHERE "Code" = 'LNG'), true, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', NOW()),

-- Area
('9fe33e5a-65d3-4b48-8547-777ee013eb8d', 'SQM', 'Square Meter', 'Area measurement', (SELECT "Id" FROM "UOMCategories" WHERE "Code" = 'AREA'), true, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', NOW()),
('02a55014-6e3d-4706-9dfb-c72b8880b0f9', 'SQFT', 'Square Foot', 'Imperial area measurement', (SELECT "Id" FROM "UOMCategories" WHERE "Code" = 'AREA'), true, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', NOW()),

-- Volume
('a208e64a-3b5c-401f-8001-14fa4ed8e9c2', 'LTR', 'Liter', 'Volume in liters', (SELECT "Id" FROM "UOMCategories" WHERE "Code" = 'VOL'), true, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', NOW()),
('f27e8392-a6a3-43b6-993c-25890e7aa5f3', 'ML', 'Milliliter', 'Volume in milliliters', (SELECT "Id" FROM "UOMCategories" WHERE "Code" = 'VOL'), true, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', NOW()),

-- Weight
('d478c31c-c3fc-483a-b3a6-f59d783163b8', 'G', 'Gram', 'Weight in grams', (SELECT "Id" FROM "UOMCategories" WHERE "Code" = 'WT'), true, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', NOW()),
('fff50d73-a994-4861-ac40-db6321239f49', 'KG', 'Kilogram', 'Weight in kilograms', (SELECT "Id" FROM "UOMCategories" WHERE "Code" = 'WT'), true, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', NOW()),
('4613a907-c3e9-47ae-8fa3-8e7ed31669b3', 'TON', 'Ton', 'Metric ton', (SELECT "Id" FROM "UOMCategories" WHERE "Code" = 'WT'), true, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', NOW()),

-- Time
('2bc298ea-2b35-469d-9b81-340f2a3246c7', 'HOURS', 'Hours', 'Common for work, time tracking', (SELECT "Id" FROM "UOMCategories" WHERE "Code" = 'TIME'), true, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', NOW()),
('f9ec9359-acdb-4cdf-9de9-9b9a3b3aef36', 'MIN', 'Minutes', 'Smaller time measurement', (SELECT "Id" FROM "UOMCategories" WHERE "Code" = 'TIME'), true, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', NOW());