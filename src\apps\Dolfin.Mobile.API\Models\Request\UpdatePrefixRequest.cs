using System.ComponentModel.DataAnnotations;

namespace Dolfin.Mobile.API.Models.Request
{
    /// <summary>
    /// Request model for updating an existing prefix
    /// </summary>
    public class UpdatePrefixRequest : PrefixBaseRequest
    {
        /// <summary>
        /// The ID of the prefix to update
        /// </summary>
        [Required]
        public Guid Id { get; set; }

        // Note: TableName and BranchId are not included in the update request
        // as they should not be modifiable after creation
    }
}
