using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Repository.Interfaces;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Dolfin.Mobile.API.Repositories.Interfaces
{
    /// <summary>
    /// Repository interface for Product entity
    /// </summary>
    public interface IProductRepository : IRepository<Product>
    {
        /// <summary>
        /// Get products by company ID
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <returns>List of products for the company</returns>
        Task<IEnumerable<Product>> GetProductsByCompanyAsync(Guid companyId);
        
        /// <summary>
        /// Get products by category ID
        /// </summary>
        /// <param name="categoryId">Category ID</param>
        /// <returns>List of products in the category</returns>
        Task<IEnumerable<Product>> GetProductsByCategoryAsync(Guid categoryId);
        
        /// <summary>
        /// Get product with all related entities
        /// </summary>
        /// <param name="productId">Product ID</param>
        /// <returns>Product with all related entities</returns>
        Task<Product> GetProductWithDetailsAsync(Guid productId);
        
        /// <summary>
        /// Invalidate product cache
        /// </summary>
        /// <param name="productId">Product ID</param>
        void InvalidateProductCache(Guid productId);
        
        /// <summary>
        /// Invalidate company products cache
        /// </summary>
        /// <param name="companyId">Company ID</param>
        void InvalidateCompanyProductsCache(Guid companyId);
    }
}
