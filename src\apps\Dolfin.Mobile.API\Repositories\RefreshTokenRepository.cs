using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Entity;
using Dolfin.Mobile.API.Repositories.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Dolfin.Mobile.API.Repositories
{
    /// <summary>
    /// Repository for managing refresh tokens
    /// </summary>
    public class RefreshTokenRepository : IRefreshTokenRepository
    {
        private readonly DolfinDbContext _context;
        private readonly ILogger<RefreshTokenRepository> _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="RefreshTokenRepository"/> class
        /// </summary>
        /// <param name="context">The database context</param>
        /// <param name="logger">The logger</param>
        public RefreshTokenRepository(DolfinDbContext context, ILogger<RefreshTokenRepository> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <inheritdoc/>
        public async Task<RefreshToken> GetByTokenAsync(string token)
        {
            _logger.LogInformation("Getting refresh token: {Token}", token);
            return await _context.RefreshTokens.SingleOrDefaultAsync(rt => rt.Token == token);
        }

        /// <inheritdoc/>
        public async Task AddAsync(RefreshToken refreshToken)
        {
            _logger.LogInformation("Adding refresh token for user: {UserId}", refreshToken.UserId);
            await _context.RefreshTokens.AddAsync(refreshToken);
            await _context.SaveChangesAsync();
        }

        /// <inheritdoc/>
        public async Task UpdateAsync(RefreshToken refreshToken)
        {
            _logger.LogInformation("Updating refresh token: {Token}", refreshToken.Token);
            _context.RefreshTokens.Update(refreshToken);
            await _context.SaveChangesAsync();
        }

        /// <inheritdoc/>
        public async Task<bool> RevokeAsync(string token, string reason = null)
        {
            _logger.LogInformation("Revoking refresh token: {Token}, Reason: {Reason}", token, reason ?? "Not specified");
            var refreshToken = await _context.RefreshTokens.SingleOrDefaultAsync(rt => rt.Token == token);
            if (refreshToken == null)
            {
                _logger.LogWarning("Refresh token not found: {Token}", token);
                return false;
            }

            refreshToken.Invalidated = true;
            await _context.SaveChangesAsync();
            _logger.LogInformation("Refresh token revoked: {Token}", token);
            return true;
        }

        /// <inheritdoc/>
        public async Task<int> RevokeAllUserTokensAsync(string userId, string reason = null)
        {
            _logger.LogInformation("Revoking all refresh tokens for user: {UserId}, Reason: {Reason}", userId, reason ?? "Not specified");
            var refreshTokens = await _context.RefreshTokens
                .Where(rt => rt.UserId == userId && !rt.Invalidated)
                .ToListAsync();

            if (!refreshTokens.Any())
            {
                _logger.LogInformation("No active refresh tokens found for user: {UserId}", userId);
                return 0;
            }

            foreach (var token in refreshTokens)
            {
                token.Invalidated = true;
            }

            await _context.SaveChangesAsync();
            _logger.LogInformation("Revoked {Count} refresh tokens for user: {UserId}", refreshTokens.Count, userId);
            return refreshTokens.Count;
        }
    }
}
