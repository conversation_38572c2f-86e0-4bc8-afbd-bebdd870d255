﻿namespace Dolfin.Mobile.API.Models.Request
{
    /// <summary>
    /// Model for user registration
    /// </summary>
    public class Register
    {
        /// <summary>
        /// Email address of the user (required)
        /// </summary>
        public required string Email { get; set; }

        /// <summary>
        /// Password for the account (required)
        /// </summary>
        public required string Password { get; set; }

        /// <summary>
        /// Confirmation of the password (required, must match Password)
        /// </summary>
        public required string ConfirmPassword { get; set; }

        /// <summary>
        /// Username for the account (required)
        /// </summary>
        public required string Username { get; set; }

        /// <summary>
        /// Full name of the user (required)
        /// </summary>
        public required string FullName { get; set; }

        /// <summary>
        /// Primary phone number (required)
        /// </summary>
        public required string PhoneNo1 { get; set; }

        /// <summary>
        /// Secondary phone number (optional)
        /// </summary>
        public string? PhoneNo2 { get; set; }

        /// <summary>
        /// Primary fax number (optional)
        /// </summary>
        public string? FaxNo1 { get; set; }

        /// <summary>
        /// Secondary fax number (optional)
        /// </summary>
        public string? FaxNo2 { get; set; }

        /// <summary>
        /// Serial number (optional)
        /// </summary>
        public string? SerialNo { get; set; }

        /// <summary>
        /// Branch ID (optional)
        /// </summary>
        public Guid? BranchId { get; set; }

        /// <summary>
        /// Company ID (optional)
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// User type ID (optional)
        /// </summary>
        public Guid? UserTypeId { get; set; }
    }

    /// <summary>
    /// Model for user login
    /// </summary>
    public class Login
    {
        /// <summary>
        /// Email address of the user
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// Password for the account
        /// </summary>
        public string Password { get; set; }
    }

    public class ForgotPassword
    {
        public string Email { get; set; }
    }

    public class ResetPassword
    {
        public string Email { get; set; }
        public string Token { get; set; }
        public string NewPassword { get; set; }
        public string ConfirmPassword { get; set; }
    }

    public class ChangePassword
    {
        public string CurrentPassword { get; set; }
        public string NewPassword { get; set; }
        public string ConfirmNewPassword { get; set; }
    }
}
