﻿using Dolfin.Framework.Data.Domains.CustomIdentity;
using Dolfin.Framework.Data.Entity;
using Dolfin.Mobile.API.Models;
using Dolfin.Mobile.API.Models.Request;
using Microsoft.AspNetCore.Identity;

namespace Dolfin.Mobile.API.Services
{
    public interface IEmailSender
    {
        Task<EmailTemplate> GetEmailTemplate(string messageTemplateName);
        Task<Guid> InsertEmail(EmailTemplate messageTemplate,
            EmailAccount emailAccount/*, IEnumerable<Token> tokens*/,
            string toEmailAddress, string toName,
            string attachmentFilePath = null, string attachmentFileName = null, DateTime? dontSendBeforeDateUtc = null,
            string replyToEmailAddress = null, string replyToName = null,
            string fromEmail = null, string fromName = null, string subject = null, string body = null);
    }
}
