using Hangfire;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Dolfin.Mobile.API.Helper
{
    /// <summary>
    /// Startup filter to register recurring jobs after the application has started
    /// </summary>
    /// <typeparam name="TJob">The job type to register</typeparam>
    public class RecurringJobStartupFilter<TJob> : IStartupFilter where TJob : BaseRecurringJob
    {
        private readonly string _jobId;
        private readonly string _cronExpression;
        private readonly ILogger _logger;

        public RecurringJobStartupFilter(string jobId, string cronExpression, ILogger logger)
        {
            _jobId = jobId;
            _cronExpression = cronExpression;
            _logger = logger;
        }

        public Action<IApplicationBuilder> Configure(Action<IApplicationBuilder> next)
        {
            return app =>
            {
                // Register the recurring job using the static RecurringJob API
                try
                {
                    using (var scope = app.ApplicationServices.CreateScope())
                    {
                        // Get the job instance from the service provider
                        var job = scope.ServiceProvider.GetRequiredService<TJob>();
                        
                        _logger.LogInformation($"Registering recurring job {_jobId} with cron expression: {_cronExpression}");
                        
                        // Register the job with Hangfire
                        RecurringJob.AddOrUpdate<TJob>(
                            _jobId,
                            j => j.Execute(),
                            _cronExpression,
                            TimeZoneInfo.Local
                        );
                        
                        _logger.LogInformation($"Successfully registered recurring job {_jobId}");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Failed to register recurring job {_jobId}");
                }

                // Call the next middleware in the pipeline
                next(app);
            };
        }
    }
}
