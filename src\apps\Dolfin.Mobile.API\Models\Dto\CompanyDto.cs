﻿using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Domains.CustomIdentity;

namespace Dolfin.Mobile.API.Models.Dto
{
    public class CompanyDto
    {
        public CompanyDto()
        {
            Branch = new HashSet<BranchDto>();
            //User = new HashSet<ApplicationUser>();
        }
        public Guid Id { get; set; }
        public required string Code { get; set; }
        public required string Name { get; set; }
        public required string RegNo { get; set; }
        public required string SstNo { get; set; }
        public Guid DefaultSalesTaxNoId { get; set; }
        public Guid DefaultServiceTaxNoId { get; set; }
        public required string TinNo { get; set; }
        public string? Logo { get; set; }

        // Company logo file
        public FileUploadDto? LogoFile { get; set; }
        public bool IsBranchSameProduct { get; set; }
        public bool IsEnableInventory { get; set; }
        public bool IsRoundingAdjustment { get; set; }
        public DateTime ExpiredAt { get; set; }
        public string? WebSiteUrl { get; set; }
        public Guid CurrencyId { get; set; }
        public virtual TaxRateDto? DefaultSalesTaxNo { get; set; }
        public virtual TaxRateDto? DefaultServiceTaxNo { get; set; }
        //public virtual Currency? Currency { get; set; }
        public virtual ICollection<BranchDto> Branch { get; set; }
        //public virtual ICollection<ApplicationUser> User { get; set; }
    }
}
