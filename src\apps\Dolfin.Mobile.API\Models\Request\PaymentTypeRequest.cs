﻿﻿using System.ComponentModel.DataAnnotations;

namespace Dolfin.Mobile.API.Models.Request
{
    /// <summary>
    /// Request model for creating a new payment type
    /// </summary>
    public class PaymentTypeRequest
    {
        /// <summary>
        /// The code for the payment type
        /// </summary>
        [Required]
        public string Code { get; set; }

        /// <summary>
        /// The name of the payment type
        /// </summary>
        [Required]
        public string Name { get; set; }

        /// <summary>
        /// The account code for the payment type
        /// </summary>
        public string? AccountCode { get; set; }

        /// <summary>
        /// The ID of the account group this payment type belongs to
        /// </summary>
        public Guid? AccountGroupId { get; set; }
    }
}
