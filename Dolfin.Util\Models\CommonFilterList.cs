﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;

namespace Dolfin.Utility.Models
{
    public class CommonFilterList
    {
        public List<CommonFilter>? FilterList { get; set; } = new();
    }

    public class CommonFilter
    {
        public string FilterName { get; set; } = string.Empty;
        public string FilterValue { get; set; } = string.Empty;
    }

    //public class QueryListBinder<T> : IModelBinder where T : new()
    //{
    //    public Task BindModelAsync(ModelBindingContext bindingContext)
    //    {
    //        var values = bindingContext.ValueProvider.GetValue(bindingContext.ModelName);

    //        if (values.Length == 0)
    //        {
    //            bindingContext.Result = ModelBindingResult.Success(new List<T>());
    //            return Task.CompletedTask;
    //        }

    //        try
    //        {
    //            var list = values.Select(value =>
    //            {
    //                var parts = value.Split(':'); // Expected format: "FilterName:Category,FilterValue:Electronics"
    //                var obj = new T();
    //                var properties = typeof(T).GetProperties();

    //                foreach (var part in parts)
    //                {
    //                    var keyValue = part.Split('=');
    //                    if (keyValue.Length == 2)
    //                    {
    //                        var prop = properties.FirstOrDefault(p => p.Name.Equals(keyValue[0], StringComparison.OrdinalIgnoreCase));
    //                        prop?.SetValue(obj, keyValue[1]);
    //                    }
    //                }
    //                return obj;
    //            }).ToList();

    //            bindingContext.Result = ModelBindingResult.Success(list);
    //        }
    //        catch (Exception)
    //        {
    //            bindingContext.ModelState.AddModelError(bindingContext.ModelName, "Invalid format.");
    //        }

    //        return Task.CompletedTask;
    //    }
    //}
    public class CommonFilterListModelBinder : IModelBinder
    {
        public Task BindModelAsync(ModelBindingContext bindingContext)
        {
            var filterNames = bindingContext.ValueProvider.GetValue("filterNames").ToString().Split(',');
            var filterValues = bindingContext.ValueProvider.GetValue("filterValues").ToString().Split(',');

            if (filterNames.Length != filterValues.Length)
            {
                bindingContext.ModelState.AddModelError("Filters", "Mismatched filter names and values.");
                return Task.CompletedTask;
            }

            var filters = filterNames.Zip(filterValues, (name, value) => new CommonFilter { FilterName = name, FilterValue = value }).ToList();
            var result = new CommonFilterList { FilterList = filters };

            bindingContext.Result = ModelBindingResult.Success(result);
            return Task.CompletedTask;
        }
    }
}
