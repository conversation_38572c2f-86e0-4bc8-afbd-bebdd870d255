-- Insert sample data into Country
INSERT INTO "Country" ("Id", "Name" , "TwoLetterIsoCode" , "ThreeLetterIsoCode" , "NumericIsoCode" , "Published" , "DisplayOrder", "CreatedBy", "CreatedAt")
VALUES
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Malaysia', 'MY', 'MYS', 458, true, 1, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', NOW());
    
-- Insert sample data into State
do $$
DECLARE my_id UUID; 
begin 
	SELECT "Id" into my_id FROM "Country" WHERE "TwoLetterIsoCode" = 'MY';

	INSERT INTO "State" ("Id", "Name", "Abbreviation", "Published", "DisplayOrder", "CountryId", "CreatedBy", "CreatedAt")
	VALUES
    (
        (SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)),
        'Johor', 'JHR', true, 1, my_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()
    ),
    (
        (SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)),
        'Kedah', 'KDH', true, 2, my_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()
    ),
    (
        (SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)),
        'Kelantan', 'KTN', true, 3, my_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()
    ),
    (
        (SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)),
        'Malacca', 'MLK', true, 4, my_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()
    ),
    (
        (SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)),
        'Negeri Sembilan', 'NSN', true, 5, my_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()
    ),
    (
        (SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)),
        'Pahang', 'PHG', true, 6, my_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()
    ),
    (
        (SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)),
        'Penang', 'PNG', true, 7, my_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()
    ),
    (
        (SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)),
        'Perak', 'PRK', true, 8, my_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()
    ),
    (
        (SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)),
        'Perlis', 'PLS', true, 9, my_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()
    ),
    (
        (SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)),
        'Selangor', 'SGR', true, 10, my_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()
    ),
    (
        (SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)),
        'Terengganu', 'TRG', true, 11, my_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()
    ),
    (
        (SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)),
        'Kuala Lumpur', 'KUL', true, 12, my_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()
    ),
    (
        (SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)),
        'Putrajaya', 'PJY', true, 13, my_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()
    ),
    (
        (SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)),
        'Labuan', 'LBN', true, 14, my_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()
    ),
    (
        (SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)),
        'Sabah', 'SBH', true, 15, my_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()
    ),
    (
        (SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)),
        'Sarawak', 'SWK', true, 16, my_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()
    );
END $$;

-- Insert into Region
INSERT INTO "Region" ("Id", "Name", "Published", "DisplayOrder", "CreatedBy", "CreatedAt")
VALUES
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Air Keroh', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Ajil', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Al Muktatfi Billah Shah', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Alor Gajah', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Alor Setar', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Alor Star', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Ampang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Arau', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Asahan', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Asajaya', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Ayer Baloi', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Ayer Hitam', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Ayer Itam', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Ayer Keroh', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Ayer Lanas', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Ayer Puteh', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Ayer Tawar', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Ayer Tawar 2', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Ayer Tawar 3', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Ayer Tawar 4', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Ayer Tawar 5', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bachok', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bagan Datoh', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bagan Serai', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bahau', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Balik Pulau', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Baling', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Balingian', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Balok', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bandar Baharu', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bandar Bahru', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bandar Baru Bangi', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bandar Baru Enstek', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bandar Bera', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bandar Penawar', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bandar Puncak Alam', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bandar Pusat Jengka', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bandar Seri Iskandar', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bandar Seri Jempol', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bandar Tenggara', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bandar Tun Abdul Razak', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bangi', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Banting', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Baram', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Batang Berjuntai', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Batang Kali', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Batangkali', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Batu Anam', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Batu Arang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Batu Caves', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Batu Ferringhi', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Batu Gajah', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Batu Kikir', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Batu Kurau', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Batu Maung', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Batu Pahat', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bau', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bayan Lepas', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Beaufort', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bedong', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Behrang Stesen', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bekenu', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bekok', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Belaga', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Belawai', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Beluran', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bemban', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bentong', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Benut', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Beranang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Betong', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Beverly', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bidor', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bintangor', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bintulu', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bongawan', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bota', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Brinchang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bruas', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bukit Besi', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bukit Fraser', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bukit Gambir', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bukit Goh', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bukit Kayu Hitam', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bukit Mertajam', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bukit Pasir', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bukit Payong', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Bukit Rotan', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Butterworth', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Ceneh', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Chaah', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Chalok', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Changkat Jering', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Changkat Keruing', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Changloon', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Chemor', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Chenderiang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Chenderong Balai', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Chenor', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Cherang Ruku', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Cheras', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Chikus', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Chini', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Cukai', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Cyberjaya', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Dabong', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Dalat', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Damak', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Daro', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Debak', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Dengkil', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Dong', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Dungun', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Durian Tunggal', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Endau', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Enggor', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Engkilili', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Gambang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Gelang Patah', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Gelugor', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Gemas', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Gemencheh', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Genting Highlands', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Gerik', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Gerisek', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Gombak', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Gopeng', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Gua Musang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Gugusan Taib Andak', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Gurun', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Hulu Langat', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Hutan Melintang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Inanam', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Intan', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Ipoh', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Jaya Gading', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Jeli', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Jelutong', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Jementah', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Jeniang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Jenjarom', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Jeram', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Jerantut', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Jerteh', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Jitra', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Johol', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Johor Bahru', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Julau', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kabong', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kahang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kajang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kaki Bukit', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kampar', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kampung Gajah', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kampung Kepayang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kampung Raja', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kamunting', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kangar', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kanowit', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kapar', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kapit', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Karak', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Karangan', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kem Desa Pahlawan', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kem Trendak', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kemasek', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kemayan', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Keningau', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kepala Batas', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kerling', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kerteh', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Ketengah Jaya', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Ketereh', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kijal', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Klang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Klia', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kluang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kodiang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kota', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kota Bahru', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kota Belud', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kota Bharu', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kota Kinabalu', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kota Kinabatangan', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kota Kuala Muda', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kota Marudu', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kota Samarahan', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kota Sarang Semut', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kota Tinggi', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kuala Balah', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kuala Berang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kuala Besut', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kuala Kangsar', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kuala Kedah', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kuala Ketil', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kuala Klawang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kuala Krai', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kuala Krau', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kuala Kubu Baru', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kuala Kurau', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kuala Lipis', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kuala Lumpur', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kuala Nerang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kuala Pegang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kuala Penyu', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kuala Perlis', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kuala Pilah', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kuala Rompin', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kuala Selangor', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kuala Sepetang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kuala Terengganu', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kuantan', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kubang Semang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kuching', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kudat', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kukup', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kulai', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kulim', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kunak', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Kupang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Labis', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Labu', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Labuan', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Lahad Datu', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Lambor Kanan', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Lanchang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Langgar', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Langkap', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Langkawi', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Lawas', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Layang-Layang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Lenggong', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Likas', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Limbang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Lingga', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Linggi', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Long Lama', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Lubok Antu', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Lumut', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Lunas', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Lundu', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Lurah Bilut', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Lutong', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Machang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Malim Nawar', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Mambang Di Awan', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Manong', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Mantin', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Maran', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Marang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Masai', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Masjid Tanah', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Matang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Matu', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Melaka', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Melor', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Membakut', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Mentakab', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Menumbok', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Merbok', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Merlimau', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Mersing', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Miri', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Muadzam Shah', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Muar', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Mukah', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Nabawan', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Nanga Medamit', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Niah', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Nibong Tebal', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Nilai', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Nusajaya', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Padang Besar', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Padang Rengas', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Padang Serai', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Padang Tengku', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Pagoh', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Paka', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Paloh', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Pamol', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Panchor', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Pandan', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Pangkor', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Pantai Remis', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Papar', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Parit', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Parit Buntar', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Parit Jawa', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Parit Raja', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Parit Sulong', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Pasir Gudang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Pasir Mas', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Pasir Puteh', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Pekan Nenas', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Pelabuhan Klang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Penaga', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Penampang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Penang Hill', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Pendang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Pengerang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Pengkalan Hulu', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Perai', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Permaisuri', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Permatang Pauh', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Petaling Jaya', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Pokok Sena', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Pontian', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Port Dickson', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Pulai Chondong', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Pulau Carey', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Pulau Indah', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Pulau Ketam', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Pulau Pinang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Pusa', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Pusat Bandar Palong', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Pusing', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Putatan', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Putrajaya', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Ranau', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Rantau', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Rantau Panjang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Rasa', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Raub', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Rawang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Rembau', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Rengam', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Rengit', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Ringlet', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Roban', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Rompin', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Sabak Bernam', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Sandakan', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Saratok', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Sarikei', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Sauk', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Sebauh', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Sebuyau', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Sega', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Segamat', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Sekinchan', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Selama', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Selandar', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Selekoh', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Selising', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Semenyih', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Semerah', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Semporna', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Senai', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Senggarang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Sepang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Serdang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Seremban', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Serendah', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Seri Gading', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Seri Kembangan', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Seri Manjong', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Seri Manjung', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Seri Medan', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Serian', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Setapak', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Shah Alam', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Si Rusa', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Sibu', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Siburan', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Sik', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Simpang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Simpang Ampat', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Simpang Ampat Semanggol', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Simpang Durian', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Simpang Empat', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Simpang Pertang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Simpang Rengam', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Simunjan', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Sipitang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Sitiawan', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Slim River', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Song', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Spaoh', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Sri Aman', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Sri Gading', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Sri Medan', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Subang Airport', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Subang Jaya', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Sundar', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Sungai Ayer Tawar', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Sungai Besar', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Sungai Buloh', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Sungai Jawi', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Sungai Koyan', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Sungai Lembing', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Sungai Mati', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Sungai Pelek', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Sungai Petani', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Sungai Rambai', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Sungai Ruan', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Sungai Siput', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Sungai Sumun', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Sungai Tong', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Sungai Udang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Sungkai', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Taiping', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Tambunan', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Tamparuli', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Tampin', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Tanah Merah', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Tanah Rata', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Tangkak', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Tanjong Bungah', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Tanjong Ipoh', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Tanjong Karang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Tanjong Kling', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Tanjong Malim', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Tanjong Piandang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Tanjong Rambutan', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Tanjong Sepat', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Tanjong Tualang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Tanjung Aru', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Tanjung Bungah', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Tapah', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Tapah Road', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Tasek Gelugor', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Tasek Gelugur', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Tatau', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Tawau', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Telok Panglima', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Telok Panglima Garang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Teluk Intan', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Temangan', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Temerloh', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Temoh', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Tenghilan', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Tenom', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Tldm Lumut', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Triang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Trolak', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Trong', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Tronoh', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Tuaran', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Tumpat', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Ulu Bernam', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Ulu Kinta', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Ulu Tiram', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Universiti Utara Malaysia', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Usm Pulau Pinang', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Wakaf Bharu', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), 'Yan', true, NULL, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now());
   
-- Insert into RegionState
DO $$
DECLARE
    johor_id UUID;
    kedah_id UUID;
    kelantan_id UUID;
    malacca_id UUID;
    negeri_sembilan_id UUID;
    pahang_id UUID;
    penang_id UUID;
    perak_id UUID;
    perlis_id UUID;
    selangor_id UUID;
    terengganu_id UUID;
    kuala_lumpur_id UUID;
    putrajaya_id UUID;
    labuan_id UUID;
    sabah_id UUID;
    sarawak_id UUID;
BEGIN
    -- Assign the value to each variable
    SELECT "Id" INTO johor_id FROM "State" WHERE "Name" = 'Johor';
    SELECT "Id" INTO kedah_id FROM "State" WHERE "Name" = 'Kedah';
    SELECT "Id" INTO kelantan_id FROM "State" WHERE "Name" = 'Kelantan';
    SELECT "Id" INTO malacca_id FROM "State" WHERE "Name" = 'Malacca';
    SELECT "Id" INTO negeri_sembilan_id FROM "State" WHERE "Name" = 'Negeri Sembilan';
    SELECT "Id" INTO pahang_id FROM "State" WHERE "Name" = 'Pahang';
    SELECT "Id" INTO penang_id FROM "State" WHERE "Name" = 'Penang';
    SELECT "Id" INTO perak_id FROM "State" WHERE "Name" = 'Perak';
    SELECT "Id" INTO perlis_id FROM "State" WHERE "Name" = 'Perlis';
    SELECT "Id" INTO selangor_id FROM "State" WHERE "Name" = 'Selangor';
    SELECT "Id" INTO terengganu_id FROM "State" WHERE "Name" = 'Terengganu';
    SELECT "Id" INTO kuala_lumpur_id FROM "State" WHERE "Name" = 'Kuala Lumpur';
    SELECT "Id" INTO putrajaya_id FROM "State" WHERE "Name" = 'Putrajaya';
    SELECT "Id" INTO labuan_id FROM "State" WHERE "Name" = 'Labuan';
    SELECT "Id" INTO sabah_id FROM "State" WHERE "Name" = 'Sabah';
    SELECT "Id" INTO sarawak_id FROM "State" WHERE "Name" = 'Sarawak';
    
	-- Insert into RegionState
	-- Johor
	INSERT INTO "RegionState" ("Id", "RegionId", "StateId", "CreatedBy", "CreatedAt")
	VALUES
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Ayer Baloi'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Ayer Hitam'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Ayer Tawar 2'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Ayer Tawar 3'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Ayer Tawar 4'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Ayer Tawar 5'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bandar Penawar'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bandar Tenggara'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Batu Anam'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Batu Pahat'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bekok'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Benut'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bukit Gambir'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bukit Pasir'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Chaah'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Endau'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Gelang Patah'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Gerisek'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Gugusan Taib Andak'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Jementah'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Johor Bahru'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kahang'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kluang'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kota Tinggi'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kukup'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kulai'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Labis'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Layang-Layang'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Masai'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Mersing'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Muar'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Nusajaya'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Pagoh'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Paloh'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Panchor'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Parit Jawa'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Parit Raja'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Parit Sulong'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Pasir Gudang'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Pekan Nenas'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Pengerang'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Pontian'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Rengam'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Rengit'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Segamat'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Semerah'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Senai'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Senggarang'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Seri Gading'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Seri Medan'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Simpang Rengam'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Sri Gading'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Sri Medan'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Sungai Mati'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Tangkak'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Ulu Tiram'), johor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now());
	   
	-- Kedah
	INSERT INTO "RegionState" ("Id", "RegionId", "StateId", "CreatedBy", "CreatedAt")
	VALUES
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Alor Setar'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Alor Star'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Ayer Hitam'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Baling'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bandar Baharu'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bandar Bahru'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bedong'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bukit Kayu Hitam'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Changloon'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Gurun'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Jeniang'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Jitra'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Karangan'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kepala Batas'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kodiang'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kota Kuala Muda'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kota Sarang Semut'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kuala Kedah'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kuala Ketil'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kuala Nerang'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kuala Pegang'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kulim'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kupang'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Langgar'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Langkawi'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Lunas'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Merbok'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Padang Serai'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Pendang'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Pokok Sena'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Serdang'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Sik'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Simpang Empat'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Sungai Petani'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Universiti Utara Malaysia'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Yan'), kedah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now());
	
	-- Kelantan
	INSERT INTO "RegionState" ("Id", "RegionId", "StateId", "CreatedBy", "CreatedAt")
	VALUES
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Ayer Lanas'), kelantan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bachok'), kelantan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Cherang Ruku'), kelantan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Dabong'), kelantan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Gua Musang'), kelantan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Jeli'), kelantan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kem Desa Pahlawan'), kelantan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Ketereh'), kelantan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kota Bahru'), kelantan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kota Bharu'), kelantan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kuala Balah'), kelantan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kuala Krai'), kelantan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Machang'), kelantan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Melor'), kelantan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Pasir Mas'), kelantan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Pasir Puteh'), kelantan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Pulai Chondong'), kelantan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Rantau Panjang'), kelantan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Selising'), kelantan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Tanah Merah'), kelantan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Temangan'), kelantan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Tumpat'), kelantan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Wakaf Bharu'), kelantan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now());
	
	-- Malacca
	INSERT INTO "RegionState" ("Id", "RegionId", "StateId", "CreatedBy", "CreatedAt")
	VALUES
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Air Keroh'), malacca_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Alor Gajah'), malacca_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Asahan'), malacca_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Ayer Keroh'), malacca_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bemban'), malacca_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Durian Tunggal'), malacca_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kem Trendak'), malacca_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Masjid Tanah'), malacca_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Melaka'), malacca_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Merlimau'), malacca_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Selandar'), malacca_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Sungai Rambai'), malacca_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Sungai Udang'), malacca_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Tanjong Kling'), malacca_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now());
	
	-- Negeri Sembilan
	INSERT INTO "RegionState" ("Id", "RegionId", "StateId", "CreatedBy", "CreatedAt")
	VALUES
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bahau'), negeri_sembilan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bandar Baru Enstek'), negeri_sembilan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bandar Seri Jempol'), negeri_sembilan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Batu Kikir'), negeri_sembilan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Gemas'), negeri_sembilan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Gemencheh'), negeri_sembilan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Johol'), negeri_sembilan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kota'), negeri_sembilan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kuala Klawang'), negeri_sembilan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kuala Pilah'), negeri_sembilan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Labu'), negeri_sembilan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Linggi'), negeri_sembilan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Mantin'), negeri_sembilan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Nilai'), negeri_sembilan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Port Dickson'), negeri_sembilan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Pusat Bandar Palong'), negeri_sembilan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Rantau'), negeri_sembilan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Rembau'), negeri_sembilan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Rompin'), negeri_sembilan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Seremban'), negeri_sembilan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Si Rusa'), negeri_sembilan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Simpang Durian'), negeri_sembilan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Simpang Pertang'), negeri_sembilan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Tampin'), negeri_sembilan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Tanjong Ipoh'), negeri_sembilan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now());
	
	-- Pahang
	INSERT INTO "RegionState" ("Id", "RegionId", "StateId", "CreatedBy", "CreatedAt")
	VALUES
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Balok'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bandar Bera'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bandar Pusat Jengka'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bandar Tun Abdul Razak'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bentong'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Brinchang'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bukit Fraser'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bukit Goh'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Chenor'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Chini'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Damak'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Dong'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Gambang'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Genting Highlands'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Jaya Gading'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Jerantut'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Karak'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kemayan'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kuala Krau'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kuala Lipis'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kuala Rompin'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kuantan'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Lanchang'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Lurah Bilut'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Maran'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Mentakab'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Muadzam Shah'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Padang Tengku'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Raub'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Ringlet'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Sega'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Sungai Koyan'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Sungai Lembing'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Sungai Ruan'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Tanah Rata'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Temerloh'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Triang'), pahang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now());
	
	-- Penang
	INSERT INTO "RegionState" ("Id", "RegionId", "StateId", "CreatedBy", "CreatedAt")
	VALUES
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Ayer Itam'), penang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Balik Pulau'), penang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Batu Ferringhi'), penang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Batu Maung'), penang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bayan Lepas'), penang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bukit Mertajam'), penang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Butterworth'), penang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Gelugor'), penang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Jelutong'), penang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kepala Batas'), penang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kubang Semang'), penang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Nibong Tebal'), penang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Penaga'), penang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Penang Hill'), penang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Perai'), penang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Permatang Pauh'), penang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Pulau Pinang'), penang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Simpang Ampat'), penang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Sungai Jawi'), penang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Tanjong Bungah'), penang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Tanjung Bungah'), penang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Tasek Gelugor'), penang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Tasek Gelugur'), penang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Usm Pulau Pinang'), penang_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now());
	
	-- Perak
	INSERT INTO "RegionState" ("Id", "RegionId", "StateId", "CreatedBy", "CreatedAt")
	VALUES
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Ayer Tawar'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bagan Datoh'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bagan Serai'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bandar Seri Iskandar'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Batu Gajah'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Batu Kurau'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Behrang Stesen'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bidor'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bota'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bruas'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Changkat Jering'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Changkat Keruing'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Chemor'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Chenderiang'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Chenderong Balai'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Chikus'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Enggor'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Gerik'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Gopeng'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Hutan Melintang'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Intan'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Ipoh'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Jeram'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kampar'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kampung Gajah'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kampung Kepayang'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kamunting'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kuala Kangsar'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kuala Kurau'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kuala Sepetang'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Lambor Kanan'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Langkap'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Lenggong'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Lumut'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Malim Nawar'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Mambang Di Awan'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Manong'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Matang'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Padang Rengas'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Pangkor'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Pantai Remis'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Parit'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Parit Buntar'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Pengkalan Hulu'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Pusing'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Rantau Panjang'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Sauk'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Selama'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Selekoh'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Seri Manjong'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Seri Manjung'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Simpang'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Simpang Ampat Semanggol'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Sitiawan'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Slim River'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Sungai Siput'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Sungai Sumun'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Sungkai'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Taiping'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Tanah Rata'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Tanjong Malim'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Tanjong Piandang'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Tanjong Rambutan'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Tanjong Tualang'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Tapah'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Tapah Road'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Teluk Intan'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Temoh'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Tldm Lumut'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Trolak'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Trong'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Tronoh'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Ulu Bernam'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Ulu Kinta'), perak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now());
	
	-- Perlis
	INSERT INTO "RegionState" ("Id", "RegionId", "StateId", "CreatedBy", "CreatedAt")
	VALUES
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Arau'), perlis_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kaki Bukit'), perlis_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kangar'), perlis_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kuala Perlis'), perlis_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Padang Besar'), perlis_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Simpang Ampat'), perlis_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now());
	
	-- Selangor
	INSERT INTO "RegionState" ("Id", "RegionId", "StateId", "CreatedBy", "CreatedAt")
	VALUES
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Ampang'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bandar Baru Bangi'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bandar Puncak Alam'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bangi'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Banting'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Batang Berjuntai'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Batang Kali'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Batangkali'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Batu Arang'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Batu Caves'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Beranang'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bukit Rotan'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Cheras'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Cyberjaya'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Dengkil'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Gombak'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Hulu Langat'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Jenjarom'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Jeram'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Jerantut'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kajang'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kapar'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kerling'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Klang'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Klia'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kuala Kubu Baru'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kuala Selangor'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kuantan'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Pandan'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Pelabuhan Klang'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Petaling Jaya'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Pulau Carey'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Pulau Indah'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Pulau Ketam'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Rasa'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Rawang'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Sabak Bernam'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Sekinchan'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Semenyih'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Sepang'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Serdang'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Serendah'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Seri Kembangan'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Shah Alam'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Subang Airport'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Subang Jaya'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Sungai Ayer Tawar'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Sungai Besar'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Sungai Buloh'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Sungai Pelek'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Tanjong Karang'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Tanjong Sepat'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Telok Panglima Garang'), selangor_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now());
	
	-- Terengganu
	INSERT INTO "RegionState" ("Id", "RegionId", "StateId", "CreatedBy", "CreatedAt")
	VALUES
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Ajil'), terengganu_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Al Muktatfi Billah Shah'), terengganu_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Ayer Puteh'), terengganu_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bukit Besi'), terengganu_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bukit Payong'), terengganu_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Ceneh'), terengganu_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Chalok'), terengganu_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Cukai'), terengganu_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Dungun'), terengganu_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Jerteh'), terengganu_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kampung Raja'), terengganu_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kemasek'), terengganu_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kerteh'), terengganu_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Ketengah Jaya'), terengganu_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kijal'), terengganu_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kuala Berang'), terengganu_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kuala Besut'), terengganu_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kuala Terengganu'), terengganu_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Marang'), terengganu_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Paka'), terengganu_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Permaisuri'), terengganu_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Sungai Tong'), terengganu_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now());
	
	-- Kuala Lumpur
	INSERT INTO "RegionState" ("Id", "RegionId", "StateId", "CreatedBy", "CreatedAt")
	VALUES
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Batu Caves'), kuala_lumpur_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Cheras'), kuala_lumpur_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kuala Lumpur'), kuala_lumpur_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Setapak'), kuala_lumpur_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now());
	
	-- Putrajaya
	INSERT INTO "RegionState" ("Id", "RegionId", "StateId", "CreatedBy", "CreatedAt")
	VALUES
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Putrajaya'), putrajaya_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now());
	
	-- Labuan
	INSERT INTO "RegionState" ("Id", "RegionId", "StateId", "CreatedBy", "CreatedAt")
	VALUES
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Labuan'), labuan_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now());
	
	-- Sabah
	INSERT INTO "RegionState" ("Id", "RegionId", "StateId", "CreatedBy", "CreatedAt")
	VALUES
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Beaufort'), sabah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Beluran'), sabah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Beverly'), sabah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bongawan'), sabah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Inanam'), sabah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Keningau'), sabah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kota Belud'), sabah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kota Kinabalu'), sabah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kota Kinabatangan'), sabah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kota Marudu'), sabah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kuala Penyu'), sabah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kudat'), sabah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kunak'), sabah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Lahad Datu'), sabah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Likas'), sabah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Membakut'), sabah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Menumbok'), sabah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Nabawan'), sabah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Pamol'), sabah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Papar'), sabah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Penampang'), sabah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Putatan'), sabah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Ranau'), sabah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Sandakan'), sabah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Semporna'), sabah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Sipitang'), sabah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Tambunan'), sabah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Tamparuli'), sabah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Tanjung Aru'), sabah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Tawau'), sabah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Tenghilan'), sabah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Tenom'), sabah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Tuaran'), sabah_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now());
	
	-- Sarawak
	INSERT INTO "RegionState" ("Id", "RegionId", "StateId", "CreatedBy", "CreatedAt")
	VALUES
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Asajaya'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Balingian'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Baram'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bau'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bekenu'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Belaga'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Belawai'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Betong'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bintangor'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Bintulu'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Dalat'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Daro'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Debak'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Engkilili'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Julau'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kabong'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kanowit'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kapit'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kota Samarahan'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Kuching'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Lawas'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Limbang'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Lingga'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Long Lama'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Lubok Antu'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Lundu'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Lutong'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Matu'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Miri'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Mukah'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Nanga Medamit'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Niah'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Pusa'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Roban'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Saratok'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Sarikei'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Sebauh'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Sebuyau'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Serian'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Sibu'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Siburan'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Simunjan'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Song'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Spaoh'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Sri Aman'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Sundar'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now()),
	    ((SELECT uuid_in(overlay(overlay(md5(random()::text || ':' || random()::text) placing '4' from 13) placing to_hex(floor(random()*(11-8+1) + 8)::int)::text from 17)::cstring)), (SELECT "Id" FROM "Region" WHERE "Name" = 'Tatau'), sarawak_id, '7c255661-4d3a-4aa5-9a7d-6f8e44455f56', now());
 END $$;