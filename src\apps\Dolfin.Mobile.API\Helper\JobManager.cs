﻿using Dolfin.Mobile.API.Models.Request;
using Dolfin.Mobile.API.Scheduler;
using Hangfire;
using Hangfire.Server;
using Microsoft.Extensions.Logging;

namespace Dolfin.Mobile.API.Helper
{
    public static class JobManager
    {
        public static void RegisterHostedProcesses(this IServiceCollection services, IConfiguration configuration)
        {
            //Register Background Jobs
            services.RegisterBackgroundProcess(configuration.GetSection("BackgroundJob"));

            //Register Recurring Jobs
            services.RegisterRecurringProcess(configuration.GetSection("RecurringJob"));
        }

        private static void RegisterRecurringProcess(this IServiceCollection services, IConfigurationSection configuration)
        {
            //register all recurring jobs
            services.RegisterRecurringJob<RecurringSendEmailJob, RecurringSendEmailJobSetting>(configuration);
            //services.RegisterRecurringJob<RecurringSubmitDocumentJob, RecurringSubmitDocumentJobSetting>(configuration);
            //services.RegisterRecurringJob<RecurringCreateEInvoiceJob, RecurringCreateEInvoiceJobSetting>(configuration);
        }

        private static void RegisterBackgroundProcess(this IServiceCollection services, IConfigurationSection configuration)
        {
            //register all background processes
            services.RegisterBackgroundJob<SendEmailJob, SendEmailJobSetting>(configuration);
        }

        private static void RegisterRecurringJob<TJob, TSetting>(this IServiceCollection services, IConfigurationSection configSection)
            where TJob : BaseRecurringJob
            where TSetting : RecurringJobOption
        {
            var jobName = "";

            try
            {
                jobName = typeof(TJob).Name;
                var jobSetting = configSection.GetSection(jobName);

                // Check if the job setting section exists
                if (!jobSetting.Exists())
                {
                    Console.WriteLine($"Configuration for job {jobName} not found. Skipping registration.");
                    return;
                }

                var setting = jobSetting.Get<TSetting>();

                // Check if the setting was properly deserialized and is valid
                if (setting == null)
                {
                    Console.WriteLine($"Configuration for job {jobName} could not be deserialized. Skipping registration.");
                    return;
                }

                if (setting.IsValid())
                {
                    // Configure the job settings
                    services.Configure<TSetting>(jobSetting);

                    // Register the job as a singleton
                    services.AddSingleton(typeof(TJob));

                    // Register the job with Hangfire on application startup
                    services.AddTransient<IStartupFilter>(sp =>
                    {
                        var logger = sp.GetRequiredService<ILogger<TJob>>();
                        return new RecurringJobStartupFilter<TJob>(
                            jobName,
                            setting.CronExpression,
                            logger
                        );
                    });

                    Console.WriteLine($"Successfully registered recurring job {jobName}");
                }
                else
                {
                    Console.WriteLine($"Configuration for job {jobName} is invalid. Skipping registration.");
                }
            }
            catch (Exception ex)
            {
                // Log the exception
                Console.WriteLine($"Error registering recurring job {jobName}: {ex.Message}");
                throw new Exception($"Unable to register recurring Job {jobName}", ex);
            }
        }

        private static void RegisterJob<TJob>(string cronExpression) where TJob : BaseRecurringJob
        {
            try
            {
                RecurringJob.AddOrUpdate<TJob>(x => (x as BaseRecurringJob).Execute(), cronExpression, TimeZoneInfo.Local);
            }
            catch (Exception ex)
            {
                throw new Exception($"Unable to register recurring Job for {typeof(TJob).Name}", ex);
            }
        }

        private static void RegisterBackgroundJob<TJob, TSetting>(this IServiceCollection services, IConfigurationSection configSection)
            where TJob : IBackgroundProcess
            where TSetting : BackgroundJobOption
        {
            var jobName = "";
            try
            {
                jobName = typeof(TJob).Name;
                var jobSetting = configSection.GetSection(jobName);
                var setting = jobSetting.Get<TSetting>();
                if (setting.IsValid())
                {
                    services.Configure<TSetting>(jobSetting);
                    services.AddSingleton(typeof(IBackgroundProcess), typeof(TJob));
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Unable to register background Job {jobName}", ex);
            }
        }
    }
}
