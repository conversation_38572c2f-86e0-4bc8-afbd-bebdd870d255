﻿﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Framework.Data.Domains
{
    /// <summary>
    /// Entity for storing file upload metadata
    /// </summary>
    public partial class FileUpload : _BaseDomain
    {
        /// <summary>
        /// The company ID this file belongs to
        /// </summary>
        public Guid CompanyId { get; set; }
        
        /// <summary>
        /// The full URL/path to the file in S3
        /// </summary>
        public required string FileUrl { get; set; }
        
        /// <summary>
        /// The original filename
        /// </summary>
        public string? FileName { get; set; }
        
        /// <summary>
        /// The file's MIME type
        /// </summary>
        public string? MimeType { get; set; }
        
        /// <summary>
        /// The file size in bytes
        /// </summary>
        public long? Size { get; set; }
        
        /// <summary>
        /// The module this file belongs to (e.g., "product", "customer", etc.)
        /// </summary>
        public string Module { get; set; }
        
        /// <summary>
        /// Optional reference ID (e.g., product ID, customer ID)
        /// </summary>
        public Guid ReferenceId { get; set; }
        
        /// <summary>
        /// Optional description or notes about the file
        /// </summary>
        public string? Description { get; set; }
        
        /// <summary>
        /// Navigation property for the company
        /// </summary>
        public virtual Company? Company { get; set; }
    }
}
