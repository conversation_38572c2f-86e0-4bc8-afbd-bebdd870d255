﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Framework.Data.Domains
{
    public partial class ProductPrice : _BaseDomain
    {
        public ProductPrice() {
            TransactionItem = new HashSet<TransactionItem>();
        }
        public decimal FractionQty { get; set; } = 1.00m;
        public decimal Price { get; set; }
        public DateTime EffectiveAt { get; set; }
        public string? Remark { get; set; }
        public Guid ProductUOMId { get; set; }
        public virtual ProductUOM ProductUOM { get; set; }
        public virtual ICollection<TransactionItem> TransactionItem { get; }
    }
}
