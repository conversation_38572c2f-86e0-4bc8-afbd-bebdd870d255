using System;
using System.Collections.Generic;

namespace Dolfin.Mobile.API.Models.Dto
{
    /// <summary>
    /// DTO for LookupGroup entity
    /// </summary>
    public partial class LookupGroupDto
    {
        public LookupGroupDto()
        {
            LookupItems = new List<LookupItemDto>();
        }

        /// <summary>
        /// Unique identifier
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Unique code for the lookup group
        /// </summary>
        public required string Code { get; set; }

        /// <summary>
        /// Display name for the lookup group
        /// </summary>
        public required string Name { get; set; }

        /// <summary>
        /// Optional description for the lookup group
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Display order for the lookup group
        /// </summary>
        public int? DisplayOrder { get; set; }

        /// <summary>
        /// Indicates if the lookup group is system-defined (cannot be modified by users)
        /// </summary>
        public bool IsSystem { get; set; }

        /// <summary>
        /// Company ID if this lookup group is specific to a company, null if global
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// Collection of lookup items in this group
        /// </summary>
        public virtual List<LookupItemDto> LookupItems { get; set; }
    }
}
