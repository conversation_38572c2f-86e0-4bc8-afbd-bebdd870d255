﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Mobile.API.Models.Dto
{
    public partial class UOMCategoryDto
    {
        public UOMCategoryDto()
        {
            //ProductUOMPrimary = new HashSet<ProductUOM>();
            //ProductUOMSecondary = new HashSet<ProductUOM>();
        }
        public Guid Id { get; set; }
        public required string Code { get; set; }
        public required string Name { get; set; }
        public string? Description { get; set; }
        //public virtual ICollection<ProductUOM> ProductUOMPrimary { get; }
        //public virtual ICollection<ProductUOM> ProductUOMSecondary { get; }

    }
}
