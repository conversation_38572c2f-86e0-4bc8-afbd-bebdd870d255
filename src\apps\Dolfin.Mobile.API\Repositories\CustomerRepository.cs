using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Entity;
using Dolfin.Framework.Repository.Interfaces;
using Dolfin.Mobile.API.Constants;
using Dolfin.Mobile.API.Repositories.Interfaces;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Dolfin.Mobile.API.Repositories
{
    /// <summary>
    /// Repository implementation for Customer entity
    /// </summary>
    public class CustomerRepository : BaseRepository<Customer>, ICustomerRepository
    {
        /// <summary>
        /// Constructor
        /// </summary>
        public CustomerRepository(
            DolfinDbContext context,
            ICacheService cacheService,
            ILogger<CustomerRepository> logger)
            : base(context, cacheService, logger)
        {
        }

        /// <inheritdoc />
        public async Task<IEnumerable<Customer>> GetCustomersByCompanyAsync(Guid companyId)
        {
            string cacheKey = CacheKeys.Customer.GetCustomerListKey(companyId);

            return await GetOrCreateAsync<IEnumerable<Customer>>(
                cacheKey,
                async () => {
                    _logger.LogInformation("Loading customers for company {CompanyId} from database", companyId);
                    return await _dbSet
                        .Where(c => c.CompanyId == companyId && c.IsActive)
                        .Include(c => c.DebtorType)
                        .ToListAsync();
                });
        }

        /// <inheritdoc />
        public async Task<Customer> GetCustomerWithDetailsAsync(Guid customerId)
        {
            string cacheKey = CacheKeys.Customer.GetCustomerKey(customerId);

            return await GetOrCreateAsync<Customer>(
                cacheKey,
                async () => {
                    _logger.LogInformation("Loading customer {CustomerId} with details from database", customerId);
                    return await _dbSet
                        .Where(c => c.Id == customerId && c.IsActive)
                        .Include(c => c.DebtorType)
                        .Include(c => c.Company)
                        .Include(c => c.Address)
                        .FirstOrDefaultAsync();
                });
        }

        /// <inheritdoc />
        public void InvalidateCustomerCache(Guid customerId)
        {
            string cacheKey = CacheKeys.Customer.GetCustomerKey(customerId);
            InvalidateCache(cacheKey);
            _logger.LogInformation("Invalidated cache for customer {CustomerId}", customerId);
        }

        /// <inheritdoc />
        public void InvalidateCompanyCustomersCache(Guid companyId)
        {
            string cacheKey = CacheKeys.Customer.GetCustomerListKey(companyId);
            InvalidateCache(cacheKey);
            _logger.LogInformation("Invalidated cache for company {CompanyId} customers", companyId);
        }
    }
}
