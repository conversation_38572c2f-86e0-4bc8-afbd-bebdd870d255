using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Framework.Data.Domains
{
    public partial class Prefix : _BaseDomain
    {
        public required string TableName { get; set; }
        public required string PrefixValue { get; set; }
        public int LastNumber { get; set; }
        public int PaddingLength { get; set; } = 4;
        public Guid BranchId { get; set; }
        public virtual Branch Branch { get; set; }
    }
}
