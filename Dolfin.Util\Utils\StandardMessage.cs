﻿using Dolfin.Utility.Models;
using static Dolfin.Utility.Enum.Enums;

namespace Dolfin.Utility.Utils
{
    public class StandardMessage
    {
        public NoResultResponse ErrorMessage<TableEntity, ResponseEntity>(NoResultResponse response, StatusCode statusCode, string statusColumn = "", string statusMessage = "", Exception exception = null)
           where TableEntity : class where ResponseEntity : class
        {
            statusMessage = GetStatusMessage<TableEntity>(statusCode, statusColumn, statusMessage);

            response.IsSuccessful = false;
            response.StatusCode = (int)statusCode;
            response.StatusMessage = statusMessage;
            response.Exception = exception?.ToString();
            return response;
        }

        public BaseResponse<ResponseEntity> ErrorMessage<TableEntity, ResponseEntity>(BaseResponse<ResponseEntity> response, StatusCode statusCode, string statusColumn = "", string statusMessage = "", Exception exception = null)
            where TableEntity : class where ResponseEntity : class
        {
            statusMessage = GetStatusMessage<TableEntity>(statusCode, statusColumn, statusMessage);

            response.IsSuccessful = false;
            response.StatusCode = (int)statusCode;
            response.StatusMessage = statusMessage;
            response.Exception = exception?.ToString();
            return response;
        }

        private string GetStatusMessage<TableEntity>(StatusCode statusCode, string statusColumn = "", string statusMessage = "") where TableEntity : class
        {
            if (statusMessage == null || statusMessage == "")
            {
                statusMessage = statusCode.GetDescription();
            }

            if (statusColumn == null || statusColumn == "")
            {
                statusColumn = typeof(TableEntity).Name;
            }

            return statusMessage.Replace("{0}", statusColumn);
        }

    }
}
