﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Framework.Data.Domains
{
    public partial class AccountGroup : _BaseDomain
    {
        public AccountGroup()
        {
            Customer = new HashSet<Customer>();
            PaymentType = new HashSet<PaymentType>();
            Transaction = new HashSet<Transaction>();
        }
        public required string Code { get; set; }
        public required string Name { get; set; }
        public required string DisplayName { get; set; }
        public virtual ICollection<Customer> Customer { get; }
        public virtual ICollection<PaymentType> PaymentType { get; }
        public virtual ICollection<Transaction> Transaction { get; }

    }
}
