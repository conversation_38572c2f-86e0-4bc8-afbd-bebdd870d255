﻿using Microsoft.Extensions.Hosting;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Framework.Data.Domains
{
    public partial class AccountGroupByPeriod : _BaseDomain
    {
        public required string Code { get; set; }
        public required string Name { get; set; }
        public int Year { get; set; }
        public int Month { get; set; }
        public bool Locked { get; set; }
        public Guid BranchId { get; set; }
        public Guid TransactionTypeId { get; set; }
        public virtual Branch? Branch { get; set; }
        public virtual TransactionType? TransactionType { get; set; }

    }
}
