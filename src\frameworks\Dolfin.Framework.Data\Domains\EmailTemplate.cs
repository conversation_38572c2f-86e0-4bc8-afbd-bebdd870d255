﻿using Dolfin.Framework.Data.Domains;
using System;
using System.Collections.Generic;

namespace Dolfin.Mobile.API.Models
{
    public partial class EmailTemplate : _BaseDomain
    {
        public string Name { get; set; }
        public string BccEmailAddresses { get; set; }
        public string Subject { get; set; }
        public string Body { get; set; }
        public Guid EmailAccountId { get; set; }

        public virtual EmailAccount EmailAccount { get; set; }
    }
}
