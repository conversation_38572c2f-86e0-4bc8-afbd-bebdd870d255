﻿using Dolfin.Mobile.API.Models;
using MailKit.Search;
using MailKit;
using Microsoft.Extensions.Options;
using MimeKit;
using MailKit.Net.Imap;
using MailKit.Net.Pop3;
using MailKit.Net.Smtp;

namespace Dolfin.Mobile.API.Connector
{

    public class EmailConnector : IEmailConnector
    {
        //private readonly EmailSettings _emailSettings;
        private readonly ILogger _logger;

        public EmailConnector(IOptions<EmailSettings> emailSettings, ILoggerFactory loggerFactory)
        {
            //_emailSettings = emailSettings.Value;
            _logger = loggerFactory.CreateLogger<EmailConnector>();
        }

        public async Task<List<EmailMessage>> ReceiveAsnycAll(EmailAccount emailAccount)
        {
            List<EmailMessage> messages;

            try
            {
                if (emailAccount.Protocol == "IMAP")
                {
                    messages = await GetEmailMessagesImapClient(true, default(DateTime), emailAccount);
                }
                else
                {
                    messages = await GetEmailMessagesPop3Client(true, default(DateTime), emailAccount);
                }

                return messages;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error when receive all messages from mail server.");
                throw;
            }
        }

        public async Task<List<EmailMessage>> ReceiveAsnycSince(DateTime startDateTime, EmailAccount emailAccount)
        {
            List<EmailMessage> messages;

            try
            {
                if (emailAccount.Protocol == "IMAP")
                {
                    messages = await GetEmailMessagesImapClient(false, startDateTime, emailAccount);
                }
                else
                {
                    messages = await GetEmailMessagesPop3Client(false, startDateTime, emailAccount);
                }

                return messages.Where(m => m.Date >= startDateTime).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error when receive messages from mail server delivered after: {0}.", startDateTime);
                throw;
            }
        }

        public void Send(EmailMessage emailMessage, EmailAccount emailAccount)
        {
            var message = new MimeMessage();
            message.To.AddRange(emailMessage.ToAddresses.Select(x => new MailboxAddress(x.Name, x.Address)));
            message.From.AddRange(emailMessage.FromAddresses.Select(x => new MailboxAddress(x.Name, x.Address)));

            if (emailMessage.CCAddresses.Count > 0)
            {
                message.Cc.AddRange(emailMessage.CCAddresses.Select(x => new MailboxAddress(x.Name, x.Address)));
            }

            if (emailMessage.BCCAddresses.Count > 0)
            {
                message.Bcc.AddRange(emailMessage.BCCAddresses.Select(x => new MailboxAddress(x.Name, x.Address)));
            }

            message.Subject = emailMessage.Subject;

            var builder = new BodyBuilder { HtmlBody = emailMessage.Content };

            if (emailMessage.Attachments.Count > 0)
            {
                for (int i = 0; i < emailMessage.Attachments.Count; i++)
                {
                    builder.Attachments.Add(emailMessage.Attachments[i].Filename, emailMessage.Attachments[i].Data);
                }
            }

            message.Body = builder.ToMessageBody();

            try
            {
                SendMimeMessage(message, emailAccount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error when sending email.");
                throw;

            }
        }

        #region Private Method
        private async Task<List<EmailMessage>> GetEmailMessagesPop3Client(bool isSelectAll, DateTime startDateTime, EmailAccount emailAccount)
        {
            List<EmailMessage> messages = new List<EmailMessage>();

            _logger.LogInformation("## Using POP3 Client ##");
            using (var client = new Pop3Client())
            {
                // For demo-purposes, accept all SSL certificates (in case the server supports STARTTLS)
                client.ServerCertificateValidationCallback = (s, c, h, e) => true;

                _logger.LogInformation("Connecting to mail server: {0} at port: {1} with ssl enabled: {2}", emailAccount.Host, emailAccount.Port, emailAccount.EnableSsl);
                await client.ConnectAsync(emailAccount.Host, emailAccount.Port, emailAccount.EnableSsl);
                _logger.LogInformation("Successfully connected to mail server.");
                _logger.LogInformation("Authenticating with mail server at mailbox: {0}", emailAccount.Username);
                await client.AuthenticateAsync(emailAccount.Username, emailAccount.Password);
                _logger.LogInformation("Successfully authenticated with mail server.");

                _logger.LogInformation("Begin downloading messages from server.");
                for (int i = 0; i < client.Count; i++)
                {
                    var mimeMessage = await client.GetMessageAsync(i);

                    if (isSelectAll)
                    {
                        messages.Add(ParseMimeMessage(mimeMessage));
                    }
                    else
                    {
                        if (mimeMessage.Date >= startDateTime)
                        {
                            messages.Add(ParseMimeMessage(mimeMessage));
                        }
                        else
                        {
                            break;
                        }
                    }

                }
                _logger.LogInformation("Successfully downloaded number of message: {0}.", messages.Count);

                await client.DisconnectAsync(true);
                _logger.LogInformation("Disconnected from mail server.");
            }

            return messages;
        }

        private async Task<List<EmailMessage>> GetEmailMessagesImapClient(bool isSelectAll, DateTime startDateTime, EmailAccount emailAccount)
        {
            List<EmailMessage> messages = new List<EmailMessage>();

            _logger.LogInformation("## Using IMAP Client ##");
            using (var client = new ImapClient())
            {
                // For demo-purposes, accept all SSL certificates (in case the server supports STARTTLS)
                client.ServerCertificateValidationCallback = (s, c, h, e) => true;

                _logger.LogInformation("Connecting to mail server: {0} at port: {1} with ssl enabled: {2}", emailAccount.Host, emailAccount.Port, emailAccount.EnableSsl);
                await client.ConnectAsync(emailAccount.Host, emailAccount.Port, emailAccount.EnableSsl);
                _logger.LogInformation("Successfully connected to mail server.");
                _logger.LogInformation("Authenticating with mail server at mailbox: {0}", emailAccount.Username);
                client.AuthenticationMechanisms.Remove("XOAUTH2");
                await client.AuthenticateAsync(emailAccount.Username, emailAccount.Password);
                _logger.LogInformation("Successfully authenticated with mail server.");

                // The Inbox folder is always available on all IMAP servers...
                var inbox = client.Inbox;
                await inbox.OpenAsync(FolderAccess.ReadOnly);


                _logger.LogInformation("Begin downloading messages from server.");

                if (isSelectAll)
                {
                    for (int i = 0; i < inbox.Count; i++)
                    {
                        var mimeMessage = inbox.GetMessage(i);

                        messages.Add(ParseMimeMessage(mimeMessage));
                    }
                }
                else
                {
                    var query = SearchQuery.DeliveredAfter(startDateTime);

                    foreach (var uid in await inbox.SearchAsync(query))
                    {
                        var mimeMessage = await inbox.GetMessageAsync(uid);

                        messages.Add(ParseMimeMessage(mimeMessage));
                    }
                }
                _logger.LogInformation("Successfully downloaded number of message: {0}.", messages.Count);

                await client.DisconnectAsync(true);
                _logger.LogInformation("Disconnected from mail server.");
            }

            return messages;
        }

        private EmailMessage ParseMimeMessage(MimeMessage message)
        {
            EmailMessage m = new EmailMessage();

            m.FromAddresses.AddRange(message.From.Mailboxes.Select(x => new EmailAddress() { Name = x.Name, Address = x.Address }));
            m.ToAddresses.AddRange(message.To.Mailboxes.Select(x => new EmailAddress() { Name = x.Name, Address = x.Address }));
            m.CCAddresses.AddRange(message.Cc.Mailboxes.Select(x => new EmailAddress() { Name = x.Name, Address = x.Address }));
            m.Date = message.Date.ToUniversalTime().UtcDateTime;
            m.Content = message.HtmlBody;

            foreach (var attachment in message.Attachments)
            {
                EmailAttachment ea = new EmailAttachment();

                if (attachment is MimePart mimePart)
                {
                    using (var memory = new MemoryStream())
                    {

                        mimePart.Content.DecodeTo(memory);
                        ea.Filename = mimePart.FileName;
                        ea.Data = memory.ToArray();

                        m.Attachments.Add(ea);

                    }
                }
                // Ignore Message part of the moment.
                //else
                //{
                //    MessagePart messagePart = ((MessagePart)attachment);
                //    messagePart.Message.WriteTo(memory);

                //    ea.Filename = messagePart.ContentType.;
                //}

            }

            return m;
        }

        private void SendMimeMessage(MimeMessage message, EmailAccount emailAccount)
        {
            //Be careful that the SmtpClient class is the one from Mailkit not the framework!
            _logger.LogInformation("## Using SMTP Client ##");
            using (var emailClient = new SmtpClient())
            {
                _logger.LogInformation("Connecting to mail server: {0} at port: {1} with ssl enabled: {2}", emailAccount.Host, emailAccount.Port, emailAccount.EnableSsl);
                var sslOption = emailAccount.EnableSsl
                    //? MailKit.Security.SecureSocketOptions.SslOnConnect // Only works on port 465
                    ? MailKit.Security.SecureSocketOptions.StartTls  // Use StartTls for port 587
                    : MailKit.Security.SecureSocketOptions.None;
                emailClient.Connect(emailAccount.Host, emailAccount.Port, sslOption);

                //Remove any OAuth functionality as we won't be using it. 
                //emailClient.AuthenticationMechanisms.Remove("XOAUTH2");
                _logger.LogInformation("Successfully connected to mail server.");
                _logger.LogInformation("Authenticating with mail server at mailbox: {0}", emailAccount.Username);
                emailClient.Authenticate(emailAccount.Username, emailAccount.Password);
                _logger.LogInformation("Successfully authenticated with mail server.");

                emailClient.Send(message);
                _logger.LogInformation("Successfully sent the email: " + message.Subject);

                emailClient.Disconnect(true);
            }
        }
        #endregion
    }
}
