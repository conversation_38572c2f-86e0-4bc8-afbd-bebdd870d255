﻿using Dolfin.Framework.Data.Domains.CustomIdentity;
using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Framework.Data.Domains
{
    public partial class Subscription : _BaseDomain
    {
        public Subscription()
        {
            Company = new HashSet<Company>();
        }
        public required string Code { get; set; }
        public required string Name { get; set; }
        public required string Description { get; set; }
        public required int Months { get; set; }
        public int? DisplayOrder { get; set; }
        public bool Published { get; set; }
        public virtual ICollection<Company> Company { get; }
    }
}
