﻿using Dolfin.Framework.Data.Domains.CustomIdentity;
using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Framework.Data.Domains
{
    public partial class Transaction : _BaseDomain
    {
        public Transaction()
        {
            TransactionItem = new HashSet<TransactionItem>();
            TransactionPaid = new HashSet<TransactionPaid>();
			EInvoice = new HashSet<EInvoice>();
		}
        public required string TrxNo { get; set; }
        public DateTime TrxDatetime { get; set; }
        public required string CurrencyCode { get; set; }
        public decimal ExchangeRate { get; set; }
        public decimal TotalRoundingAdjustmentAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal TotalAmountWOTax { get; set; }
        public decimal TotalDiscount { get; set; }
        public decimal TotalExclTaxAmount { get; set; }
        public decimal TotalInclTaxAmount { get; set; }
        public decimal TotalSalesTaxAmount { get; set; }
        public decimal TotalServiceTaxAmount { get; set; }
        public decimal SalesTaxRate { get; set; }
        public decimal ServiceTaxRate { get; set; }
        public Guid? SalesTaxNoId { get; set; }
        public Guid? ServiceTaxNoId { get; set; }
        public decimal TotalPayableAmount { get; set; }
        public required string PIC { get; set; }
        public string TermDay { get; set; }
        public Guid? TermId { get; set; }
        public Guid? AccountGroupId { get; set; }
        public Guid TransactionTypeId { get; set; }
        //public Guid CompanyId { get; set; }
        public Guid BranchId { get; set; }
        public Guid CustomerId { get; set; }
        public string UserId { get; set; }
        public Guid ShippingAddressId { get; set; }
        public Guid BillingAddressId { get; set; }
        public Guid TransactionStatusId { get; set; }
        public bool? IsEInvoiceCreated { get; set; }
        public virtual TaxRate? SalesTaxNo { get; set; }
        public virtual TaxRate? ServiceTaxNo { get; set; }
        public virtual Term? Term { get; set; }
        public virtual AccountGroup? AccountGroup { get; set; }
        public virtual TransactionType? TransactionType { get; set; }
        //public virtual Company? Company { get; set; }
        public virtual Branch? Branch { get; set; }
        public virtual Customer? Customer { get; set; }
        public virtual ApplicationUser? User { get; set; }
        public virtual Address? ShippingAddress { get; set; }
        public virtual Address? BillingAddress { get; set; }
        public virtual TransactionStatus? TransactionStatus { get; set; }
        public virtual ICollection<TransactionItem> TransactionItem { get; }
        public virtual ICollection<TransactionPaid> TransactionPaid { get; }
		public virtual ICollection<EInvoice> EInvoice { get; }
	}
}
