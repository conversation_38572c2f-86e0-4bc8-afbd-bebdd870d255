﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Framework.Data.Domains
{
    public partial class TransactionStatus : _BaseDomain
    {
        public TransactionStatus()
        {
            Transaction = new HashSet<Transaction>();
        }
        public required string Code { get; set; }
        public required string Name { get; set; }
        public virtual ICollection<Transaction> Transaction { get; }
    }
}
