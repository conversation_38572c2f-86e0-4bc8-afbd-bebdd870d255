using System.Xml.Serialization;
using System.Collections.Generic;
using Dolfin.Utility.Models.UBLInvoice;

namespace Dolfin.Mobile.API.Models
{
    // Extension class for Invoice to add missing properties
    public partial class InvoiceExtensions
    {
        // These extension properties are used to extend the Invoice class from the UBLInvoice model
        
        // Add TaxTotal property to Invoice
        public static TaxTotal GetTaxTotal(this Invoice invoice)
        {
            return invoice.GetType().GetProperty("TaxTotal")?.GetValue(invoice) as TaxTotal;
        }
        
        public static void SetTaxTotal(this Invoice invoice, TaxTotal taxTotal)
        {
            var property = invoice.GetType().GetProperty("TaxTotal");
            if (property != null)
            {
                property.SetValue(invoice, taxTotal);
            }
        }
        
        // Add LegalMonetaryTotal property to Invoice
        public static LegalMonetaryTotal GetLegalMonetaryTotal(this Invoice invoice)
        {
            return invoice.GetType().GetProperty("LegalMonetaryTotal")?.GetValue(invoice) as LegalMonetaryTotal;
        }
        
        public static void SetLegalMonetaryTotal(this Invoice invoice, LegalMonetaryTotal legalMonetaryTotal)
        {
            var property = invoice.GetType().GetProperty("LegalMonetaryTotal");
            if (property != null)
            {
                property.SetValue(invoice, legalMonetaryTotal);
            }
        }
        
        // Add InvoiceLines property to Invoice
        public static List<InvoiceLine> GetInvoiceLines(this Invoice invoice)
        {
            return invoice.GetType().GetProperty("InvoiceLines")?.GetValue(invoice) as List<InvoiceLine>;
        }
        
        public static void SetInvoiceLines(this Invoice invoice, List<InvoiceLine> invoiceLines)
        {
            var property = invoice.GetType().GetProperty("InvoiceLines");
            if (property != null)
            {
                property.SetValue(invoice, invoiceLines);
            }
        }
    }
}
