﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Framework.Data.Domains
{
    public partial class TaxRate : _BaseDomain
    {
        public TaxRate()
        {
            CompanyDefaultSalesTaxNo = new HashSet<Company>();
            CompanyDefaultServiceTaxNo = new HashSet<Company>();
            TransactionSalesTaxNo = new HashSet<Transaction>();
            TransactionServiceTaxNo = new HashSet<Transaction>();
            TransactionItemSalesTaxNo = new HashSet<TransactionItem>();
            TransactionItemServiceTaxNo = new HashSet<TransactionItem>();
            ProductCustomSalesTaxNo = new HashSet<Product>();
            ProductCustomServiceTaxNo = new HashSet<Product>();
        }
        public required string Code { get; set; }
        public required string Name { get; set; }
        public string? Description { get; set; }
        public int ChargePercentage { get; set; }
        public Guid TaxCategoryId { get; set; }
        public virtual TaxCategories? TaxCategory { get; set; }
        public virtual ICollection<Company> CompanyDefaultSalesTaxNo { get; }
        public virtual ICollection<Company> CompanyDefaultServiceTaxNo { get; }
        public virtual ICollection<Transaction> TransactionSalesTaxNo { get; }
        public virtual ICollection<Transaction> TransactionServiceTaxNo { get; }
        public virtual ICollection<TransactionItem> TransactionItemSalesTaxNo { get; }
        public virtual ICollection<TransactionItem> TransactionItemServiceTaxNo { get; }
        public virtual ICollection<Product> ProductCustomSalesTaxNo { get; }
        public virtual ICollection<Product> ProductCustomServiceTaxNo { get; }

    }
}
