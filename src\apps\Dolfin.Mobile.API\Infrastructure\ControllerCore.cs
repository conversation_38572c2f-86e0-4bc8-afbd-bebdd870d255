﻿using System;
using System.Collections.Generic;
using System.Text;
using Microsoft.AspNetCore.Mvc;
using AutoMapper;
using System.Security.Claims;
using Newtonsoft.Json;
using Dolfin.Utility.Enum;
using Dolfin.Utility.Models;

namespace Dolfin.Mobile.API.Infrastructure
{
    public class ControllerCore : ControllerBase
    {
        [NonAction]
        public IActionResult ActionResultResponse<TO, FROM>(IMapper _mapper, BaseResponse<FROM> result, Pagination pagination = null, object metadataHeader = null)
        where TO : class where FROM : class
        {
            if (result.IsSuccessful)
            {
                if (pagination != null)
                {
                    Response.Headers.Add("X-Pagination", JsonConvert.SerializeObject(metadataHeader));
                }

                return Ok(_mapper.Map<BaseResponse<TO>>(result));
            }

            NoResultResponse noResultResponse;
            try
            {
                noResultResponse = _mapper.Map<NoResultResponse>(result);
            }
            catch (AutoMapper.AutoMapperMappingException)
            {
                // If mapping fails, create a new NoResultResponse manually
                noResultResponse = new NoResultResponse
                {
                    IsSuccessful = result.IsSuccessful,
                    StatusCode = result.StatusCode,
                    StatusMessage = result.StatusMessage,
                    Exception = result.Exception,
                    Result = new NoResult()
                };
            }

            // Only debug mode return exception
#if !DEBUG
            noResultResponse.Exception = null;
#endif

            if (noResultResponse.StatusCode != null)
            {
                return StatusCode((int)noResultResponse.StatusCode, noResultResponse);
            }
            else
            {
                return BadRequest(noResultResponse);
            }
        }

        [NonAction]
        public IActionResult IncompleteDataResultResponse()
        {
            var response = new NoResultResponse
            {
                IsSuccessful = false,
                StatusCode = (int)Enums.StatusCode.BadRequest,
                StatusMessage = "Incomplete data"
            };

            return BadRequest(response);
        }

    }
}
