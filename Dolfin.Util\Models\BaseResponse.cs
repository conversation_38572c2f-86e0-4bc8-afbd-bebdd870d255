﻿using System;

namespace Dolfin.Utility.Models
{
    public class BaseResponse<T> where T : class
    {
        public bool IsSuccessful { get; set; }
        public int? StatusCode { get; set; }
        public string StatusMessage { get; set; }
        public T Result { get; set; }
        public string Exception { get; set; }
    }

    public class ResultId
    {
        public Guid? Id { get; set; }
    }

    public class ResultMessage
    {
        public string DisplayMessage { get; set; }
    }


    public class NoResultResponse : BaseResponse<NoResult>
    { }

    public class NoResult
    { }

    public static class ConvertResponse
    {
        public static BaseResponse<NoResultResponse> ToNoResultResponse<T>(BaseResponse<T> response) where T : class
        {
            return new BaseResponse<NoResultResponse>
            {
                IsSuccessful = response.IsSuccessful,
                StatusCode = response.StatusCode,
                StatusMessage = response.StatusMessage,
                Exception = response.Exception
            };
        }

        public static BaseResponse<TO> ToOtherResultResponse<TO, FROM>(BaseResponse<FROM> response) 
            where FROM : class
            where TO : class
        {
            return new BaseResponse<TO>
            {
                IsSuccessful = response.IsSuccessful,
                StatusCode = response.StatusCode,
                StatusMessage = response.StatusMessage,
                Exception = response.Exception
            };
        }
    }
}
