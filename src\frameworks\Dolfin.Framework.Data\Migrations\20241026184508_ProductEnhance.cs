﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace Dolfin.Framework.Data.Migrations
{
    /// <inheritdoc />
    public partial class ProductEnhance : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Product_CompanyId",
                table: "Product");

            migrationBuilder.DeleteData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("aef630d3-6f0b-4c1f-a17b-c60f8f0408ea"));

            migrationBuilder.DeleteData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("b3ec5d2b-ca0d-45bc-ba86-bc965852922c"));

            migrationBuilder.DeleteData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("c0a87fee-d2a5-4c7e-beee-e60c7936f056"));

            migrationBuilder.DeleteData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("dbd5a6a5-0995-4ab6-822f-559ec36d222e"));

            migrationBuilder.RenameColumn(
                name: "CustomServiceTaxNo",
                table: "Product",
                newName: "CustomServiceTaxNoId");

            migrationBuilder.RenameColumn(
                name: "CustomSalesTaxNo",
                table: "Product",
                newName: "CustomSalesTaxNoId");

            migrationBuilder.AddColumn<Guid>(
                name: "CompanyId",
                table: "ProductCategory",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AddColumn<int>(
                name: "StockMethod",
                table: "Inventory",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<Guid>(
                name: "CompanyId",
                table: "Branch",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.UpdateData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("69ed35dc-8775-4641-9f1d-26d9d91e5f4b"),
                column: "CreatedAt",
                value: new DateTime(2024, 10, 26, 18, 45, 7, 717, DateTimeKind.Utc).AddTicks(3494));

            migrationBuilder.InsertData(
                table: "ProductCostMethod",
                columns: new[] { "Id", "Code", "CreatedAt", "CreatedBy", "Description", "IsActive", "Name", "UpdatedAt", "UpdatedBy" },
                values: new object[,]
                {
                    { new Guid("11fd5e73-51fa-428a-a919-960ee860b00f"), "WA", new DateTime(2024, 10, 26, 18, 45, 7, 717, DateTimeKind.Utc).AddTicks(3663), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), null, true, "Weighted Average", null, null },
                    { new Guid("5d22874b-5834-40a2-81be-bf5100eb845d"), "FILO", new DateTime(2024, 10, 26, 18, 45, 7, 717, DateTimeKind.Utc).AddTicks(3657), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), null, true, "First In, Last Out", null, null },
                    { new Guid("c219bf33-28d0-4595-a25b-8018bd87e67a"), "FIFO", new DateTime(2024, 10, 26, 18, 45, 7, 717, DateTimeKind.Utc).AddTicks(3655), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), null, true, "First In, First Out", null, null },
                    { new Guid("e57e65d0-85a9-49c8-8463-3424a42e1f01"), "LIFO", new DateTime(2024, 10, 26, 18, 45, 7, 717, DateTimeKind.Utc).AddTicks(3660), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), null, true, "Last In, First Out", null, null }
                });

            migrationBuilder.UpdateData(
                table: "Settings",
                keyColumn: "Id",
                keyValue: new Guid("929c9070-55d2-4b9d-832d-84f6e8bda3cd"),
                column: "CreatedBy",
                value: new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"),
                column: "CreatedAt",
                value: new DateTime(2024, 10, 26, 18, 45, 7, 717, DateTimeKind.Utc).AddTicks(3559));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("81ba4893-d891-4e3c-a5e7-ba13c697bf97"),
                column: "CreatedAt",
                value: new DateTime(2024, 10, 26, 18, 45, 7, 717, DateTimeKind.Utc).AddTicks(3567));

            migrationBuilder.UpdateData(
                table: "TaxRate",
                keyColumn: "Id",
                keyValue: new Guid("59bb4a30-7cae-47ab-800d-a117e0134ac5"),
                column: "CreatedAt",
                value: new DateTime(2024, 10, 26, 18, 45, 7, 717, DateTimeKind.Utc).AddTicks(3622));

            migrationBuilder.CreateIndex(
                name: "IX_ProductCategory_Company_Code_Unique",
                table: "ProductCategory",
                columns: new[] { "CompanyId", "Code" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Product_Company_Code_Unique",
                table: "Product",
                columns: new[] { "CompanyId", "Code" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Product_Company_Sku_Unique",
                table: "Product",
                columns: new[] { "CompanyId", "Sku" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Product_CustomSalesTaxNoId",
                table: "Product",
                column: "CustomSalesTaxNoId");

            migrationBuilder.CreateIndex(
                name: "IX_Product_CustomServiceTaxNoId",
                table: "Product",
                column: "CustomServiceTaxNoId");

            migrationBuilder.CreateIndex(
                name: "IX_Branch_CompanyId",
                table: "Branch",
                column: "CompanyId");

            migrationBuilder.AddForeignKey(
                name: "FK_Branch_Company",
                table: "Branch",
                column: "CompanyId",
                principalTable: "Company",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Product_CustomSalesTaxNo",
                table: "Product",
                column: "CustomSalesTaxNoId",
                principalTable: "TaxRate",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Product_CustomServiceTaxNo",
                table: "Product",
                column: "CustomServiceTaxNoId",
                principalTable: "TaxRate",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_ProductCategory_Company",
                table: "ProductCategory",
                column: "CompanyId",
                principalTable: "Company",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Branch_Company",
                table: "Branch");

            migrationBuilder.DropForeignKey(
                name: "FK_Product_CustomSalesTaxNo",
                table: "Product");

            migrationBuilder.DropForeignKey(
                name: "FK_Product_CustomServiceTaxNo",
                table: "Product");

            migrationBuilder.DropForeignKey(
                name: "FK_ProductCategory_Company",
                table: "ProductCategory");

            migrationBuilder.DropIndex(
                name: "IX_ProductCategory_Company_Code_Unique",
                table: "ProductCategory");

            migrationBuilder.DropIndex(
                name: "IX_Product_Company_Code_Unique",
                table: "Product");

            migrationBuilder.DropIndex(
                name: "IX_Product_Company_Sku_Unique",
                table: "Product");

            migrationBuilder.DropIndex(
                name: "IX_Product_CustomSalesTaxNoId",
                table: "Product");

            migrationBuilder.DropIndex(
                name: "IX_Product_CustomServiceTaxNoId",
                table: "Product");

            migrationBuilder.DropIndex(
                name: "IX_Branch_CompanyId",
                table: "Branch");

            migrationBuilder.DeleteData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("11fd5e73-51fa-428a-a919-960ee860b00f"));

            migrationBuilder.DeleteData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("5d22874b-5834-40a2-81be-bf5100eb845d"));

            migrationBuilder.DeleteData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("c219bf33-28d0-4595-a25b-8018bd87e67a"));

            migrationBuilder.DeleteData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("e57e65d0-85a9-49c8-8463-3424a42e1f01"));

            migrationBuilder.DropColumn(
                name: "CompanyId",
                table: "ProductCategory");

            migrationBuilder.DropColumn(
                name: "StockMethod",
                table: "Inventory");

            migrationBuilder.DropColumn(
                name: "CompanyId",
                table: "Branch");

            migrationBuilder.RenameColumn(
                name: "CustomServiceTaxNoId",
                table: "Product",
                newName: "CustomServiceTaxNo");

            migrationBuilder.RenameColumn(
                name: "CustomSalesTaxNoId",
                table: "Product",
                newName: "CustomSalesTaxNo");

            migrationBuilder.UpdateData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("69ed35dc-8775-4641-9f1d-26d9d91e5f4b"),
                column: "CreatedAt",
                value: new DateTime(2024, 10, 13, 16, 8, 28, 958, DateTimeKind.Utc).AddTicks(5672));

            migrationBuilder.InsertData(
                table: "Role",
                columns: new[] { "Id", "Code", "CreatedAt", "CreatedBy", "Editable", "IsActive", "Name", "PartailEditable", "TargetType", "UpdatedAt", "UpdatedBy" },
                values: new object[,]
                {
                    { new Guid("aef630d3-6f0b-4c1f-a17b-c60f8f0408ea"), "1", new DateTime(2024, 10, 13, 16, 8, 28, 958, DateTimeKind.Utc).AddTicks(4342), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), true, true, "Admin", false, "APP", null, null },
                    { new Guid("b3ec5d2b-ca0d-45bc-ba86-bc965852922c"), "3", new DateTime(2024, 10, 13, 16, 8, 28, 958, DateTimeKind.Utc).AddTicks(5142), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), true, true, "Super Admin", false, "SYSADMIN", null, null },
                    { new Guid("c0a87fee-d2a5-4c7e-beee-e60c7936f056"), "2", new DateTime(2024, 10, 13, 16, 8, 28, 958, DateTimeKind.Utc).AddTicks(4760), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), true, true, "Super User", false, "ADMIN", null, null }
                });

            migrationBuilder.InsertData(
                table: "Role",
                columns: new[] { "Id", "Code", "CreatedAt", "CreatedBy", "IsActive", "Name", "PartailEditable", "TargetType", "UpdatedAt", "UpdatedBy" },
                values: new object[] { new Guid("dbd5a6a5-0995-4ab6-822f-559ec36d222e"), "4", new DateTime(2024, 10, 13, 16, 8, 28, 958, DateTimeKind.Utc).AddTicks(5577), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), true, "User", false, "USER", null, null });

            migrationBuilder.UpdateData(
                table: "Settings",
                keyColumn: "Id",
                keyValue: new Guid("929c9070-55d2-4b9d-832d-84f6e8bda3cd"),
                column: "CreatedBy",
                value: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"),
                column: "CreatedAt",
                value: new DateTime(2024, 10, 13, 16, 8, 28, 958, DateTimeKind.Utc).AddTicks(5747));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("81ba4893-d891-4e3c-a5e7-ba13c697bf97"),
                column: "CreatedAt",
                value: new DateTime(2024, 10, 13, 16, 8, 28, 958, DateTimeKind.Utc).AddTicks(5755));

            migrationBuilder.UpdateData(
                table: "TaxRate",
                keyColumn: "Id",
                keyValue: new Guid("59bb4a30-7cae-47ab-800d-a117e0134ac5"),
                column: "CreatedAt",
                value: new DateTime(2024, 10, 13, 16, 8, 28, 958, DateTimeKind.Utc).AddTicks(5799));

            migrationBuilder.CreateIndex(
                name: "IX_Product_CompanyId",
                table: "Product",
                column: "CompanyId");
        }
    }
}
