﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Framework.Data.Domains
{
    public partial class TransactionType : _BaseDomain
    {
        public TransactionType()
        {
            Transaction = new HashSet<Transaction>();
            AccountGroupByPeriods = new HashSet<AccountGroupByPeriod>();
        }
        public required string Code { get; set; }
        public required string Name { get; set; }
        public virtual ICollection<Transaction> Transaction { get; }
        public virtual ICollection<AccountGroupByPeriod> AccountGroupByPeriods { get; }

    }
}
