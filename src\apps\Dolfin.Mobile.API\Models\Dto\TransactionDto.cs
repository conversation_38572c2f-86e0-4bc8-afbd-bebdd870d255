﻿using Dolfin.Framework.Data.Domains.CustomIdentity;
using Dolfin.Framework.Data.Domains;

namespace Dolfin.Mobile.API.Models.Dto
{
    public partial class TransactionDto     
    {
        public TransactionDto()
        {
            TransactionItem = new HashSet<TransactionItemDto>();
            TransactionPaid = new HashSet<TransactionPaidDto>();
        }
        public Guid Id { get; set; }
        public required string TrxNo { get; set; }
        public DateTime TrxDatetime { get; set; }
        public required string CurrencyCode { get; set; }
        public decimal ExchangeRate { get; set; }
        public decimal TotalRoundingAdjustmentAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal TotalAmountWOTax { get; set; }
        public decimal TotalDiscount { get; set; }
        public decimal TotalExclTaxAmount { get; set; }
        public decimal TotalInclTaxAmount { get; set; }
        public decimal TotalSalesTaxAmount { get; set; }
        public decimal TotalServiceTaxAmount { get; set; }
        public decimal SalesTaxRate { get; set; }
        public decimal ServiceTaxRate { get; set; }
        public Guid SalesTaxNoId { get; set; }
        public Guid ServiceTaxNoId { get; set; }
        public decimal TotalPayableAmount { get; set; }
        public required string PIC { get; set; }
        public string? SignatureUBL21 { get; set; }
        public int? EInvoiceSyncRetryCount { get; set; }
        public string? EInvoiceSyncPayload { get; set; }
        public string? EInvoiceSyncResponse { get; set; }
        public DateTime? EInvoiceSyncAt { get; set; }
        public Guid TermId { get; set; }
        public Guid AccountGroupId { get; set; }
        public Guid TransactionTypeId { get; set; }
        //public Guid CompanyId { get; set; }
        public Guid BranchId { get; set; }
        public Guid CustomerId { get; set; }
        public string UserId { get; set; }
        public Guid ShippingAddressId { get; set; }
        public Guid BillingAddressId { get; set; }
        public Guid TransactionStatusId { get; set; }
        public virtual TaxRateDto? SalesTaxNo { get; set; }
        public virtual TaxRateDto? ServiceTaxNo { get; set; }
        public virtual TermDto? Term { get; set; }
        public virtual AccountGroupDto? AccountGroup { get; set; }
        public virtual TransactionTypeDto? TransactionType { get; set; }
        //public virtual Company? Company { get; set; }
        public virtual BranchDto? Branch { get; set; }
        public virtual CustomerDto? Customer { get; set; }
        //public virtual ApplicationUserDto? User { get; set; }
        public virtual AddressDto? ShippingAddress { get; set; }
        public virtual AddressDto? BillingAddress { get; set; }
        public virtual TransactionStatusDto? TransactionStatus { get; set; }
        public virtual ICollection<TransactionItemDto> TransactionItem { get; }
        public virtual ICollection<TransactionPaidDto> TransactionPaid { get; }
    }
}
