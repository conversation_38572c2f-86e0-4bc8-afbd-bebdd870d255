﻿using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Entity;
using Dolfin.Mobile.API.Models.Request;
using Dolfin.Utility.Models;
using static Dolfin.Utility.Enum.Enums;

namespace Dolfin.Mobile.API.Services
{
    public interface ITransactionService
    {
        DolfinDbContext CreateDbContext();

        // Transaction methods
        Task<BaseResponse<PagedList<Transaction>>> GetTransactionList(Pagination pagination = null, CommonFilterList filterList = null, Guid? companyId = null);
        Task<BaseResponse<Transaction>> GetTransactionByGuid(Guid transactionId);
        Task<BaseResponse<ResultId>> InsertTransaction(TransactionRequest reqBody, DolfinDbContext dbContextRollback);
        Task<BaseResponse<ResultId>> UpdateTransaction(UpdateTransactionRequest reqBody, DolfinDbContext dbContextRollback);

        // Transaction Item methods
        Task<BaseResponse<ResultId>> InsertTransactionItem(TransactionItemRequest reqBody, DolfinDbContext dbContextRollback);
        Task<BaseResponse<ResultId>> UpdateTransactionItem(Guid trxId, UpsertTransactionItemRequest reqBody, DolfinDbContext dbContextRollback);
        Task<NoResultResponse> DeleteTransactionItem(Guid id, DolfinDbContext dbContextRollback = null);
        Task<NoResultResponse> DeleteTransaction(Guid id, DolfinDbContext dbContextRollback = null);

        // Transaction Paid methods
        Task<BaseResponse<PagedList<TransactionPaid>>> GetTransactionPaidList(Pagination pagination = null, CommonFilterList filterList = null, Guid? transactionId = null);
        Task<BaseResponse<TransactionPaid>> GetTransactionPaidByGuid(Guid transactionPaidId);
        Task<BaseResponse<ResultId>> InsertTransactionPaid(TransactionPaidRequest reqBody, DolfinDbContext dbContextRollback);
        Task<BaseResponse<ResultId>> UpdateTransactionPaid(UpdateTransactionPaidRequest reqBody, DolfinDbContext dbContextRollback);
        Task<NoResultResponse> DeleteTransactionPaid(Guid id, DolfinDbContext dbContextRollback = null);

        // Payment Type methods
        Task<BaseResponse<PagedList<PaymentType>>> GetPaymentTypeList(Pagination pagination = null, CommonFilterList filterList = null, Guid? companyId = null);
        Task<BaseResponse<PaymentType>> GetPaymentTypeByGuid(Guid paymentTypeId);
        Task<BaseResponse<ResultId>> InsertPaymentType(PaymentTypeRequest reqBody, DolfinDbContext dbContextRollback = null);
        Task<BaseResponse<ResultId>> UpdatePaymentType(UpdatePaymentTypeRequest reqBody, DolfinDbContext dbContextRollback = null);
        Task<NoResultResponse> DeletePaymentType(Guid id, DolfinDbContext dbContextRollback = null);

        // Other methods
        Task<BaseResponse<PagedList<TransactionStatus>>> GetTransactionStatusList(Pagination pagination = null);
        Task<BaseResponse<TransactionStatus>> GetTransactionStatusByGuid(Guid transactionStatusId);
        Task<BaseResponse<PagedList<TransactionType>>> GetTransactionTypeList(Pagination pagination = null);
        Task<BaseResponse<TransactionType>> GetTransactionTypeByGuid(Guid transactionTypeId);
        Task<BaseResponse<AccountGroup>> GetAccountGroupByGuid(Guid accountGroupId);
    }
}
