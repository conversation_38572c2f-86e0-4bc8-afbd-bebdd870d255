using System;
using System.ComponentModel.DataAnnotations;

namespace Dolfin.Mobile.API.Models.Request
{
    /// <summary>
    /// Request model for creating a new lookup item
    /// </summary>
    public class LookupItemRequest
    {
        /// <summary>
        /// Unique code for the lookup item
        /// </summary>
        [Required]
        [StringLength(50)]
        public string Code { get; set; }

        /// <summary>
        /// Display value for the lookup item
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Value { get; set; }

        /// <summary>
        /// Optional description for the lookup item
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// Display order for the lookup item
        /// </summary>
        public int? DisplayOrder { get; set; }

        /// <summary>
        /// Additional data in JSON format (can store custom properties)
        /// </summary>
        public string? AdditionalData { get; set; }

        /// <summary>
        /// ID of the lookup group this item belongs to
        /// </summary>
        [Required]
        public Guid LookupGroupId { get; set; }
    }

    /// <summary>
    /// Request model for updating an existing lookup item
    /// </summary>
    public class UpdateLookupItemRequest : LookupItemRequest
    {
        /// <summary>
        /// Unique identifier of the lookup item to update
        /// </summary>
        [Required]
        public Guid Id { get; set; }
    }
}
