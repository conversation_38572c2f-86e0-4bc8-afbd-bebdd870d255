using System.ComponentModel.DataAnnotations;

namespace Dolfin.Mobile.API.Models.Request
{
    /// <summary>
    /// Request model for generating a code
    /// </summary>
    public class GenerateCodeRequest
    {
        /// <summary>
        /// The name of the table for which to generate a code
        /// </summary>
        [Required]
        public required string TableName { get; set; }
        
        /// <summary>
        /// The branch ID to use for code generation
        /// </summary>
        [Required]
        public required Guid BranchId { get; set; }
    }
}
