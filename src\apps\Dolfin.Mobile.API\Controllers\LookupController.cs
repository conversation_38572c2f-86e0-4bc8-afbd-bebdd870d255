using AutoMapper;
using Dolfin.Framework.Data.Domains;
using Dolfin.Mobile.API.Constants;
using Dolfin.Mobile.API.Infrastructure;
using Dolfin.Mobile.API.Models.Dto;
using Dolfin.Mobile.API.Models.Request;
using Dolfin.Mobile.API.Services;
using Dolfin.Utility.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Threading.Tasks;

namespace Dolfin.Mobile.API.Controllers
{
    /// <summary>
    /// Controller for managing lookup data
    /// </summary>
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class LookupController : ControllerCore
    {
        private readonly ILookupService _lookupService;
        private readonly IMapper _mapper;
        private readonly ILogger<LookupController> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        public LookupController(
            ILookupService lookupService,
            IMapper mapper,
            ILogger<LookupController> logger)
        {
            _lookupService = lookupService;
            _mapper = mapper;
            _logger = logger;
        }

        #region Lookup Group Endpoints

        /// <summary>
        /// Get all lookup groups
        /// </summary>
        /// <param name="pagination">Pagination parameters</param>
        /// <param name="filterList">Filter parameters</param>
        /// <param name="companyId">Optional company ID to filter by</param>
        /// <returns>A list of lookup groups</returns>
        [HttpGet("Group")]
        [RequirePermission(Permissions.Lookup.ViewGroups)]
        public async Task<IActionResult> GetLookupGroupList(
            [FromQuery] Pagination pagination,
            [FromQuery] CommonFilterList filterList,
            [FromQuery] Guid? companyId)
        {
            var response = await _lookupService.GetLookupGroupList(pagination, filterList, companyId);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occurred in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<List<LookupGroupDto>, PagedList<LookupGroup>>(_mapper, response, pagination, PagedList<LookupGroup>.PagedMetadata(response));
        }

        /// <summary>
        /// Get a lookup group by its ID
        /// </summary>
        /// <param name="id">The lookup group ID</param>
        /// <returns>The lookup group if found</returns>
        [HttpGet("Group/{id}")]
        [RequirePermission(Permissions.Lookup.ViewGroup)]
        public async Task<IActionResult> GetLookupGroupById(Guid id)
        {
            var response = await _lookupService.GetLookupGroupById(id);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occurred in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<LookupGroupDto, LookupGroup>(_mapper, response);
        }

        /// <summary>
        /// Get a lookup group by its code
        /// </summary>
        /// <param name="code">The lookup group code</param>
        /// <param name="companyId">Optional company ID to filter by</param>
        /// <returns>The lookup group if found</returns>
        [HttpGet("Group/ByCode/{code}")]
        [RequirePermission(Permissions.Lookup.ViewGroup)]
        public async Task<IActionResult> GetLookupGroupByCode(string code, [FromQuery] Guid? companyId)
        {
            var response = await _lookupService.GetLookupGroupByCode(code, companyId);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occurred in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<LookupGroupDto, LookupGroup>(_mapper, response);
        }

        /// <summary>
        /// Create a new lookup group
        /// </summary>
        /// <param name="request">The lookup group request</param>
        /// <returns>The ID of the created lookup group</returns>
        [HttpPost("Group")]
        [RequirePermission(Permissions.Lookup.CreateGroup)]
        public async Task<IActionResult> CreateLookupGroup([FromBody] LookupGroupRequest request)
        {
            var response = await _lookupService.CreateLookupGroup(request);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occurred in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<ResultId, ResultId>(_mapper, response);
        }

        /// <summary>
        /// Update an existing lookup group
        /// </summary>
        /// <param name="id">The lookup group ID</param>
        /// <param name="request">The update lookup group request</param>
        /// <returns>The ID of the updated lookup group</returns>
        [HttpPut("Group/{id}")]
        [RequirePermission(Permissions.Lookup.UpdateGroup)]
        public async Task<IActionResult> UpdateLookupGroup(Guid id, [FromBody] LookupGroupRequest request)
        {
            var updateRequest = _mapper.Map<UpdateLookupGroupRequest>(request);
            updateRequest.Id = id;

            var response = await _lookupService.UpdateLookupGroup(updateRequest);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occurred in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<ResultId, ResultId>(_mapper, response);
        }

        /// <summary>
        /// Delete a lookup group
        /// </summary>
        /// <param name="id">The lookup group ID</param>
        /// <returns>A success or error response</returns>
        [HttpDelete("Group/{id}")]
        [RequirePermission(Permissions.Lookup.DeleteGroup)]
        public async Task<IActionResult> DeleteLookupGroup(Guid id)
        {
            var response = await _lookupService.DeleteLookupGroup(id);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occurred in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<NoResult, NoResult>(_mapper, response);
        }

        #endregion

        #region Lookup Item Endpoints

        /// <summary>
        /// Get all lookup items for a specific group
        /// </summary>
        /// <param name="groupId">The lookup group ID</param>
        /// <param name="pagination">Pagination parameters</param>
        /// <param name="filterList">Filter parameters</param>
        /// <returns>A list of lookup items</returns>
        [HttpGet("Item/ByGroup/{groupId}")]
        [RequirePermission(Permissions.Lookup.ViewItems)]
        public async Task<IActionResult> GetLookupItemList(
            Guid groupId,
            [FromQuery] Pagination pagination,
            [FromQuery] CommonFilterList filterList)
        {
            var response = await _lookupService.GetLookupItemList(groupId, pagination, filterList);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occurred in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<List<LookupItemDto>, PagedList<LookupItem>>(_mapper, response, pagination, PagedList<LookupItem>.PagedMetadata(response));
        }

        /// <summary>
        /// Get all lookup items for a specific group without pagination
        /// </summary>
        /// <param name="groupId">The lookup group ID</param>
        /// <returns>A list of lookup items</returns>
        [HttpGet("Item/AllByGroup/{groupId}")]
        [RequirePermission(Permissions.Lookup.ViewItems)]
        public async Task<IActionResult> GetAllLookupItemsByGroup(Guid groupId)
        {
            var response = await _lookupService.GetAllLookupItemsByGroup(groupId);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occurred in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<List<LookupItemDto>, IEnumerable<LookupItem>>(_mapper, response);
        }

        /// <summary>
        /// Get a lookup item by its ID
        /// </summary>
        /// <param name="id">The lookup item ID</param>
        /// <returns>The lookup item if found</returns>
        [HttpGet("Item/{id}")]
        [RequirePermission(Permissions.Lookup.ViewItem)]
        public async Task<IActionResult> GetLookupItemById(Guid id)
        {
            var response = await _lookupService.GetLookupItemById(id);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occurred in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<LookupItemDto, LookupItem>(_mapper, response);
        }

        /// <summary>
        /// Create a new lookup item
        /// </summary>
        /// <param name="request">The lookup item request</param>
        /// <returns>The ID of the created lookup item</returns>
        [HttpPost("Item")]
        [RequirePermission(Permissions.Lookup.CreateItem)]
        public async Task<IActionResult> CreateLookupItem([FromBody] LookupItemRequest request)
        {
            var response = await _lookupService.CreateLookupItem(request);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occurred in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<ResultId, ResultId>(_mapper, response);
        }

        /// <summary>
        /// Update an existing lookup item
        /// </summary>
        /// <param name="id">The lookup item ID</param>
        /// <param name="request">The update lookup item request</param>
        /// <returns>The ID of the updated lookup item</returns>
        [HttpPut("Item/{id}")]
        [RequirePermission(Permissions.Lookup.UpdateItem)]
        public async Task<IActionResult> UpdateLookupItem(Guid id, [FromBody] LookupItemRequest request)
        {
            var updateRequest = _mapper.Map<UpdateLookupItemRequest>(request);
            updateRequest.Id = id;

            var response = await _lookupService.UpdateLookupItem(updateRequest);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occurred in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<ResultId, ResultId>(_mapper, response);
        }

        /// <summary>
        /// Delete a lookup item
        /// </summary>
        /// <param name="id">The lookup item ID</param>
        /// <returns>A success or error response</returns>
        [HttpDelete("Item/{id}")]
        [RequirePermission(Permissions.Lookup.DeleteItem)]
        public async Task<IActionResult> DeleteLookupItem(Guid id)
        {
            var response = await _lookupService.DeleteLookupItem(id);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occurred in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<NoResult, NoResult>(_mapper, response);
        }

        #endregion
    }
}
