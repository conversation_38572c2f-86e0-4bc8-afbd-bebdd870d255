﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace Dolfin.Framework.Data.Migrations
{
    /// <inheritdoc />
    public partial class addEmail : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("85c8bccf-7458-41af-84ec-4c6736f66644"));

            migrationBuilder.DeleteData(
                table: "Settings",
                keyColumn: "Id",
                keyValue: new Guid("4ae51f62-59bf-4fbd-86b4-7635705c219d"));

            migrationBuilder.DeleteData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("23355ff7-e300-487c-b570-67d80384ce85"));

            migrationBuilder.DeleteData(
                table: "TaxRate",
                keyColumn: "Id",
                keyValue: new Guid("24afe535-1923-45a0-9d8e-24b40e8b7fc0"));

            migrationBuilder.DeleteData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("44bfa063-191f-41ff-bc73-7329a670632b"));

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedAt",
                table: "AspNetUsers",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<Guid>(
                name: "CreatedBy",
                table: "AspNetUsers",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                table: "AspNetUsers",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "PhoneNo1",
                table: "AspNetUsers",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "PhoneNo2",
                table: "AspNetUsers",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "UpdatedAt",
                table: "AspNetUsers",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "UpdatedBy",
                table: "AspNetUsers",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedAt",
                table: "AspNetRoles",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<Guid>(
                name: "CreatedBy",
                table: "AspNetRoles",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AddColumn<bool>(
                name: "Editable",
                table: "AspNetRoles",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                table: "AspNetRoles",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "PartailEditable",
                table: "AspNetRoles",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "TargetType",
                table: "AspNetRoles",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateTime>(
                name: "UpdatedAt",
                table: "AspNetRoles",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "UpdatedBy",
                table: "AspNetRoles",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "EmailAccount",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Email = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    DisplayName = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    Host = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    Port = table.Column<int>(type: "integer", nullable: false),
                    Username = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    Password = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    EnableSsl = table.Column<bool>(type: "boolean", nullable: false),
                    UseDefaultCredentials = table.Column<bool>(type: "boolean", nullable: false),
                    IsDefault = table.Column<bool>(type: "boolean", nullable: false),
                    Protocol = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EmailAccount", x => x.Id);
                });

            migrationBuilder.InsertData(
                table: "Currency",
                columns: new[] { "Id", "Code", "CreatedAt", "CreatedBy", "ExchangeRate", "IsActive", "Name", "Precision", "Symbol", "UpdatedAt", "UpdatedBy" },
                values: new object[] { new Guid("eb5eb0a6-12f1-4c1e-8dc6-e4701e67175f"), "MYR", new DateTime(2024, 10, 3, 17, 20, 40, 78, DateTimeKind.Utc).AddTicks(2990), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), 1.0m, true, "Malaysia Ringgit", 2, "RM", null, null });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("aef630d3-6f0b-4c1f-a17b-c60f8f0408ea"),
                column: "CreatedAt",
                value: new DateTime(2024, 10, 3, 17, 20, 40, 78, DateTimeKind.Utc).AddTicks(2339));

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("b3ec5d2b-ca0d-45bc-ba86-bc965852922c"),
                column: "CreatedAt",
                value: new DateTime(2024, 10, 3, 17, 20, 40, 78, DateTimeKind.Utc).AddTicks(2728));

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("c0a87fee-d2a5-4c7e-beee-e60c7936f056"),
                column: "CreatedAt",
                value: new DateTime(2024, 10, 3, 17, 20, 40, 78, DateTimeKind.Utc).AddTicks(2573));

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("dbd5a6a5-0995-4ab6-822f-559ec36d222e"),
                column: "CreatedAt",
                value: new DateTime(2024, 10, 3, 17, 20, 40, 78, DateTimeKind.Utc).AddTicks(2882));

            migrationBuilder.InsertData(
                table: "Settings",
                columns: new[] { "Id", "Code", "CreatedBy", "Description", "Name", "Type", "UpdatedAt", "UpdatedBy", "Value" },
                values: new object[] { new Guid("db856745-7a43-480d-af8a-48f5cfc04af9"), "SYSTEM_NAME", new Guid("00000000-0000-0000-0000-000000000000"), "Name of the system", "System name", "SYSTEM", null, null, "Dolfin Solutions" });

            migrationBuilder.InsertData(
                table: "TaxCategories",
                columns: new[] { "Id", "Code", "CreatedAt", "CreatedBy", "Description", "IsActive", "Name", "UpdatedAt", "UpdatedBy" },
                values: new object[,]
                {
                    { new Guid("c31a1012-4e78-4033-85a7-f14c58d4787a"), "SALESTAX", new DateTime(2024, 10, 3, 17, 20, 40, 78, DateTimeKind.Utc).AddTicks(3065), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), null, true, "Sales Tax", null, null },
                    { new Guid("f2eb9858-3b42-4f9f-9560-a4f85aa2fe85"), "SERVICETAX", new DateTime(2024, 10, 3, 17, 20, 40, 78, DateTimeKind.Utc).AddTicks(3075), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), null, true, "Service Tax", null, null }
                });

            migrationBuilder.InsertData(
                table: "TaxRate",
                columns: new[] { "Id", "ChargePercentage", "Code", "CreatedAt", "CreatedBy", "Description", "IsActive", "Name", "TaxCategoryId", "UpdatedAt", "UpdatedBy" },
                values: new object[] { new Guid("5e227566-99a8-4718-bb89-36c3ab79d7f1"), 0, "SR", new DateTime(2024, 10, 3, 17, 20, 40, 78, DateTimeKind.Utc).AddTicks(3168), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), null, true, "Sales Rate", new Guid("c31a1012-4e78-4033-85a7-f14c58d4787a"), null, null });

            migrationBuilder.CreateIndex(
                name: "IX_User_BranchId",
                table: "User",
                column: "BranchId");

            migrationBuilder.AddForeignKey(
                name: "FK_User_Branch_BranchId",
                table: "User",
                column: "BranchId",
                principalTable: "Branch",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_User_Branch_BranchId",
                table: "User");

            migrationBuilder.DropTable(
                name: "EmailAccount");

            migrationBuilder.DropIndex(
                name: "IX_User_BranchId",
                table: "User");

            migrationBuilder.DeleteData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("eb5eb0a6-12f1-4c1e-8dc6-e4701e67175f"));

            migrationBuilder.DeleteData(
                table: "Settings",
                keyColumn: "Id",
                keyValue: new Guid("db856745-7a43-480d-af8a-48f5cfc04af9"));

            migrationBuilder.DeleteData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("f2eb9858-3b42-4f9f-9560-a4f85aa2fe85"));

            migrationBuilder.DeleteData(
                table: "TaxRate",
                keyColumn: "Id",
                keyValue: new Guid("5e227566-99a8-4718-bb89-36c3ab79d7f1"));

            migrationBuilder.DeleteData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("c31a1012-4e78-4033-85a7-f14c58d4787a"));

            migrationBuilder.DropColumn(
                name: "CreatedAt",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "CreatedBy",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "IsActive",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "PhoneNo1",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "PhoneNo2",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "UpdatedAt",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "UpdatedBy",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "CreatedAt",
                table: "AspNetRoles");

            migrationBuilder.DropColumn(
                name: "CreatedBy",
                table: "AspNetRoles");

            migrationBuilder.DropColumn(
                name: "Editable",
                table: "AspNetRoles");

            migrationBuilder.DropColumn(
                name: "IsActive",
                table: "AspNetRoles");

            migrationBuilder.DropColumn(
                name: "PartailEditable",
                table: "AspNetRoles");

            migrationBuilder.DropColumn(
                name: "TargetType",
                table: "AspNetRoles");

            migrationBuilder.DropColumn(
                name: "UpdatedAt",
                table: "AspNetRoles");

            migrationBuilder.DropColumn(
                name: "UpdatedBy",
                table: "AspNetRoles");

            migrationBuilder.InsertData(
                table: "Currency",
                columns: new[] { "Id", "Code", "CreatedAt", "CreatedBy", "ExchangeRate", "IsActive", "Name", "Precision", "Symbol", "UpdatedAt", "UpdatedBy" },
                values: new object[] { new Guid("85c8bccf-7458-41af-84ec-4c6736f66644"), "MYR", new DateTime(2024, 9, 23, 16, 18, 28, 942, DateTimeKind.Utc).AddTicks(2772), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), 1.0m, true, "Malaysia Ringgit", 2, "RM", null, null });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("aef630d3-6f0b-4c1f-a17b-c60f8f0408ea"),
                column: "CreatedAt",
                value: new DateTime(2024, 9, 23, 16, 18, 28, 942, DateTimeKind.Utc).AddTicks(1807));

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("b3ec5d2b-ca0d-45bc-ba86-bc965852922c"),
                column: "CreatedAt",
                value: new DateTime(2024, 9, 23, 16, 18, 28, 942, DateTimeKind.Utc).AddTicks(2405));

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("c0a87fee-d2a5-4c7e-beee-e60c7936f056"),
                column: "CreatedAt",
                value: new DateTime(2024, 9, 23, 16, 18, 28, 942, DateTimeKind.Utc).AddTicks(2129));

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("dbd5a6a5-0995-4ab6-822f-559ec36d222e"),
                column: "CreatedAt",
                value: new DateTime(2024, 9, 23, 16, 18, 28, 942, DateTimeKind.Utc).AddTicks(2697));

            migrationBuilder.InsertData(
                table: "Settings",
                columns: new[] { "Id", "Code", "CreatedBy", "Description", "Name", "Type", "UpdatedAt", "UpdatedBy", "Value" },
                values: new object[] { new Guid("4ae51f62-59bf-4fbd-86b4-7635705c219d"), "SYSTEM_NAME", new Guid("00000000-0000-0000-0000-000000000000"), "Name of the system", "System name", "SYSTEM", null, null, "Dolfin Solutions" });

            migrationBuilder.InsertData(
                table: "TaxCategories",
                columns: new[] { "Id", "Code", "CreatedAt", "CreatedBy", "Description", "IsActive", "Name", "UpdatedAt", "UpdatedBy" },
                values: new object[,]
                {
                    { new Guid("23355ff7-e300-487c-b570-67d80384ce85"), "SERVICETAX", new DateTime(2024, 9, 23, 16, 18, 28, 942, DateTimeKind.Utc).AddTicks(2826), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), null, true, "Service Tax", null, null },
                    { new Guid("44bfa063-191f-41ff-bc73-7329a670632b"), "SALESTAX", new DateTime(2024, 9, 23, 16, 18, 28, 942, DateTimeKind.Utc).AddTicks(2819), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), null, true, "Sales Tax", null, null }
                });

            migrationBuilder.InsertData(
                table: "TaxRate",
                columns: new[] { "Id", "ChargePercentage", "Code", "CreatedAt", "CreatedBy", "Description", "IsActive", "Name", "TaxCategoryId", "UpdatedAt", "UpdatedBy" },
                values: new object[] { new Guid("24afe535-1923-45a0-9d8e-24b40e8b7fc0"), 0, "SR", new DateTime(2024, 9, 23, 16, 18, 28, 942, DateTimeKind.Utc).AddTicks(2864), new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"), null, true, "Sales Rate", new Guid("44bfa063-191f-41ff-bc73-7329a670632b"), null, null });
        }
    }
}
