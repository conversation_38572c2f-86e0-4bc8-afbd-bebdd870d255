﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Mobile.API.Models.Dto
{
    public partial class TransactionPaidDto
    {
        public required Guid Id { get; set; }
        public decimal PaidAmount { get; set; }
        public DateTime PaidAt { get; set; }
        public Guid PaymentTypeId { get; set; }
        public Guid TrxId { get; set; }
        public virtual PaymentTypeDto? PaymentType { get; set; }
    }
}
