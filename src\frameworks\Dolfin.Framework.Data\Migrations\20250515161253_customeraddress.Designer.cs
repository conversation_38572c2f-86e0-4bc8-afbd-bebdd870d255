﻿// <auto-generated />
using System;
using Dolfin.Framework.Data.Entity;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Dolfin.Framework.Data.Migrations
{
    [DbContext(typeof(DolfinDbContext))]
    [Migration("20250515161253_customeraddress")]
    partial class customeraddress
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.7")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.AccountGroup", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("AccountGroup");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.AccountGroupByPeriod", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("BranchId")
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<bool>("Locked")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("false");

                    b.Property<int>("Month")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("TransactionTypeId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.Property<int>("Year")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("BranchId");

                    b.HasIndex("TransactionTypeId");

                    b.ToTable("AccountGroupByPeriod");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Address", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Address1")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Address2")
                        .HasColumnType("text");

                    b.Property<string>("Address3")
                        .HasColumnType("text");

                    b.Property<string>("Company")
                        .HasColumnType("text");

                    b.Property<string>("Coordinate")
                        .HasColumnType("text");

                    b.Property<Guid>("CountryId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Email")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<string>("FaxNo")
                        .HasColumnType("text");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("PhoneNo")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<string>("PostalCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("RegionId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("StateProvinceId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CountryId");

                    b.HasIndex("RegionId");

                    b.HasIndex("StateProvinceId");

                    b.ToTable("Address");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Branch", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("AddressId")
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<bool>("IsHq")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("false");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("CompanyId");

                    b.ToTable("Branch");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Classification", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("Classification");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Company", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<int?>("ConsolidatePay")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValueSql("5");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<Guid>("CurrencyId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("DefaultSalesTaxNoId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("DefaultServiceTaxNoId")
                        .HasColumnType("uuid");

                    b.Property<string>("EInvoiceClientId")
                        .HasColumnType("text");

                    b.Property<string>("EInvoiceClientSecret")
                        .HasColumnType("text");

                    b.Property<string>("EInvoiceToken")
                        .HasColumnType("text");

                    b.Property<DateTime?>("EInvoiceTokenDateTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("ExpiredAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<bool>("IsBranchSameProduct")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<bool?>("IsEInvoiceSubmitAuto")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("false");

                    b.Property<bool>("IsEnableInventory")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsRoundingAdjustment")
                        .HasColumnType("boolean");

                    b.Property<string>("Logo")
                        .HasColumnType("text");

                    b.Property<Guid?>("MsicId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RegNo")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("SstNo")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid?>("SubscriptionId")
                        .HasColumnType("uuid");

                    b.Property<string>("TinNo")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("WebSiteUrl")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CurrencyId");

                    b.HasIndex("DefaultSalesTaxNoId");

                    b.HasIndex("DefaultServiceTaxNoId");

                    b.HasIndex("MsicId");

                    b.HasIndex("SubscriptionId");

                    b.ToTable("Company");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Country", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<int?>("DisplayOrder")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<string>("Name")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<int>("NumericIsoCode")
                        .HasColumnType("integer");

                    b.Property<bool>("Published")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("false");

                    b.Property<string>("ThreeLetterIsoCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TwoLetterIsoCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("Country");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Currency", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<decimal>("ExchangeRate")
                        .HasColumnType("numeric");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Precision")
                        .HasColumnType("integer");

                    b.Property<string>("Symbol")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("Currency");

                    b.HasData(
                        new
                        {
                            Id = new Guid("69ed35dc-8775-4641-9f1d-26d9d91e5f4b"),
                            Code = "MYR",
                            CreatedAt = new DateTime(2025, 5, 15, 16, 12, 52, 584, DateTimeKind.Utc).AddTicks(6643),
                            CreatedBy = new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"),
                            ExchangeRate = 1.0m,
                            IsActive = true,
                            Name = "Malaysia Ringgit",
                            Precision = 2,
                            Symbol = "RM"
                        });
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.CustomIdentity.ApplicationRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("Editable")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("PartailEditable")
                        .HasColumnType("boolean");

                    b.Property<string>("TargetType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.CustomIdentity.ApplicationUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("integer");

                    b.Property<Guid?>("BranchId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("boolean");

                    b.Property<string>("FaxNo1")
                        .HasColumnType("text");

                    b.Property<string>("FaxNo2")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsAllowEditable")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("LastLoginAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("boolean");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<DateTime>("PasswordExpireAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("text");

                    b.Property<string>("PhoneNo1")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("PhoneNo2")
                        .HasColumnType("text");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("boolean");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("text");

                    b.Property<string>("SerialNo")
                        .HasColumnType("text");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("BranchId");

                    b.HasIndex("CompanyId");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex");

                    b.ToTable("AspNetUsers", (string)null);
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.CustomIdentity.ApplicationUserRole", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("RoleId")
                        .HasColumnType("text");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Customer", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AccountCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid?>("AccountGroupId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AddressId")
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<Guid>("CurrencyId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("DebtorTypeId")
                        .HasColumnType("uuid");

                    b.Property<string>("DefaultPIC")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("FullName")
                        .HasColumnType("text");

                    b.Property<string>("IdentityNo")
                        .HasColumnType("text");

                    b.Property<Guid?>("IdentityTypeId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<bool>("IsPICEditable")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("false");

                    b.Property<bool>("IsTaxExempt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("false");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ReferralCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid?>("ReferrerId")
                        .HasColumnType("uuid");

                    b.Property<string>("Remark")
                        .HasColumnType("text");

                    b.Property<string>("TinNo")
                        .HasColumnType("text");

                    b.Property<int>("TinVerifyStatus")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("AccountGroupId");

                    b.HasIndex("AddressId");

                    b.HasIndex("CompanyId");

                    b.HasIndex("CurrencyId");

                    b.HasIndex("DebtorTypeId");

                    b.HasIndex("IdentityTypeId");

                    b.HasIndex("ReferrerId");

                    b.ToTable("Customer");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.DebtorType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("DebtorType");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.EInvoice", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("AcceptedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CancelReason")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CancelledDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CustomerId1")
                        .HasColumnType("uuid");

                    b.Property<string>("DocumentBase64")
                        .HasColumnType("text");

                    b.Property<string>("DocumentHash")
                        .HasColumnType("text");

                    b.Property<string>("DocumentJson")
                        .HasColumnType("text");

                    b.Property<string>("DocumentLongId")
                        .HasColumnType("text");

                    b.Property<string>("DocumentToken")
                        .HasColumnType("text");

                    b.Property<string>("DocumentType")
                        .HasColumnType("text");

                    b.Property<string>("DocumentTypeVersion")
                        .HasColumnType("text");

                    b.Property<string>("DocumentUUID")
                        .HasColumnType("text");

                    b.Property<string>("DocumentUrl")
                        .HasColumnType("text");

                    b.Property<string>("DocumentXml")
                        .HasColumnType("text");

                    b.Property<string>("InternalNo")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("InvoiceNo")
                        .IsRequired()
                        .HasMaxLength(100)
                        .IsUnicode(true)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<string>("QrCodeUrl")
                        .HasColumnType("text");

                    b.Property<string>("RejectReason")
                        .HasColumnType("text");

                    b.Property<DateTime?>("RejectedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ResponseCode")
                        .HasColumnType("text");

                    b.Property<string>("ResponseMessage")
                        .HasColumnType("text");

                    b.Property<int?>("RetryCount")
                        .HasColumnType("integer");

                    b.Property<int>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<string>("SubmissionUID")
                        .HasColumnType("text");

                    b.Property<DateTime?>("SubmittedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("TransactionId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("TransactionId1")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("CustomerId");

                    b.HasIndex("CustomerId1");

                    b.HasIndex("TransactionId");

                    b.HasIndex("TransactionId1");

                    b.ToTable("EInvoice");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.EInvoiceErrorLog", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<Guid>("EInvoiceId")
                        .HasColumnType("uuid");

                    b.Property<string>("ErrorCode")
                        .HasColumnType("text");

                    b.Property<string>("ErrorMessage")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<string>("SubmissionUID")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("EInvoiceId");

                    b.ToTable("EInvoiceErrorLog");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.EInvoiceSourceUpload", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("FileName")
                        .HasColumnType("text");

                    b.Property<string>("FileUrl")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<string>("LogDetails")
                        .HasColumnType("text");

                    b.Property<string>("MimeType")
                        .HasColumnType("text");

                    b.Property<DateTime?>("ProcessedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("SourceUploadStatus")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("EInvoiceSourceUpload");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.IdentityType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Format")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<string>("Name")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("IdentityType");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Inventory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("BranchId")
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("BranchId");

                    b.ToTable("Inventory");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.InventoryItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("BalanceQuantity")
                        .HasColumnType("numeric");

                    b.Property<decimal>("Cost")
                        .HasColumnType("numeric");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ExpireAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("InventoryProductId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<DateTime?>("ManufacturingAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("ManufacturingYear")
                        .HasColumnType("integer");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ProductUOMId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("SafeQuantity")
                        .HasColumnType("numeric");

                    b.Property<DateTime>("StockInAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal>("StockQuantity")
                        .HasColumnType("numeric");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("InventoryProductId");

                    b.HasIndex("ProductId");

                    b.HasIndex("ProductUOMId");

                    b.ToTable("InventoryItem");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.InventoryItemTransactionItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<Guid>("InventoryItemId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<decimal>("Quantity")
                        .HasColumnType("numeric");

                    b.Property<Guid>("TransactionItemId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("InventoryItemId");

                    b.HasIndex("TransactionItemId");

                    b.ToTable("InventoryItemTransactionItem");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.InventoryProduct", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("BalanceQuantity")
                        .HasColumnType("numeric");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<Guid>("InventoryId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("SafeQuantity")
                        .HasColumnType("numeric");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("InventoryId");

                    b.HasIndex("ProductId");

                    b.ToTable("InventoryProduct");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.JobConfig", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("LastProcessDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.HasKey("Id");

                    b.ToTable("JobConfig");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Msic", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("Msic");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.PaymentType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AccountCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("AccountGroupId")
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("AccountGroupId");

                    b.ToTable("PaymentType");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Prefix", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("BranchId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<int>("LastNumber")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("PaddingLength")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(5);

                    b.Property<string>("PrefixValue")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<string>("TableName")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("BranchId");

                    b.HasIndex("TableName", "BranchId")
                        .IsUnique()
                        .HasDatabaseName("IX_Prefix_TableName_Branch_Unique");

                    b.ToTable("Prefix");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Product", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AccountCode")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<DateTime?>("AvailableEndAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("AvailableStartAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ClassificationId")
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CurrencyId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CustomSalesTaxNoId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CustomServiceTaxNoId")
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<int?>("DisplayOrder")
                        .HasColumnType("integer");

                    b.Property<decimal?>("Height")
                        .HasColumnType("numeric");

                    b.Property<string>("Image")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<bool>("IsTaxExcl")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("false");

                    b.Property<bool>("IsTaxExempt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("false");

                    b.Property<decimal?>("Length")
                        .HasColumnType("numeric");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("ProductCategoryId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ProductCostMethodId")
                        .HasColumnType("uuid");

                    b.Property<bool>("Published")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("false");

                    b.Property<string>("Sku")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.Property<decimal?>("Weight")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("Width")
                        .HasColumnType("numeric");

                    b.HasKey("Id");

                    b.HasIndex("ClassificationId");

                    b.HasIndex("CurrencyId");

                    b.HasIndex("CustomSalesTaxNoId");

                    b.HasIndex("CustomServiceTaxNoId");

                    b.HasIndex("ProductCategoryId");

                    b.HasIndex("ProductCostMethodId");

                    b.HasIndex("CompanyId", "Code")
                        .IsUnique()
                        .HasDatabaseName("IX_Product_Company_Code_Unique");

                    b.HasIndex("CompanyId", "Sku")
                        .IsUnique()
                        .HasDatabaseName("IX_Product_Company_Sku_Unique");

                    b.ToTable("Product");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.ProductCategory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<int?>("DisplayOrder")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("Published")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId", "Code")
                        .IsUnique()
                        .HasDatabaseName("IX_ProductCategory_Company_Code_Unique");

                    b.ToTable("ProductCategory");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.ProductCostMethod", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("ProductCostMethod");

                    b.HasData(
                        new
                        {
                            Id = new Guid("c219bf33-28d0-4595-a25b-8018bd87e67a"),
                            Code = "FIFO",
                            CreatedAt = new DateTime(2025, 5, 15, 16, 12, 52, 584, DateTimeKind.Utc).AddTicks(6749),
                            CreatedBy = new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"),
                            IsActive = true,
                            Name = "First In, First Out"
                        },
                        new
                        {
                            Id = new Guid("5d22874b-5834-40a2-81be-bf5100eb845d"),
                            Code = "FILO",
                            CreatedAt = new DateTime(2025, 5, 15, 16, 12, 52, 584, DateTimeKind.Utc).AddTicks(6753),
                            CreatedBy = new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"),
                            IsActive = true,
                            Name = "First In, Last Out"
                        },
                        new
                        {
                            Id = new Guid("e57e65d0-85a9-49c8-8463-3424a42e1f01"),
                            Code = "LIFO",
                            CreatedAt = new DateTime(2025, 5, 15, 16, 12, 52, 584, DateTimeKind.Utc).AddTicks(6756),
                            CreatedBy = new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"),
                            IsActive = true,
                            Name = "Last In, First Out"
                        },
                        new
                        {
                            Id = new Guid("11fd5e73-51fa-428a-a919-960ee860b00f"),
                            Code = "WA",
                            CreatedAt = new DateTime(2025, 5, 15, 16, 12, 52, 584, DateTimeKind.Utc).AddTicks(6759),
                            CreatedBy = new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"),
                            IsActive = true,
                            Name = "Weighted Average"
                        });
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.ProductPrice", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("EffectiveAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal>("FractionQty")
                        .HasColumnType("numeric");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<decimal>("Price")
                        .HasColumnType("numeric");

                    b.Property<Guid>("ProductUOMId")
                        .HasColumnType("uuid");

                    b.Property<string>("Remark")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ProductUOMId");

                    b.ToTable("ProductPrice");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.ProductUOM", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Barcode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Code")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<decimal?>("Cost")
                        .HasColumnType("numeric");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<decimal>("Fraction")
                        .HasColumnType("numeric");

                    b.Property<decimal>("FractionTotal")
                        .HasColumnType("numeric");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<bool>("IsMainUom")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("false");

                    b.Property<bool>("IsPriceFollowUomMainId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("false");

                    b.Property<decimal?>("MaxEditPrice")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("MinEditPrice")
                        .HasColumnType("numeric");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<decimal?>("OrderMaxQty")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("OrderMinQty")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("PreviousCost")
                        .HasColumnType("numeric");

                    b.Property<bool>("PriceEditable")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("false");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("UomPrimaryId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("UomSecondaryId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("UomPrimaryId");

                    b.HasIndex("UomSecondaryId");

                    b.ToTable("ProductUOM");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.RefreshToken", b =>
                {
                    b.Property<string>("Token")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("ExpiryDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("Invalidated")
                        .HasColumnType("boolean");

                    b.Property<string>("JwtId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("Used")
                        .HasColumnType("boolean");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Token");

                    b.HasIndex("UserId");

                    b.ToTable("RefreshTokens");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Region", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<int?>("DisplayOrder")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<string>("Name")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<bool>("Published")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("false");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("Region");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.RegionState", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<Guid>("RegionId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("StateId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("RegionId");

                    b.HasIndex("StateId");

                    b.ToTable("RegionState");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Role", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("Editable")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("false");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("PartailEditable")
                        .HasColumnType("boolean");

                    b.Property<string>("TargetType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("Role");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.RolePermission", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("RolePermission");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Settings", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Settings");

                    b.HasData(
                        new
                        {
                            Id = new Guid("929c9070-55d2-4b9d-832d-84f6e8bda3cd"),
                            Code = "SYSTEM_NAME",
                            CreatedAt = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            CreatedBy = new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"),
                            Description = "Name of the system",
                            IsActive = false,
                            Name = "System name",
                            Type = "SYSTEM",
                            Value = "Dolfin Solutions"
                        });
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.State", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Abbreviation")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<Guid>("CountryId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<int?>("DisplayOrder")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<string>("Name")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<bool>("Published")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("false");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CountryId");

                    b.ToTable("State");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Subscription", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("DisplayOrder")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<int>("Months")
                        .IsUnicode(true)
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<bool>("Published")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("false");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("Subscription");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.TaxCategories", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("TaxCategories");

                    b.HasData(
                        new
                        {
                            Id = new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"),
                            Code = "SALESTAX",
                            CreatedAt = new DateTime(2025, 5, 15, 16, 12, 52, 584, DateTimeKind.Utc).AddTicks(6706),
                            CreatedBy = new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"),
                            IsActive = true,
                            Name = "Sales Tax"
                        },
                        new
                        {
                            Id = new Guid("81ba4893-d891-4e3c-a5e7-ba13c697bf97"),
                            Code = "SERVICETAX",
                            CreatedAt = new DateTime(2025, 5, 15, 16, 12, 52, 584, DateTimeKind.Utc).AddTicks(6712),
                            CreatedBy = new Guid("7c255661-4d3a-4aa5-9a7d-6f8e44455f56"),
                            IsActive = true,
                            Name = "Service Tax"
                        });
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.TaxRate", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("ChargePercentage")
                        .HasColumnType("integer");

                    b.Property<string>("Code")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("TaxCategoryId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("TaxCategoryId");

                    b.ToTable("TaxRate");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Term", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<int>("Days")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("Term");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.TraceLogIntegration", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<string>("Request")
                        .HasColumnType("text");

                    b.Property<string>("Response")
                        .HasColumnType("text");

                    b.Property<string>("ResponseCode")
                        .HasColumnType("text");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("TransactionId")
                        .HasColumnType("text");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Url")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("TraceLogIntegration");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Transaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AccountGroupId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("BillingAddressId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("BranchId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("ExchangeRate")
                        .HasColumnType("numeric");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<bool?>("IsEInvoiceCreated")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("false");

                    b.Property<string>("PIC")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("SalesTaxNoId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("SalesTaxRate")
                        .HasColumnType("numeric");

                    b.Property<Guid>("ServiceTaxNoId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("ServiceTaxRate")
                        .HasColumnType("numeric");

                    b.Property<Guid>("ShippingAddressId")
                        .HasColumnType("uuid");

                    b.Property<string>("TermDay")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid?>("TermId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal>("TotalAmountWOTax")
                        .HasColumnType("numeric");

                    b.Property<decimal>("TotalDiscount")
                        .HasColumnType("numeric");

                    b.Property<decimal>("TotalExclTaxAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal>("TotalInclTaxAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal>("TotalPayableAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal>("TotalRoundingAdjustmentAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal>("TotalSalesTaxAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal>("TotalServiceTaxAmount")
                        .HasColumnType("numeric");

                    b.Property<Guid>("TransactionStatusId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("TransactionTypeId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("TrxDatetime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("TrxNo")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid?>("UserId1")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("AccountGroupId");

                    b.HasIndex("BillingAddressId");

                    b.HasIndex("BranchId");

                    b.HasIndex("CustomerId");

                    b.HasIndex("SalesTaxNoId");

                    b.HasIndex("ServiceTaxNoId");

                    b.HasIndex("ShippingAddressId");

                    b.HasIndex("TermId");

                    b.HasIndex("TransactionStatusId");

                    b.HasIndex("TransactionTypeId");

                    b.HasIndex("UserId");

                    b.HasIndex("UserId1");

                    b.ToTable("Transaction");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.TransactionItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("AdjAmount")
                        .HasColumnType("numeric");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("Discount")
                        .HasColumnType("numeric");

                    b.Property<decimal>("ExclTaxAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal>("FractionQuantity")
                        .HasColumnType("numeric");

                    b.Property<decimal>("FractionTotal")
                        .HasColumnType("numeric");

                    b.Property<decimal>("InclTaxAmount")
                        .HasColumnType("numeric");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<decimal>("OriUnitProductCost")
                        .HasColumnType("numeric");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ProductPriceId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ProductUOMId")
                        .HasColumnType("uuid");

                    b.Property<string>("ProductUOMPrimaryMCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ProductUOMSecondaryMCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<decimal>("Quantity")
                        .HasColumnType("numeric");

                    b.Property<decimal>("SalesTaxAmount")
                        .HasColumnType("numeric");

                    b.Property<Guid>("SalesTaxNoId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("SalesTaxRate")
                        .HasColumnType("numeric");

                    b.Property<decimal>("ServiceTaxAmount")
                        .HasColumnType("numeric");

                    b.Property<Guid>("ServiceTaxNoId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("ServiceTaxRate")
                        .HasColumnType("numeric");

                    b.Property<decimal>("SubTotalAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal>("TotalUnitAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal>("TotalUnitAmountWOTax")
                        .HasColumnType("numeric");

                    b.Property<decimal>("TransactionProductPrice")
                        .HasColumnType("numeric");

                    b.Property<Guid>("TrxId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("UnitAmount")
                        .HasColumnType("numeric");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("ProductPriceId");

                    b.HasIndex("ProductUOMId");

                    b.HasIndex("SalesTaxNoId");

                    b.HasIndex("ServiceTaxNoId");

                    b.HasIndex("TrxId");

                    b.ToTable("TransactionItem");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.TransactionPaid", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<decimal>("PaidAmount")
                        .HasColumnType("numeric");

                    b.Property<DateTime>("PaidAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("PaymentTypeId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("TrxId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("PaymentTypeId");

                    b.HasIndex("TrxId");

                    b.ToTable("TransactionPaid");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.TransactionStatus", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("TransactionStatus");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.TransactionType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("TransactionType");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.UOM", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("UOMCategoryId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("UOMCategoryId");

                    b.ToTable("UOM");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.UOMCategories", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("UOMCategories");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("integer");

                    b.Property<Guid?>("BranchId")
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<Guid?>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Email")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<string>("FaxNo1")
                        .HasColumnType("text");

                    b.Property<string>("FaxNo2")
                        .HasColumnType("text");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<DateTime>("LastLoginAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("Locked")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("false");

                    b.Property<DateTime?>("LockedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LockedEnd")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("PasswordExpireAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("PasswordSalt")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("PhoneNo1")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.Property<string>("PhoneNo2")
                        .HasColumnType("text");

                    b.Property<string>("SerialNo")
                        .HasColumnType("text");

                    b.Property<string>("TempPassword")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Username")
                        .IsRequired()
                        .IsUnicode(true)
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("BranchId");

                    b.HasIndex("CompanyId");

                    b.ToTable("User");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.UserRole", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.HasIndex("UserId");

                    b.ToTable("UserRole");
                });

            modelBuilder.Entity("Dolfin.Mobile.API.Models.Email", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AttachmentFileName")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("AttachmentFilePath")
                        .HasColumnType("text");

                    b.Property<string>("Bcc")
                        .HasMaxLength(500)
                        .IsUnicode(false)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Body")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Cc")
                        .HasMaxLength(500)
                        .IsUnicode(false)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DontSendBeforeDateUtc")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("EmailAccountId")
                        .HasColumnType("uuid");

                    b.Property<string>("FromEmail")
                        .IsRequired()
                        .HasMaxLength(500)
                        .IsUnicode(false)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("FromName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<int>("Priority")
                        .HasColumnType("integer");

                    b.Property<string>("ReplyToEmail")
                        .HasMaxLength(500)
                        .IsUnicode(false)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("ReplyToName")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("SentOnUtc")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("SentTries")
                        .HasColumnType("integer");

                    b.Property<string>("Subject")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ToEmail")
                        .IsRequired()
                        .HasMaxLength(500)
                        .IsUnicode(false)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("ToName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("EmailAccountId");

                    b.ToTable("Email");
                });

            modelBuilder.Entity("Dolfin.Mobile.API.Models.EmailAccount", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("EnableSsl")
                        .HasColumnType("boolean");

                    b.Property<string>("Host")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("boolean");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("Port")
                        .HasColumnType("integer");

                    b.Property<string>("Protocol")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("UseDefaultCredentials")
                        .HasColumnType("boolean");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.HasKey("Id");

                    b.ToTable("EmailAccount");
                });

            modelBuilder.Entity("Dolfin.Mobile.API.Models.EmailTemplate", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("BccEmailAddresses")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Body")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("now()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<Guid>("EmailAccountId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValueSql("true");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Subject")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("EmailAccountId");

                    b.ToTable("EmailTemplate");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("text");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("text");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("text");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("text");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("Value")
                        .HasColumnType("text");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.AccountGroupByPeriod", b =>
                {
                    b.HasOne("Dolfin.Framework.Data.Domains.Branch", "Branch")
                        .WithMany("AccountGroupByPeriods")
                        .HasForeignKey("BranchId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_AccountGroupByPeriod_Branch");

                    b.HasOne("Dolfin.Framework.Data.Domains.TransactionType", "TransactionType")
                        .WithMany("AccountGroupByPeriods")
                        .HasForeignKey("TransactionTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_AccountGroupByPeriod_TransactionType");

                    b.Navigation("Branch");

                    b.Navigation("TransactionType");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Address", b =>
                {
                    b.HasOne("Dolfin.Framework.Data.Domains.Country", "Country")
                        .WithMany("Address")
                        .HasForeignKey("CountryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Address_Country");

                    b.HasOne("Dolfin.Framework.Data.Domains.Region", "Region")
                        .WithMany("Address")
                        .HasForeignKey("RegionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Address_Region");

                    b.HasOne("Dolfin.Framework.Data.Domains.State", "State")
                        .WithMany("Address")
                        .HasForeignKey("StateProvinceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Address_State");

                    b.Navigation("Country");

                    b.Navigation("Region");

                    b.Navigation("State");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Branch", b =>
                {
                    b.HasOne("Dolfin.Framework.Data.Domains.Address", "Address")
                        .WithMany("Branch")
                        .HasForeignKey("AddressId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Branch_Address");

                    b.HasOne("Dolfin.Framework.Data.Domains.Company", "Company")
                        .WithMany("Branch")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Branch_Company");

                    b.Navigation("Address");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Company", b =>
                {
                    b.HasOne("Dolfin.Framework.Data.Domains.Currency", "Currency")
                        .WithMany("Company")
                        .HasForeignKey("CurrencyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Company_Currency");

                    b.HasOne("Dolfin.Framework.Data.Domains.TaxRate", "DefaultSalesTaxNo")
                        .WithMany("CompanyDefaultSalesTaxNo")
                        .HasForeignKey("DefaultSalesTaxNoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Company_TaxRate_SalesTaxNo");

                    b.HasOne("Dolfin.Framework.Data.Domains.TaxRate", "DefaultServiceTaxNo")
                        .WithMany("CompanyDefaultServiceTaxNo")
                        .HasForeignKey("DefaultServiceTaxNoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Company_TaxRate_ServiceTaxNo");

                    b.HasOne("Dolfin.Framework.Data.Domains.Msic", "Msic")
                        .WithMany("Company")
                        .HasForeignKey("MsicId")
                        .HasConstraintName("FK_Company_Msic");

                    b.HasOne("Dolfin.Framework.Data.Domains.Subscription", "Subscription")
                        .WithMany("Company")
                        .HasForeignKey("SubscriptionId")
                        .HasConstraintName("FK_Company_Subscription");

                    b.Navigation("Currency");

                    b.Navigation("DefaultSalesTaxNo");

                    b.Navigation("DefaultServiceTaxNo");

                    b.Navigation("Msic");

                    b.Navigation("Subscription");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.CustomIdentity.ApplicationUser", b =>
                {
                    b.HasOne("Dolfin.Framework.Data.Domains.Branch", "Branch")
                        .WithMany("User")
                        .HasForeignKey("BranchId")
                        .HasConstraintName("FK_Branch_User");

                    b.HasOne("Dolfin.Framework.Data.Domains.Company", "Company")
                        .WithMany("User")
                        .HasForeignKey("CompanyId")
                        .HasConstraintName("FK_Company_User");

                    b.Navigation("Branch");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.CustomIdentity.ApplicationUserRole", b =>
                {
                    b.HasOne("Dolfin.Framework.Data.Domains.CustomIdentity.ApplicationRole", "Role")
                        .WithMany("UserRoles")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Dolfin.Framework.Data.Domains.CustomIdentity.ApplicationUser", "User")
                        .WithMany("UserRoles")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Customer", b =>
                {
                    b.HasOne("Dolfin.Framework.Data.Domains.AccountGroup", "AccountGroup")
                        .WithMany("Customer")
                        .HasForeignKey("AccountGroupId")
                        .HasConstraintName("FK_Customer_AccountGroup");

                    b.HasOne("Dolfin.Framework.Data.Domains.Address", "Address")
                        .WithMany("Customer")
                        .HasForeignKey("AddressId")
                        .HasConstraintName("FK_Customer_Address");

                    b.HasOne("Dolfin.Framework.Data.Domains.Company", "Company")
                        .WithMany("Cust")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Customer_Company");

                    b.HasOne("Dolfin.Framework.Data.Domains.Currency", "Currency")
                        .WithMany("Customer")
                        .HasForeignKey("CurrencyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Customer_Currency");

                    b.HasOne("Dolfin.Framework.Data.Domains.DebtorType", "DebtorType")
                        .WithMany("Customer")
                        .HasForeignKey("DebtorTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Customer_DebtorType");

                    b.HasOne("Dolfin.Framework.Data.Domains.IdentityType", "IdentityType")
                        .WithMany("Customer")
                        .HasForeignKey("IdentityTypeId")
                        .HasConstraintName("FK_Customer_IdentityType");

                    b.HasOne("Dolfin.Framework.Data.Domains.Customer", "Referrer")
                        .WithMany("CustomerReferral")
                        .HasForeignKey("ReferrerId")
                        .HasConstraintName("FK_Customer_Referrer");

                    b.Navigation("AccountGroup");

                    b.Navigation("Address");

                    b.Navigation("Company");

                    b.Navigation("Currency");

                    b.Navigation("DebtorType");

                    b.Navigation("IdentityType");

                    b.Navigation("Referrer");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.EInvoice", b =>
                {
                    b.HasOne("Dolfin.Framework.Data.Domains.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Dolfin.Framework.Data.Domains.Customer", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_EInvoice_Customer");

                    b.HasOne("Dolfin.Framework.Data.Domains.Customer", null)
                        .WithMany("EInvoice")
                        .HasForeignKey("CustomerId1");

                    b.HasOne("Dolfin.Framework.Data.Domains.Transaction", "Transaction")
                        .WithMany()
                        .HasForeignKey("TransactionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_EInvoice_Transaction");

                    b.HasOne("Dolfin.Framework.Data.Domains.Transaction", null)
                        .WithMany("EInvoice")
                        .HasForeignKey("TransactionId1");

                    b.Navigation("Company");

                    b.Navigation("Customer");

                    b.Navigation("Transaction");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.EInvoiceErrorLog", b =>
                {
                    b.HasOne("Dolfin.Framework.Data.Domains.EInvoice", "EInvoice")
                        .WithMany()
                        .HasForeignKey("EInvoiceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_EInvoiceErrorLog_EInvoice");

                    b.Navigation("EInvoice");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Inventory", b =>
                {
                    b.HasOne("Dolfin.Framework.Data.Domains.Branch", "Branch")
                        .WithMany("Inventory")
                        .HasForeignKey("BranchId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Inventory_Branch");

                    b.Navigation("Branch");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.InventoryItem", b =>
                {
                    b.HasOne("Dolfin.Framework.Data.Domains.InventoryProduct", "InventoryProduct")
                        .WithMany("InventoryItem")
                        .HasForeignKey("InventoryProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_InventoryItem_InventoryProduct");

                    b.HasOne("Dolfin.Framework.Data.Domains.Product", "Product")
                        .WithMany("InventoryItem")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_InventoryItem_Product");

                    b.HasOne("Dolfin.Framework.Data.Domains.ProductUOM", "ProductUOM")
                        .WithMany("InventoryItem")
                        .HasForeignKey("ProductUOMId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_InventoryItem_ProductUOM");

                    b.Navigation("InventoryProduct");

                    b.Navigation("Product");

                    b.Navigation("ProductUOM");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.InventoryItemTransactionItem", b =>
                {
                    b.HasOne("Dolfin.Framework.Data.Domains.InventoryItem", "InventoryItem")
                        .WithMany("InventoryItemTransactionItem")
                        .HasForeignKey("InventoryItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_InventoryItemTransactionItem_Product");

                    b.HasOne("Dolfin.Framework.Data.Domains.TransactionItem", "TransactionItem")
                        .WithMany("InventoryItemTransactionItem")
                        .HasForeignKey("TransactionItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_InventoryItemTransactionItem_TransactionItem");

                    b.Navigation("InventoryItem");

                    b.Navigation("TransactionItem");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.InventoryProduct", b =>
                {
                    b.HasOne("Dolfin.Framework.Data.Domains.Inventory", "Inventory")
                        .WithMany("InventoryProduct")
                        .HasForeignKey("InventoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Dolfin.Framework.Data.Domains.Product", "Product")
                        .WithMany("InventoryProduct")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_InventoryProduct_Product");

                    b.Navigation("Inventory");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.PaymentType", b =>
                {
                    b.HasOne("Dolfin.Framework.Data.Domains.AccountGroup", "AccountGroup")
                        .WithMany("PaymentType")
                        .HasForeignKey("AccountGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_PaymentType_AccountGroup");

                    b.Navigation("AccountGroup");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Prefix", b =>
                {
                    b.HasOne("Dolfin.Framework.Data.Domains.Branch", "Branch")
                        .WithMany()
                        .HasForeignKey("BranchId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Prefix_Branch");

                    b.Navigation("Branch");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Product", b =>
                {
                    b.HasOne("Dolfin.Framework.Data.Domains.Classification", "Classification")
                        .WithMany("Product")
                        .HasForeignKey("ClassificationId")
                        .HasConstraintName("FK_Product_Classification");

                    b.HasOne("Dolfin.Framework.Data.Domains.Company", "Company")
                        .WithMany("Product")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Product_Company");

                    b.HasOne("Dolfin.Framework.Data.Domains.Currency", "Currency")
                        .WithMany("Product")
                        .HasForeignKey("CurrencyId")
                        .HasConstraintName("FK_Product_Currency");

                    b.HasOne("Dolfin.Framework.Data.Domains.TaxRate", "CustomSalesTaxNo")
                        .WithMany("ProductCustomSalesTaxNo")
                        .HasForeignKey("CustomSalesTaxNoId")
                        .HasConstraintName("FK_Product_CustomSalesTaxNo");

                    b.HasOne("Dolfin.Framework.Data.Domains.TaxRate", "CustomServiceTaxNo")
                        .WithMany("ProductCustomServiceTaxNo")
                        .HasForeignKey("CustomServiceTaxNoId")
                        .HasConstraintName("FK_Product_CustomServiceTaxNo");

                    b.HasOne("Dolfin.Framework.Data.Domains.ProductCategory", "ProductCategory")
                        .WithMany("Product")
                        .HasForeignKey("ProductCategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Product_ProductCategory");

                    b.HasOne("Dolfin.Framework.Data.Domains.ProductCostMethod", "ProductCostMethod")
                        .WithMany("Product")
                        .HasForeignKey("ProductCostMethodId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Product_ProductCostMethod");

                    b.Navigation("Classification");

                    b.Navigation("Company");

                    b.Navigation("Currency");

                    b.Navigation("CustomSalesTaxNo");

                    b.Navigation("CustomServiceTaxNo");

                    b.Navigation("ProductCategory");

                    b.Navigation("ProductCostMethod");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.ProductCategory", b =>
                {
                    b.HasOne("Dolfin.Framework.Data.Domains.Company", "Company")
                        .WithMany("ProductCategory")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_ProductCategory_Company");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.ProductPrice", b =>
                {
                    b.HasOne("Dolfin.Framework.Data.Domains.ProductUOM", "ProductUOM")
                        .WithMany("ProductPrice")
                        .HasForeignKey("ProductUOMId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_ProductPrice_ProductUOM");

                    b.Navigation("ProductUOM");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.ProductUOM", b =>
                {
                    b.HasOne("Dolfin.Framework.Data.Domains.Product", "Product")
                        .WithMany("ProductUOM")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_ProductUOM_Product");

                    b.HasOne("Dolfin.Framework.Data.Domains.UOM", "UomPrimary")
                        .WithMany("ProductUOMPrimary")
                        .HasForeignKey("UomPrimaryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_ProductUOM_UOM_Primary");

                    b.HasOne("Dolfin.Framework.Data.Domains.UOM", "UomSecondary")
                        .WithMany("ProductUOMSecondary")
                        .HasForeignKey("UomSecondaryId")
                        .HasConstraintName("FK_ProductUOM_UOM_Secondary");

                    b.Navigation("Product");

                    b.Navigation("UomPrimary");

                    b.Navigation("UomSecondary");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.RefreshToken", b =>
                {
                    b.HasOne("Dolfin.Framework.Data.Domains.CustomIdentity.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.RegionState", b =>
                {
                    b.HasOne("Dolfin.Framework.Data.Domains.Region", "Region")
                        .WithMany("RegionState")
                        .HasForeignKey("RegionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_RegionState_Region");

                    b.HasOne("Dolfin.Framework.Data.Domains.State", "State")
                        .WithMany("RegionState")
                        .HasForeignKey("StateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_RegionState_State");

                    b.Navigation("Region");

                    b.Navigation("State");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.RolePermission", b =>
                {
                    b.HasOne("Dolfin.Framework.Data.Domains.Role", "Role")
                        .WithMany("RolePermission")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_RolePermission_Role");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.State", b =>
                {
                    b.HasOne("Dolfin.Framework.Data.Domains.Country", "Country")
                        .WithMany("State")
                        .HasForeignKey("CountryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_State_Country");

                    b.Navigation("Country");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.TaxRate", b =>
                {
                    b.HasOne("Dolfin.Framework.Data.Domains.TaxCategories", "TaxCategory")
                        .WithMany("TaxRate")
                        .HasForeignKey("TaxCategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_TaxRate_TaxCategories");

                    b.Navigation("TaxCategory");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Transaction", b =>
                {
                    b.HasOne("Dolfin.Framework.Data.Domains.AccountGroup", "AccountGroup")
                        .WithMany("Transaction")
                        .HasForeignKey("AccountGroupId")
                        .HasConstraintName("FK_Transaction_AccountGroup");

                    b.HasOne("Dolfin.Framework.Data.Domains.Address", "BillingAddress")
                        .WithMany("TransactionBillingAddress")
                        .HasForeignKey("BillingAddressId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Transaction_Address_BillingAddress");

                    b.HasOne("Dolfin.Framework.Data.Domains.Branch", "Branch")
                        .WithMany("Transaction")
                        .HasForeignKey("BranchId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Transaction_Branch");

                    b.HasOne("Dolfin.Framework.Data.Domains.Customer", "Customer")
                        .WithMany("Transaction")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Transaction_Customer");

                    b.HasOne("Dolfin.Framework.Data.Domains.TaxRate", "SalesTaxNo")
                        .WithMany("TransactionSalesTaxNo")
                        .HasForeignKey("SalesTaxNoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Transaction_TaxRate_SalesTaxNo");

                    b.HasOne("Dolfin.Framework.Data.Domains.TaxRate", "ServiceTaxNo")
                        .WithMany("TransactionServiceTaxNo")
                        .HasForeignKey("ServiceTaxNoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Transaction_TaxRate_ServiceTaxNo");

                    b.HasOne("Dolfin.Framework.Data.Domains.Address", "ShippingAddress")
                        .WithMany("TransactionShippingAddress")
                        .HasForeignKey("ShippingAddressId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Transaction_Address_ShippingAddress");

                    b.HasOne("Dolfin.Framework.Data.Domains.Term", "Term")
                        .WithMany("Transaction")
                        .HasForeignKey("TermId")
                        .HasConstraintName("FK_Transaction_Term");

                    b.HasOne("Dolfin.Framework.Data.Domains.TransactionStatus", "TransactionStatus")
                        .WithMany("Transaction")
                        .HasForeignKey("TransactionStatusId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Transaction_TransactionStatus");

                    b.HasOne("Dolfin.Framework.Data.Domains.TransactionType", "TransactionType")
                        .WithMany("Transaction")
                        .HasForeignKey("TransactionTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Transaction_TransactionType");

                    b.HasOne("Dolfin.Framework.Data.Domains.CustomIdentity.ApplicationUser", "User")
                        .WithMany("Transaction")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Dolfin.Framework.Data.Domains.User", null)
                        .WithMany("Transaction")
                        .HasForeignKey("UserId1");

                    b.Navigation("AccountGroup");

                    b.Navigation("BillingAddress");

                    b.Navigation("Branch");

                    b.Navigation("Customer");

                    b.Navigation("SalesTaxNo");

                    b.Navigation("ServiceTaxNo");

                    b.Navigation("ShippingAddress");

                    b.Navigation("Term");

                    b.Navigation("TransactionStatus");

                    b.Navigation("TransactionType");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.TransactionItem", b =>
                {
                    b.HasOne("Dolfin.Framework.Data.Domains.Product", "Product")
                        .WithMany("TransactionItem")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_TransactionItem_Product");

                    b.HasOne("Dolfin.Framework.Data.Domains.ProductPrice", "ProductPrice")
                        .WithMany("TransactionItem")
                        .HasForeignKey("ProductPriceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_TransactionItem_ProductPrice");

                    b.HasOne("Dolfin.Framework.Data.Domains.ProductUOM", "ProductUOM")
                        .WithMany("TransactionItem")
                        .HasForeignKey("ProductUOMId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_TransactionItem_ProductUOM");

                    b.HasOne("Dolfin.Framework.Data.Domains.TaxRate", "SalesTaxNo")
                        .WithMany("TransactionItemSalesTaxNo")
                        .HasForeignKey("SalesTaxNoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_TransactionItem_TaxRate_SalesTaxNo");

                    b.HasOne("Dolfin.Framework.Data.Domains.TaxRate", "ServiceTaxNo")
                        .WithMany("TransactionItemServiceTaxNo")
                        .HasForeignKey("ServiceTaxNoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_TransactionItem_TaxRate_ServiceTaxNo");

                    b.HasOne("Dolfin.Framework.Data.Domains.Transaction", "Transaction")
                        .WithMany("TransactionItem")
                        .HasForeignKey("TrxId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_TransactionItem_Transaction");

                    b.Navigation("Product");

                    b.Navigation("ProductPrice");

                    b.Navigation("ProductUOM");

                    b.Navigation("SalesTaxNo");

                    b.Navigation("ServiceTaxNo");

                    b.Navigation("Transaction");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.TransactionPaid", b =>
                {
                    b.HasOne("Dolfin.Framework.Data.Domains.PaymentType", "PaymentType")
                        .WithMany("TransactionPaid")
                        .HasForeignKey("PaymentTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_TransactionPaid_Product");

                    b.HasOne("Dolfin.Framework.Data.Domains.Transaction", "Transaction")
                        .WithMany("TransactionPaid")
                        .HasForeignKey("TrxId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_TransactionPaid_Transaction");

                    b.Navigation("PaymentType");

                    b.Navigation("Transaction");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.UOM", b =>
                {
                    b.HasOne("Dolfin.Framework.Data.Domains.UOMCategories", "UOMCategory")
                        .WithMany("Uom")
                        .HasForeignKey("UOMCategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_UOM_UOMCategory");

                    b.Navigation("UOMCategory");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.User", b =>
                {
                    b.HasOne("Dolfin.Framework.Data.Domains.Branch", "Branch")
                        .WithMany()
                        .HasForeignKey("BranchId");

                    b.HasOne("Dolfin.Framework.Data.Domains.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId");

                    b.Navigation("Branch");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.UserRole", b =>
                {
                    b.HasOne("Dolfin.Framework.Data.Domains.Role", "Role")
                        .WithMany("UserRole")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_UserRole_Role");

                    b.HasOne("Dolfin.Framework.Data.Domains.User", "User")
                        .WithMany("UserRole")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_UserRole_User");

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Dolfin.Mobile.API.Models.Email", b =>
                {
                    b.HasOne("Dolfin.Mobile.API.Models.EmailAccount", "EmailAccount")
                        .WithMany("Emails")
                        .HasForeignKey("EmailAccountId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Emails_EmailAccount");

                    b.Navigation("EmailAccount");
                });

            modelBuilder.Entity("Dolfin.Mobile.API.Models.EmailTemplate", b =>
                {
                    b.HasOne("Dolfin.Mobile.API.Models.EmailAccount", "EmailAccount")
                        .WithMany("EmailTemplate")
                        .HasForeignKey("EmailAccountId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_EmailTemplate_EmailAccount");

                    b.Navigation("EmailAccount");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("Dolfin.Framework.Data.Domains.CustomIdentity.ApplicationRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("Dolfin.Framework.Data.Domains.CustomIdentity.ApplicationUser", null)
                        .WithMany("Claims")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("Dolfin.Framework.Data.Domains.CustomIdentity.ApplicationUser", null)
                        .WithMany("Logins")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("Dolfin.Framework.Data.Domains.CustomIdentity.ApplicationUser", null)
                        .WithMany("Tokens")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.AccountGroup", b =>
                {
                    b.Navigation("Customer");

                    b.Navigation("PaymentType");

                    b.Navigation("Transaction");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Address", b =>
                {
                    b.Navigation("Branch");

                    b.Navigation("Customer");

                    b.Navigation("TransactionBillingAddress");

                    b.Navigation("TransactionShippingAddress");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Branch", b =>
                {
                    b.Navigation("AccountGroupByPeriods");

                    b.Navigation("Inventory");

                    b.Navigation("Transaction");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Classification", b =>
                {
                    b.Navigation("Product");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Company", b =>
                {
                    b.Navigation("Branch");

                    b.Navigation("Cust");

                    b.Navigation("Product");

                    b.Navigation("ProductCategory");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Country", b =>
                {
                    b.Navigation("Address");

                    b.Navigation("State");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Currency", b =>
                {
                    b.Navigation("Company");

                    b.Navigation("Customer");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.CustomIdentity.ApplicationRole", b =>
                {
                    b.Navigation("UserRoles");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.CustomIdentity.ApplicationUser", b =>
                {
                    b.Navigation("Claims");

                    b.Navigation("Logins");

                    b.Navigation("Tokens");

                    b.Navigation("Transaction");

                    b.Navigation("UserRoles");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Customer", b =>
                {
                    b.Navigation("CustomerReferral");

                    b.Navigation("EInvoice");

                    b.Navigation("Transaction");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.DebtorType", b =>
                {
                    b.Navigation("Customer");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.IdentityType", b =>
                {
                    b.Navigation("Customer");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Inventory", b =>
                {
                    b.Navigation("InventoryProduct");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.InventoryItem", b =>
                {
                    b.Navigation("InventoryItemTransactionItem");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.InventoryProduct", b =>
                {
                    b.Navigation("InventoryItem");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Msic", b =>
                {
                    b.Navigation("Company");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.PaymentType", b =>
                {
                    b.Navigation("TransactionPaid");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Product", b =>
                {
                    b.Navigation("InventoryItem");

                    b.Navigation("InventoryProduct");

                    b.Navigation("ProductUOM");

                    b.Navigation("TransactionItem");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.ProductCategory", b =>
                {
                    b.Navigation("Product");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.ProductCostMethod", b =>
                {
                    b.Navigation("Product");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.ProductPrice", b =>
                {
                    b.Navigation("TransactionItem");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.ProductUOM", b =>
                {
                    b.Navigation("InventoryItem");

                    b.Navigation("ProductPrice");

                    b.Navigation("TransactionItem");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Region", b =>
                {
                    b.Navigation("Address");

                    b.Navigation("RegionState");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Role", b =>
                {
                    b.Navigation("RolePermission");

                    b.Navigation("UserRole");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.State", b =>
                {
                    b.Navigation("Address");

                    b.Navigation("RegionState");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Subscription", b =>
                {
                    b.Navigation("Company");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.TaxCategories", b =>
                {
                    b.Navigation("TaxRate");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.TaxRate", b =>
                {
                    b.Navigation("CompanyDefaultSalesTaxNo");

                    b.Navigation("CompanyDefaultServiceTaxNo");

                    b.Navigation("ProductCustomSalesTaxNo");

                    b.Navigation("ProductCustomServiceTaxNo");

                    b.Navigation("TransactionItemSalesTaxNo");

                    b.Navigation("TransactionItemServiceTaxNo");

                    b.Navigation("TransactionSalesTaxNo");

                    b.Navigation("TransactionServiceTaxNo");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Term", b =>
                {
                    b.Navigation("Transaction");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.Transaction", b =>
                {
                    b.Navigation("EInvoice");

                    b.Navigation("TransactionItem");

                    b.Navigation("TransactionPaid");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.TransactionItem", b =>
                {
                    b.Navigation("InventoryItemTransactionItem");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.TransactionStatus", b =>
                {
                    b.Navigation("Transaction");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.TransactionType", b =>
                {
                    b.Navigation("AccountGroupByPeriods");

                    b.Navigation("Transaction");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.UOM", b =>
                {
                    b.Navigation("ProductUOMPrimary");

                    b.Navigation("ProductUOMSecondary");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.UOMCategories", b =>
                {
                    b.Navigation("Uom");
                });

            modelBuilder.Entity("Dolfin.Framework.Data.Domains.User", b =>
                {
                    b.Navigation("Transaction");

                    b.Navigation("UserRole");
                });

            modelBuilder.Entity("Dolfin.Mobile.API.Models.EmailAccount", b =>
                {
                    b.Navigation("EmailTemplate");

                    b.Navigation("Emails");
                });
#pragma warning restore 612, 618
        }
    }
}
