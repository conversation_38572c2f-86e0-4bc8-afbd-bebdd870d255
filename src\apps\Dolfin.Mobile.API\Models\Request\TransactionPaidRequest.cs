﻿﻿using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Dolfin.Mobile.API.Models.Request
{
    /// <summary>
    /// Request model for creating a new transaction payment
    /// </summary>
    public class TransactionPaidRequest
    {
        /// <summary>
        /// The amount paid in this payment
        /// </summary>
        [Required]
        [Range(0.01, double.MaxValue, ErrorMessage = "Paid amount must be greater than 0")]
        public decimal PaidAmount { get; set; }

        /// <summary>
        /// The date and time when the payment was made
        /// </summary>
        //[Required]
        //public DateTime PaidAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// The ID of the payment type used for this payment
        /// </summary>
        [Required]
        public Guid PaymentTypeId { get; set; }

        /// <summary>
        /// The ID of the transaction this payment is for
        /// </summary>
        [Required]
        public Guid TrxId { get; set; }

        /// <summary>
        /// Optional remarks for this payment
        /// </summary>
        public string? Remark { get; set; }
        [JsonIgnore]
        public Guid? BranchId { get; set; }
    }
}
