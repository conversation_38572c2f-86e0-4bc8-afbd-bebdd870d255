﻿using Dolfin.Mobile.API.Models;
using Microsoft.AspNetCore.Identity;
using System.Security.Cryptography;
using System.Text;
using static Dolfin.Utility.Enum.Enums;

namespace Dolfin.Utility.Utils
{
    public class PasswordService
    {
        public PasswordService() { 
        }

        public static bool VerifyPassword<TUser>(TUser user, string passwordHash, string password) where TUser : class
        {
            bool success = true;

            var hasher = new PasswordHasher<TUser>();
            var result = hasher.VerifyHashedPassword(user, passwordHash, password);

            if (result == PasswordVerificationResult.Failed)
            {
                success = false;
            }

            return success;
        }

        public static string HashPassword<TUser>(TUser user, string password) where TUser : class
        {
            var hasher = new PasswordHasher<TUser>();
            return hasher.HashPassword(user, password);
        }

        public static string CreatePasswordHash(string password, string saltkey, string passwordFormat = "SHA1")
        {
            return CreateHash(Encoding.UTF8.GetBytes(String.Concat(password, saltkey)), passwordFormat);
        }

        private static string CreateHash(byte[] data, string hashAlgorithm = "SHA1")
        {
            if (String.IsNullOrEmpty(hashAlgorithm))
                hashAlgorithm = "SHA1";
            if (hashAlgorithm != "SHA1")
                throw new ArgumentException("Unrecognized hash name");

            //return FormsAuthentication.HashPasswordForStoringInConfigFile(saltAndPassword, passwordFormat);
            var algorithm = SHA1.Create();

            var hashByteArray = algorithm.ComputeHash(data);
            return BitConverter.ToString(hashByteArray).Replace("-", "");
        }

        public static string CreateSaltKey(int size = 5)
        {
            //generate a cryptographic random number
            //using (var provider = new RNGCryptoServiceProvider())
            using (var provider = RandomNumberGenerator.Create())
            {
                var buff = new byte[size];
                provider.GetBytes(buff);

                // Return a Base64 string representation of the random number
                return Convert.ToBase64String(buff);
            }
        }

        public static LoginRequestLocal HashPasswordSalt(string username, string password)
        {
            var saltKey = CreateSaltKey(5);
            var newPassword = CreatePasswordHash(password, saltKey, "SHA1");
            return new LoginRequestLocal()
            {
                Username = username,
                Password = newPassword,
                PasswordSalt = saltKey
            };
        }
    }
}
