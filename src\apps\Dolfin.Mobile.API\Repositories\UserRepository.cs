using Dolfin.Framework.Data.Domains.CustomIdentity;
using Dolfin.Framework.Data.Entity;
using Dolfin.Framework.Repository.Interfaces;
using Dolfin.Mobile.API.Constants;
using Dolfin.Mobile.API.Repositories.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Dolfin.Mobile.API.Repositories
{
    /// <summary>
    /// Repository implementation for ApplicationUser entity
    /// </summary>
    public class UserRepository : BaseRepository<ApplicationUser>, IUserRepository
    {
        /// <summary>
        /// Constructor
        /// </summary>
        public UserRepository(
            DolfinDbContext context,
            ICacheService cacheService,
            ILogger<UserRepository> logger)
            : base(context, cacheService, logger)
        {
        }

        /// <inheritdoc />
        public async Task<ApplicationUser?> GetUserByGuidAsync(string id)
        {
            string cacheKey = CacheKeys.User.GetCurrentUserKey(id);

            return await GetOrCreateAsync<ApplicationUser?>(
                cacheKey,
                async () => {
                    _logger.LogInformation("Loading user {UserId} from database", id);
                    return await _dbSet.AsQueryable()
                        .Include(u => u.UserRoles).ThenInclude(u => u.Role)
                        .Include(u => u.Company)
                        .Include(u => u.Branch)
                        .FirstOrDefaultAsync(x => x.Id == id && x.IsActive);
                });
        }

        /// <inheritdoc />
        public async Task<ApplicationUser?> GetUserByUsernameAsync(string username)
        {
            // Username lookups are not cached to ensure we always get the latest data
            // This is important for security-related operations like login
            var query = _dbSet.AsQueryable()
                .Include(u => u.UserRoles).ThenInclude(u => u.Role)
                .Include(u => u.Company)
                .Include(u => u.Branch)
                .Where(x => x.UserName != null &&
                      x.IsActive &&
                      EF.Functions.ILike(x.UserName, username));

            return await query.FirstOrDefaultAsync();
        }

        /// <inheritdoc />
        public async Task<List<ApplicationUser>> GetUsersByBranchIdAsync(Guid branchId)
        {
            string cacheKey = CacheKeys.User.GetUsersByBranchKey(branchId);

            return await GetOrCreateAsync<List<ApplicationUser>>(
                cacheKey,
                async () => {
                    _logger.LogInformation("Loading users for branch {BranchId} from database", branchId);
                    return await _dbSet.AsQueryable()
                        .Include(u => u.UserRoles).ThenInclude(u => u.Role)
                        .Include(u => u.Company)
                        .Include(u => u.Branch).ThenInclude(b => b.Address) // Include Address to prevent lazy loading
                        .Where(x => x.BranchId == branchId && x.IsActive)
                        .ToListAsync();
                });
        }

        /// <inheritdoc />
        public async Task<List<ApplicationUser>> GetUserListAsync(string[] userIds)
        {
            var query = _dbSet.AsQueryable()
                .Include(u => u.UserRoles).ThenInclude(u => u.Role)
                .Include(u => u.Company)
                .Include(u => u.Branch)
                .Where(x => userIds.Contains(x.Id) && x.IsActive);

            return await query.ToListAsync();
        }

        /// <inheritdoc />
        public void InvalidateUserCache(string userId)
        {
            // Get the user to find their branch ID
            var user = _dbSet.AsNoTracking()
                .FirstOrDefault(u => u.Id == userId);

            // Invalidate user-specific cache
            string userCacheKey = CacheKeys.User.GetCurrentUserKey(userId);
            InvalidateCache(userCacheKey);

            // If user has a branch, invalidate branch users cache too
            if (user?.BranchId.HasValue == true)
            {
                string branchCacheKey = CacheKeys.User.GetUsersByBranchKey(user.BranchId.Value);
                InvalidateCache(branchCacheKey);
                _logger.LogInformation("Invalidated branch users cache for branch {BranchId}", user.BranchId.Value);
            }

            _logger.LogInformation("Invalidated cache for user {UserId}", userId);
        }
    }
}
