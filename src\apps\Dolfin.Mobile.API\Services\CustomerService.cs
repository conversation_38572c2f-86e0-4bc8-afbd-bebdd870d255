﻿using AutoMapper;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.InkML;
using DocumentFormat.OpenXml.Wordprocessing;
using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Domains.CustomIdentity;
using Dolfin.Framework.Data.Entity;
using Dolfin.Framework.Data.Migrations;
using Dolfin.Mobile.API.Helper;
using Dolfin.Mobile.API.Models.Request;
using Dolfin.Utility.Enum;
using Dolfin.Utility.Models;
using Dolfin.Utility.Utils;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Linq.Expressions;
using static Dolfin.Utility.Constant.Constant;
using static Dolfin.Utility.Enum.Enums;

namespace Dolfin.Mobile.API.Services
{
    public class CustomerService : BaseComponent<Customer>, ICustomerService
    {
        private readonly StandardMessage _standardMessage;
        private readonly IUserService _userService;
        private readonly IPrefixService _prefixService;
        private readonly ILogger<CustomerService> _logger;
        private readonly IMapper _mapper;
        private readonly Random _random;

        public CustomerService(
            DbContextOptions<DolfinDbContext> dbContextOptions,
            ISettingsService settingService,
            IUserService userService,
            IPrefixService prefixService,
            ILoggerFactory loggerFactory,
            IMapper mapper) : base(dbContextOptions)
        {
            _standardMessage = new StandardMessage();
            _userService = userService;
            _prefixService = prefixService;
            _logger = loggerFactory.CreateLogger<CustomerService>();
            _mapper = mapper;
            _random = new Random();
        }

        /// <summary>
        /// Generates a unique random referral code for a customer
        /// </summary>
        /// <returns>A unique referral code</returns>
        public async Task<string> GenerateUniqueReferralCodeAsync()
        {
            using var context = GetDbContext();

            string referralCode;
            bool isUnique = false;
            int attempts = 0;
            const int maxAttempts = 50; // Increased max attempts since we're not using timestamp fallback

            do
            {
                // Generate a random alphanumeric code
                referralCode = GenerateRandomCode();

                // Check if the code already exists
                isUnique = !await context.Set<Customer>()
                    .AnyAsync(c => c.ReferralCode == referralCode);

                attempts++;

                // If we've reached max attempts, just try with a longer code
                if (attempts >= maxAttempts && !isUnique)
                {
                    // Generate a longer code for more uniqueness
                    referralCode = GenerateRandomCode(12);
                    isUnique = !await context.Set<Customer>()
                        .AnyAsync(c => c.ReferralCode == referralCode);
                }
            }
            while (!isUnique && attempts < maxAttempts * 2); // Double max attempts as safety

            return referralCode;
        }

        /// <summary>
        /// Generates a random alphanumeric code
        /// </summary>
        /// <param name="length">Length of the code (default: 8)</param>
        /// <returns>Random alphanumeric code</returns>
        private string GenerateRandomCode(int length = 8)
        {
            const string chars = "ABCDEFGHJKLMNPQRSTUVWXYZ23456789"; // Removed confusing characters like I, O, 0, 1
            return new string(Enumerable.Repeat(chars, length)
                .Select(s => s[_random.Next(s.Length)]).ToArray());
        }

        #region Customer
        public async Task<BaseResponse<PagedList<Customer>>> GetCustomerList(Pagination pagination = null, CommonFilterList filterList = null, Guid? companyId = null)
        {
            var result = new BaseResponse<PagedList<Customer>> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync(companyId);
                var currentUser = getCurrentUser.Item1;
                companyId = getCurrentUser.Item2;
                var branchId = currentUser.BranchId;

                Expression<Func<Customer, bool>> predicate = x => (x.CompanyId == companyId || (companyId == null && SharedFunctionHelper.PermissionViewLimitCompanyOrBranchAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                        && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId))));

                if (filterList != null && filterList.FilterList != null && filterList.FilterList.Count > 0)
                    predicate = ExpressionExtensions.BuildPredicate(predicate, filterList);

                var query = GetDbContext().Set<Customer>()
                .Include(p => p.DebtorType)
                .Include(p => p.Address)
                .Include(p => p.Currency)
                .Include(p => p.Company).ThenInclude(p => p.Branch)
                .Where(predicate) // Apply the main predicate at the database level
                .AsQueryable();

                var response = await query.ToListAsync();

                result.Result = SharedFunctionHelper.StandardPagination(response, pagination);
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<PagedList<Customer>, PagedList<Customer>>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<Customer>> GetCustomerByGuid(Guid customerId)
        {
            var result = new BaseResponse<Customer> { IsSuccessful = true };
            try
            {

                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
                var currentUser = getCurrentUser.Item1;
                var branchId = currentUser.BranchId;

                var query = GetDbContext().Set<Customer>()
                .Include(p => p.DebtorType)
                .Include(p => p.Address)
                .Include(p => p.Currency)
                .Include(p => p.Company)
                .Where(x => x.Id == customerId && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                .AsQueryable();

                var response = await query.ToListAsync();

                result.Result = response.FirstOrDefault();
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<Customer, Customer>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<ResultId>> InsertCustomer(CustomerRequest reqBody, DolfinDbContext dbContextRollback = null)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };

            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();
            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;
                try
                {
                    var getCurrentUser = await _userService.GetCurrentUserStdUpsertWithCompanyAsync(reqBody);
                    var currentUser = getCurrentUser.Item1;
                    reqBody = getCurrentUser.Item2;
                    var companyId = currentUser.CompanyId;

                    // Get the branch ID from the current user
                    var branchId = currentUser.BranchId ?? throw new Exception("User does not have a branch assigned");

                    //// Verify that the branch exists
                    //var branchExists = await dbContext.Branch
                    //    .AnyAsync(b => b.Id == branchId && b.IsActive);

                    //if (!branchExists)
                    //{
                    //    throw new Exception($"Branch with ID {branchId} does not exist or is not active.");
                    //}

                    // Create the Address first if provided
                    Guid? addressId = null;
                    if (reqBody.Address != null)
                    {
                        var address = _mapper.Map<Address>(reqBody.Address);

                        // Ensure Email is set (it's required in Address)
                        address.Email = reqBody.Email ?? throw new Exception($"Email with Insert Customer email instead of no email.");

                        address.IsActive = true;
                        address.CreatedAt = DateTime.UtcNow;
                        address.CreatedBy = Guid.Parse(currentUser.Id);
                        var createdAddress = await CreateAsync(address, dbContext);
                        addressId = createdAddress.Id;
                    }

                    // Now map the customer
                    var newCustomer = _mapper.Map<Customer>(reqBody);

                    // Set the AddressId if we have one
                    if (addressId.HasValue)
                    {
                        newCustomer.AddressId = addressId.Value;
                    }

                    // Generate a unique code for the customer using the prefix service
                    newCustomer.Code = await _prefixService.GenerateCode("Customer", (Guid)branchId, dbContext);

                    // Generate a unique referral code
                    newCustomer.ReferralCode = await GenerateUniqueReferralCodeAsync();

                    newCustomer.IsActive = true;
                    newCustomer.CompanyId = (Guid)companyId;
                    newCustomer.CreatedAt = DateTime.UtcNow;
                    newCustomer.CreatedBy = Guid.Parse(currentUser.Id);
                    var customer = await CreateAsync(newCustomer, dbContext);
                    result.Result = new ResultId { Id = customer.Id };

                    if (currentTransaction == null)
                        await transaction.CommitAsync();
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }
            return result;
        }

        public async Task<BaseResponse<ResultId>> UpdateCustomer(UpdateCustomerRequest reqBody)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdUpsertWithCompanyAsync(reqBody);
                var currentUser = getCurrentUser.Item1;
                reqBody = getCurrentUser.Item2;

                var customerResponse = await GetCustomerByGuid(reqBody.Id);
                if (!customerResponse.IsSuccessful)
                    throw new Exception(customerResponse.Exception);
                else if (customerResponse == null || customerResponse.Result?.Id == null)
                    throw new Exception($"{reqBody.Id} not found.");
                else
                {
                    // Get the existing customer entity
                    var existingCustomer = customerResponse.Result;

                    // Get a database context
                    using var dbContext = GetDbContext();

                    // Use the utility method to update only non-null properties
                    // Exclude properties that should not be updated
                    // Capture the returned updated entity
                    existingCustomer = SharedFunctionHelper.UpdateEntityFromDto(
                        dbContext,
                        existingCustomer,
                        reqBody,
                        "Id", "CompanyId", "Code"); // Exclude these properties from updates

                    // Update audit fields
                    existingCustomer.UpdatedAt = DateTime.UtcNow;
                    existingCustomer.UpdatedBy = Guid.Parse(currentUser.Id);

                    // Save the updated customer
                    await UpdateAsync(existingCustomer);
                    result.Result = new ResultId { Id = existingCustomer.Id };
                }
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<NoResultResponse> DeleteCustomer(Guid id)
        {
            var result = new NoResultResponse { IsSuccessful = true };
            try
            {
                var customer = await GetCustomerByGuid(id);
                if (!customer.IsSuccessful)
                    throw new Exception(customer.Exception);
                if (customer == null || customer.Result?.Id == null)
                    throw new Exception($"{id} not found.");

                var currentUser = await _userService.GetCurrentUserAsync();
                if (currentUser.UserRoles.Any(x => !x.RoleId.Equals(UserTypeEnum.Admin.GetAmbientValue().Value.ToString())) && currentUser.CompanyId != customer.Result.CompanyId)
                    throw new Exception($"{id} not found.");

                var deleteCustomer = customer.Result;
                deleteCustomer.IsActive = false;
                deleteCustomer.UpdatedAt = DateTime.UtcNow;
                deleteCustomer.UpdatedBy = Guid.Parse(currentUser.Id);
                await UpdateAsync(deleteCustomer);
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<NoResult, NoResult>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }
        #endregion

        /// <summary>
        /// Checks if a phone number already exists for any customer in the company
        /// </summary>
        /// <param name="phoneNo">The phone number to check</param>
        /// <param name="companyId">Optional company ID to scope the check to. If null, uses current user's company</param>
        /// <returns>BaseResponse with Result=true if the phone number exists, false otherwise</returns>
        public async Task<bool> IsPhoneNumberExistsAsync(string phoneNo, Guid companyId, DolfinDbContext dbContextRollback = null)
        {
            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();

            try
            {
                var customers = await dbContext.Set<Customer>()
                                .Include(p => p.Address)
                                .Where(x => x.CompanyId == companyId &&
                                            x.IsActive &&
                                            x.Address != null &&
                                            x.Address.IsActive)
                                .ToListAsync();
                string normalizedPhoneNo = new string(phoneNo.Where(char.IsDigit).ToArray());
                var exists = false;

                if (customers.Any(x => x.Address != null && x.Address?.Id != null))
                exists = customers.Any(x =>
                    new string(x.Address.PhoneNo.Where(char.IsDigit).ToArray()) == normalizedPhoneNo);

                return exists;
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }
        }

        public async Task<BaseResponse<PagedList<Term>>> GetTermList(Pagination pagination = null)
        {
            var result = new BaseResponse<PagedList<Term>> { IsSuccessful = true };
            try
            {
                UserTypeEnum? userType = null;
                var currentUser = await _userService.GetCurrentUserAsync();
                if (currentUser != null)
                    userType = Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId));

                var response = await GetDbContext().Set<Term>()
                                .ToListAsync();
                result.Result = SharedFunctionHelper.StandardPagination(response, pagination);
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<PagedList<Term>, PagedList<Term>>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<Term>> GetTermByGuid(Guid termId)
        {
            var result = new BaseResponse<Term> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserAsync();
                UserTypeEnum? userType = null;
                if (getCurrentUser != null)
                    userType = Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(getCurrentUser.UserRoles.First().RoleId));

                var query = GetDbContext().Set<Term>()
                    .Where(x => x.Id == termId &&
                           (x.IsActive || (userType != null && SharedFunctionHelper.PermissionViewAll((UserTypeEnum)userType))))
                    .AsQueryable();

                result.Result = await query.FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<Term, Term>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<Address>> GetAddressByGuid(Guid addressId)
        {
            var result = new BaseResponse<Address> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserAsync();
                UserTypeEnum? userType = null;
                if (getCurrentUser != null)
                    userType = Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(getCurrentUser.UserRoles.First().RoleId));

                var query = GetDbContext().Set<Address>()
                    .Where(x => x.Id == addressId &&
                           (x.IsActive || (userType != null && SharedFunctionHelper.PermissionViewAll((UserTypeEnum)userType))))
                    .AsQueryable();

                result.Result = await query.FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<Address, Address>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }
    }
}
