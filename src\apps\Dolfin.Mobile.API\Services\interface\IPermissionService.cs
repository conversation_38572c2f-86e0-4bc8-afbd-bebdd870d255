﻿﻿using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Dolfin.Mobile.API.Services
{
    /// <summary>
    /// Service for managing and validating permissions
    /// </summary>
    public interface IPermissionService
    {
        /// <summary>
        /// Checks if a user has a specific permission
        /// </summary>
        /// <param name="user">The claims principal representing the user</param>
        /// <param name="permission">The permission to check</param>
        /// <returns>True if the user has the permission, false otherwise</returns>
        Task<bool> UserHasPermissionAsync(ClaimsPrincipal user, string permission);

        /// <summary>
        /// Checks if a user has any of the specified permissions
        /// </summary>
        /// <param name="user">The claims principal representing the user</param>
        /// <param name="permissions">The permissions to check</param>
        /// <returns>True if the user has any of the permissions, false otherwise</returns>
        Task<bool> UserHasAnyPermissionAsync(ClaimsPrincipal user, params string[] permissions);

        /// <summary>
        /// Checks if a user has all of the specified permissions
        /// </summary>
        /// <param name="user">The claims principal representing the user</param>
        /// <param name="permissions">The permissions to check</param>
        /// <returns>True if the user has all of the permissions, false otherwise</returns>
        Task<bool> UserHasAllPermissionsAsync(ClaimsPrincipal user, params string[] permissions);

        /// <summary>
        /// Gets all permissions for a user
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>A list of permission codes</returns>
        Task<List<string>> GetUserPermissionsAsync(string userId);

        /// <summary>
        /// Gets all permissions for a role
        /// </summary>
        /// <param name="roleId">The role ID</param>
        /// <returns>A list of permission codes</returns>
        Task<List<string>> GetRolePermissionsAsync(string roleId);

        /// <summary>
        /// Adds a permission to a role
        /// </summary>
        /// <param name="roleId">The role ID</param>
        /// <param name="permission">The permission to add</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> AddPermissionToRoleAsync(string roleId, string permission);

        /// <summary>
        /// Removes a permission from a role
        /// </summary>
        /// <param name="roleId">The role ID</param>
        /// <param name="permission">The permission to remove</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> RemovePermissionFromRoleAsync(string roleId, string permission);

        /// <summary>
        /// Adds a permission directly to a user
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="permission">The permission to add</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> AddPermissionToUserAsync(string userId, string permission);

        /// <summary>
        /// Removes a permission from a user
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="permission">The permission to remove</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> RemovePermissionFromUserAsync(string userId, string permission);
    }
}
