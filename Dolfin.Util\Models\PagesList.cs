﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Dolfin.Utility.Models
{
	public class PagedList<T> : List<T>
	{
		public int CurrentPage { get; private set; }
		public int TotalPages { get; private set; }
		public int PageSize { get; private set; }
		public int TotalCount { get; private set; }

		public bool HasPrevious => CurrentPage > 1;
		public bool HasNext => CurrentPage < TotalPages;

		public PagedList(List<T> items, int count, int pageNumber, int pageSize)
		{
			TotalCount = count;
			PageSize = pageSize;
			CurrentPage = pageSize == 0 ? pageSize : pageNumber;
			TotalPages = pageSize == 0 ? pageSize : (int)Math.Ceiling(count / (double)pageSize);
			
			AddRange(items);
		}

		//implement when update column after skip and take pagination done
		public static PagedList<T> ToPagedList(List<T> items, int count, int pageNumber, int pageSize)
		{
			return new PagedList<T>(items, count, pageNumber, pageSize);
		}

		public static PagedList<T> ToPagedList(List<T> item, int pageNumber, int pageSize)
		{
			var count = item.Count();
			if (pageSize != 0)
			{
				item = item.Skip((pageNumber - 1) * pageSize).Take(pageSize).ToList();
			}
			return new PagedList<T>(item, count, pageNumber, pageSize);
		}

		public static object PagedMetadata(BaseResponse<PagedList<T>> response)
		{
			if (response.Result != null)
				return new
				{
					response.Result.TotalCount,
					response.Result.PageSize,
					response.Result.CurrentPage,
					response.Result.TotalPages,
					response.Result.HasNext,
					response.Result.HasPrevious
				};
			else
				return null;
		}

		public static object PagedMetadata(PagedList<T> response)
		{
			return new
			{
				response.TotalCount,
				response.PageSize,
				response.CurrentPage,
				response.TotalPages,
				response.HasNext,
				response.HasPrevious
			};
		}
	}
}