﻿using Dolfin.Mobile.API.Scheduler;
using Hangfire.Annotations;
using Hangfire.Server;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.DependencyInjection;
using Dolfin.Framework.Data.Entity;

namespace Dolfin.Mobile.API.Scheduler
{
    internal class SendEmailJob : IBackgroundProcess
    {
        private readonly SendEmailJobSetting _jobSettings;
        private readonly ILogger _logger;
        private readonly IServiceProvider _serviceProvider;

        public SendEmailJob(
            ILogger<SendEmailJob> logger,
            IOptions<SendEmailJobSetting> emailJobSettings,
            IServiceProvider serviceProvider)
        {
            _logger = logger;
            _jobSettings = emailJobSettings.Value;
            _serviceProvider = serviceProvider;
        }

        public void Execute([NotNull] BackgroundProcessContext context)
        {
            if (TimeSpan.TryParse(_jobSettings.TimeSpanDelay, formatProvider: null, out TimeSpan delayDuration))
            {
                try
                {
                    _logger.LogInformation($"{nameof(SendEmailJob)} Job Started");

                    // Create a scope to resolve scoped services
                    using (var scope = _serviceProvider.CreateScope())
                    {
                        var scopedProvider = scope.ServiceProvider;

                        // Get the email service from the scoped provider
                        var emailService = scopedProvider.GetRequiredService<EmailSchedulerService>();

                        // Execute the batch operation
                        emailService.SendEmailBatch(_jobSettings.BatchSize, null);
                    }

                    _logger.LogInformation($"{nameof(SendEmailJob)} Job Completed");
                    Thread.Sleep(delayDuration);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"{nameof(SendEmailJob)} Error executing job: {ex.Message}");
                }
            }
        }
    }

    //internal class SendEmailJobSetting : BackgroundJobOption
    //{
    //    public int BatchSize { get; set; }
    //}
}