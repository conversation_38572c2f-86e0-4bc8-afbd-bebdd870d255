﻿using Dolfin.Framework.Data.Domains;

namespace Dolfin.Mobile.API.Models.Dto
{
        public partial class ProductCategoryDto
        {
            public ProductCategoryDto()
            {
                //Product = new HashSet<Product>();
            }
            public Guid Id { get; set; }
            public string Code { get; set; }
            public string Name { get; set; }
            public string? Description { get; set; }
            public Guid CompanyId { get; set; }
            public int? DisplayOrder { get; set; }
            public bool Published { get; set; }
            public virtual CompanyDto Company { get; set; }
            //public virtual ICollection<Product> Product { get; }
        }
}
