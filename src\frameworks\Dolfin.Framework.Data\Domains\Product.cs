﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Framework.Data.Domains
{
    public partial class Product : _BaseDomain
    {
        public Product()
        {
            InventoryProduct = new HashSet<InventoryProduct>();
            InventoryItem = new HashSet<InventoryItem>();
            ProductUOM = new HashSet<ProductUOM>();
            TransactionItem = new HashSet<TransactionItem>();
        }
        public required string Code { get; set; }
        public required string Name { get; set; }
        public string? Description { get; set; }
        public bool IsTaxExempt { get; set; }
        public bool IsTaxExcl { get; set; }
        public Guid? CustomSalesTaxNoId { get; set; }
        public Guid? CustomServiceTaxNoId { get; set; }
        public required string Sku { get; set; }
        public string? Image { get; set; }
        public decimal? Weight { get; set; }
        public decimal? Length { get; set; }
        public decimal? Width { get; set; }
        public decimal? Height { get; set; }
        public required string AccountCode { get; set; }
        public DateTime? AvailableStartAt { get; set; }
        public DateTime? AvailableEndAt { get; set; }
        public int? DisplayOrder { get; set; }
        public bool Published { get; set; }
        public Guid CompanyId { get; set; }
        public Guid ProductCategoryId { get; set; }
        public Guid ProductCostMethodId { get; set; }
        public Guid? ClassificationId { get; set; }
        public Guid? CurrencyId { get; set; }
        public virtual Company Company { get; set; }
        public virtual ProductCategory ProductCategory { get; set; }
        public virtual ProductCostMethod ProductCostMethod { get; set; }
        public virtual TaxRate CustomSalesTaxNo { get; set; }
        public virtual TaxRate CustomServiceTaxNo { get; set; }
        public virtual Classification Classification { get; set; }
        public virtual Currency Currency { get; set; }
        public virtual ICollection<InventoryProduct> InventoryProduct { get; set; }
        public virtual ICollection<InventoryItem> InventoryItem { get; }
        public virtual ICollection<ProductUOM> ProductUOM { get; set; }
        public virtual ICollection<TransactionItem> TransactionItem { get; }

    }
}
