﻿using Dolfin.Mobile.API.Helper;
using System;
using System.Collections.Generic;
using System.Text;

namespace Dolfin.Mobile.API.Models.Request
{
    public class RecurringJobOption : IValidator
    {
        public string CronExpression { get; set; }

        public virtual bool IsValid()
        {
            //TODO : Fix more on the regex to validate before registering
            return !string.IsNullOrWhiteSpace(CronExpression);
            //var r = new Regex(@"/^(\*|([0-9]|1[0-9]|2[0-9]|3[0-9]|4[0-9]|5[0-9])|\*\/([0-9]|1[0-9]|2[0-9]|3[0-9]|4[0-9]|5[0-9])) (\*|([0-9]|1[0-9]|2[0-3])|\*\/([0-9]|1[0-9]|2[0-3])) (\*|([1-9]|1[0-9]|2[0-9]|3[0-1])|\*\/([1-9]|1[0-9]|2[0-9]|3[0-1])) (\*|([1-9]|1[0-2])|\*\/([1-9]|1[0-2])) (\*|([0-6])|\*\/([0-6]))$/");
            //return r.IsMatch(CronExpression);
        }
    }
}
