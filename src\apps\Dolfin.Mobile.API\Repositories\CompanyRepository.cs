using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Entity;
using Dolfin.Framework.Repository.Interfaces;
using Dolfin.Mobile.API.Constants;
using Dolfin.Mobile.API.Repositories.Interfaces;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Dolfin.Mobile.API.Repositories
{
    /// <summary>
    /// Repository implementation for Company entity
    /// </summary>
    public class CompanyRepository : BaseRepository<Company>, ICompanyRepository
    {
        /// <summary>
        /// Constructor
        /// </summary>
        public CompanyRepository(
            DolfinDbContext context,
            ICacheService cacheService,
            ILogger<CompanyRepository> logger)
            : base(context, cacheService, logger)
        {
        }

        /// <inheritdoc />
        public async Task<Company> GetCompanyWithDetailsAsync(Guid companyId)
        {
            string cacheKey = CacheKeys.Company.GetCompanyKey(companyId);

            return await GetOrCreateAsync<Company>(
                cacheKey,
                async () => {
                    _logger.LogInformation("Loading company {CompanyId} with details from database", companyId);
                    return await _context.Company
                        .Include(x => x.DefaultSalesTaxNo).ThenInclude(x => x.TaxCategory)
                        .Include(x => x.DefaultServiceTaxNo).ThenInclude(x => x.TaxCategory)
                        .Include(x => x.Currency)
                        .Include(x => x.User)
                        .Include(x => x.Branch).ThenInclude(b => b.Address) // Include Address to prevent lazy loading
                        .FirstOrDefaultAsync(c => c.Id == companyId && c.IsActive);
                });
        }

        /// <inheritdoc />
        public async Task<Company> GetCompanyByUserIdAsync(string userId)
        {
            return await _context.Company
                .Include(x => x.DefaultSalesTaxNo).ThenInclude(x => x.TaxCategory)
                .Include(x => x.DefaultServiceTaxNo).ThenInclude(x => x.TaxCategory)
                .Include(x => x.Currency)
                .Include(x => x.Branch)
                .FirstOrDefaultAsync(c => c.User.Any(u => u.Id == userId) && c.IsActive);
        }

        /// <inheritdoc />
        public async Task<IEnumerable<Branch>> GetBranchesByCompanyIdAsync(Guid companyId)
        {
            return await _context.Branch
                .Where(b => b.CompanyId == companyId && b.IsActive)
                .ToListAsync();
        }

        /// <inheritdoc />
        public async Task<Branch> GetBranchWithDetailsAsync(Guid branchId)
        {
            return await _context.Branch
                .Include(b => b.Company)
                .FirstOrDefaultAsync(b => b.Id == branchId && b.IsActive);
        }

        /// <inheritdoc />
        public void InvalidateCompanyCache(Guid companyId)
        {
            string cacheKey = CacheKeys.Company.GetCompanyKey(companyId);
            InvalidateCache(cacheKey);
            _logger.LogInformation("Invalidated cache for company {CompanyId}", companyId);
        }
    }
}
