using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Domains.CustomIdentity;
using Dolfin.Framework.Data.Entity;
using Dolfin.Framework.Repository.Extensions;
using Dolfin.Framework.Repository.Interfaces;
using Dolfin.Mobile.API.Repositories;
using Dolfin.Mobile.API.Repositories.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Dolfin.Mobile.API.Extensions
{
    /// <summary>
    /// Extension methods for registering repository services
    /// </summary>
    public static class RepositoryServiceExtensions
    {
        /// <summary>
        /// Add repository services to the service collection
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <param name="configuration">The configuration</param>
        /// <returns>The service collection</returns>
        public static IServiceCollection AddRepositoryServices(this IServiceCollection services, IConfiguration configuration)
        {
            // Register repository framework
            services.AddRepositoryFramework<DolfinDbContext>(configuration);

            // Register repositories
            services.AddScoped<IRepository<Product>, BaseRepository<Product>>();
            services.AddScoped<IRepository<Company>, BaseRepository<Company>>();
            services.AddScoped<IRepository<Customer>, BaseRepository<Customer>>();
            services.AddScoped<IRepository<ApplicationUser>, BaseRepository<ApplicationUser>>();

            // Register entity-specific repositories
            services.AddScoped<IProductRepository, ProductRepository>();
            services.AddScoped<ICompanyRepository, CompanyRepository>();
            services.AddScoped<ICustomerRepository, CustomerRepository>();
            services.AddScoped<IUserRepository, UserRepository>();
            services.AddScoped<ILookupGroupRepository, LookupGroupRepository>();
            services.AddScoped<ILookupItemRepository, LookupItemRepository>();

            return services;
        }
    }
}
