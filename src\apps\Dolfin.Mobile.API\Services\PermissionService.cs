﻿using Dolfin.Framework.Data.Domains.CustomIdentity;
using Dolfin.Framework.Repository.Interfaces;
using Dolfin.Mobile.API.Constants;
using Dolfin.Mobile.API.Repositories.Interfaces;
using Dolfin.Utility.Enum;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using static Dolfin.Utility.Enum.Enums;

namespace Dolfin.Mobile.API.Services
{
    /// <summary>
    /// Service for managing and validating permissions
    /// </summary>
    public class PermissionService : IPermissionService
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly RoleManager<ApplicationRole> _roleManager;
        private readonly IMemoryCache _cache;
        private readonly ILogger<PermissionService> _logger;
        private const string PERMISSION_CLAIM_TYPE = "permission";

        /// <summary>
        /// Constructor
        /// </summary>
        public PermissionService(
            UserManager<ApplicationUser> userManager,
            RoleManager<ApplicationRole> roleManager,
            IMemoryCache cache,
            ILogger<PermissionService> logger)
        {
            _userManager = userManager;
            _roleManager = roleManager;
            _cache = cache;
            _logger = logger;
        }

        /// <summary>
        /// Checks if a user has a specific permission
        /// </summary>
        /// <param name="user">The claims principal representing the user</param>
        /// <param name="permission">The permission to check</param>
        /// <returns>True if the user has the permission, false otherwise</returns>
        public async Task<bool> UserHasPermissionAsync(ClaimsPrincipal user, string permission)
        {
            try
            {
                // Get the user ID from the claims
                var userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userId))
                {
                    _logger.LogWarning("User ID not found in claims");
                    return false;
                }

                // Check if the user has the permission claim directly
                if (user.HasClaim(c => c.Type == PERMISSION_CLAIM_TYPE && c.Value == permission))
                {
                    return true;
                }

                // Get the user's permissions from the database or cache
                var userPermissions = await GetUserPermissionsAsync(userId);

                // Check if the user has the required permission
                return userPermissions.Contains(permission);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking user permission: {Permission}", permission);
                return false;
            }
        }

        /// <summary>
        /// Checks if a user has any of the specified permissions
        /// </summary>
        /// <param name="user">The claims principal representing the user</param>
        /// <param name="permissions">The permissions to check</param>
        /// <returns>True if the user has any of the permissions, false otherwise</returns>
        public async Task<bool> UserHasAnyPermissionAsync(ClaimsPrincipal user, params string[] permissions)
        {
            if (permissions == null || permissions.Length == 0)
            {
                return true; // No permissions required
            }

            // Check if the user has super admin role (has all permissions)
            var isSuperAdmin = user.IsInRole(UserTypeEnum.SuperAdmin.GetDescription());

            if (isSuperAdmin)
            {
                return true;
            }

            // Check each permission
            foreach (var permission in permissions)
            {
                if (await UserHasPermissionAsync(user, permission))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// Checks if a user has all of the specified permissions
        /// </summary>
        /// <param name="user">The claims principal representing the user</param>
        /// <param name="permissions">The permissions to check</param>
        /// <returns>True if the user has all of the permissions, false otherwise</returns>
        public async Task<bool> UserHasAllPermissionsAsync(ClaimsPrincipal user, params string[] permissions)
        {
            if (permissions == null || permissions.Length == 0)
            {
                return true; // No permissions required
            }

            // Check if the user has admin role (has all permissions)
            var isAdmin = user.IsInRole(UserTypeEnum.Admin.GetDescription()) ||
                          user.IsInRole(UserTypeEnum.SuperAdmin.GetDescription());

            if (isAdmin)
            {
                return true;
            }

            // Check each permission
            foreach (var permission in permissions)
            {
                if (!await UserHasPermissionAsync(user, permission))
                {
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// Gets all permissions for a user
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>A list of permission codes</returns>
        public async Task<List<string>> GetUserPermissionsAsync(string userId)
        {
            // Try to get permissions from cache first
            var cacheKey = CacheKeys.Permission.GetUserPermissionsKey(userId);
            if (_cache.TryGetValue(cacheKey, out List<string> cachedPermissions))
            {
                return cachedPermissions;
            }

            try
            {
                // Get the user
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null)
                {
                    _logger.LogWarning("User not found: {UserId}", userId);
                    return new List<string>();
                }

                // Get the user's roles
                var roles = await _userManager.GetRolesAsync(user);

                // Get permissions for each role
                var permissions = new List<string>();

                foreach (var roleName in roles)
                {
                    var role = await _roleManager.FindByNameAsync(roleName);
                    if (role != null)
                    {
                        // Get role claims
                        var roleClaims = await _roleManager.GetClaimsAsync(role);
                        var rolePermissions = roleClaims
                            .Where(c => c.Type == PERMISSION_CLAIM_TYPE)
                            .Select(c => c.Value)
                            .ToList();

                        permissions.AddRange(rolePermissions);
                    }
                }

                // Also get user's direct claims
                var userClaims = await _userManager.GetClaimsAsync(user);
                var userPermissions = userClaims
                    .Where(c => c.Type == PERMISSION_CLAIM_TYPE)
                    .Select(c => c.Value)
                    .ToList();

                permissions.AddRange(userPermissions);

                // Cache the permissions
                _cache.Set(cacheKey, permissions, TimeSpan.FromMinutes(30));

                return permissions.Distinct().ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user permissions: {UserId}", userId);
                return new List<string>();
            }
        }

        /// <summary>
        /// Gets all permissions for a role
        /// </summary>
        /// <param name="roleId">The role ID</param>
        /// <returns>A list of permission codes</returns>
        public async Task<List<string>> GetRolePermissionsAsync(string roleId)
        {
            try
            {
                // Try to get from cache first
                var cacheKey = CacheKeys.Permission.GetRolePermissionsKey(Guid.Parse(roleId));
                if (_cache.TryGetValue(cacheKey, out List<string> cachedPermissions))
                {
                    return cachedPermissions;
                }

                var role = await _roleManager.FindByIdAsync(roleId);
                if (role == null)
                {
                    _logger.LogWarning("Role not found: {RoleId}", roleId);
                    return new List<string>();
                }

                // Get role claims
                var roleClaims = await _roleManager.GetClaimsAsync(role);
                var permissions = roleClaims
                    .Where(c => c.Type == PERMISSION_CLAIM_TYPE)
                    .Select(c => c.Value)
                    .ToList();

                // Cache the permissions
                _cache.Set(cacheKey, permissions, TimeSpan.FromMinutes(30));

                return permissions;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting role permissions: {RoleId}", roleId);
                return new List<string>();
            }
        }

        /// <summary>
        /// Adds a permission to a role
        /// </summary>
        /// <param name="roleId">The role ID</param>
        /// <param name="permission">The permission to add</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> AddPermissionToRoleAsync(string roleId, string permission)
        {
            try
            {
                var role = await _roleManager.FindByIdAsync(roleId);
                if (role == null)
                {
                    _logger.LogWarning("Role not found: {RoleId}", roleId);
                    return false;
                }

                // Check if the role already has this permission
                var roleClaims = await _roleManager.GetClaimsAsync(role);
                if (roleClaims.Any(c => c.Type == PERMISSION_CLAIM_TYPE && c.Value == permission))
                {
                    // Permission already exists
                    return true;
                }

                // Add the permission claim
                var result = await _roleManager.AddClaimAsync(role, new Claim(PERMISSION_CLAIM_TYPE, permission));
                if (!result.Succeeded)
                {
                    _logger.LogError("Failed to add permission {Permission} to role {RoleId}: {Errors}",
                        permission, roleId, string.Join(", ", result.Errors.Select(e => e.Description)));
                    return false;
                }

                // Clear cache
                var cacheKey = CacheKeys.Permission.GetRolePermissionsKey(Guid.Parse(roleId));
                _cache.Remove(cacheKey);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding permission to role: {RoleId}, {Permission}", roleId, permission);
                return false;
            }
        }

        /// <summary>
        /// Removes a permission from a role
        /// </summary>
        /// <param name="roleId">The role ID</param>
        /// <param name="permission">The permission to remove</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> RemovePermissionFromRoleAsync(string roleId, string permission)
        {
            try
            {
                var role = await _roleManager.FindByIdAsync(roleId);
                if (role == null)
                {
                    _logger.LogWarning("Role not found: {RoleId}", roleId);
                    return false;
                }

                // Check if the role has this permission
                var roleClaims = await _roleManager.GetClaimsAsync(role);
                var claim = roleClaims.FirstOrDefault(c => c.Type == PERMISSION_CLAIM_TYPE && c.Value == permission);
                if (claim == null)
                {
                    // Permission doesn't exist
                    return true;
                }

                // Remove the permission claim
                var result = await _roleManager.RemoveClaimAsync(role, claim);
                if (!result.Succeeded)
                {
                    _logger.LogError("Failed to remove permission {Permission} from role {RoleId}: {Errors}",
                        permission, roleId, string.Join(", ", result.Errors.Select(e => e.Description)));
                    return false;
                }

                // Clear cache
                var cacheKey = CacheKeys.Permission.GetRolePermissionsKey(Guid.Parse(roleId));
                _cache.Remove(cacheKey);

                // Also clear user permission caches that might be affected
                // This is a simple approach - in a production system you might want to be more selective
                var userIds = await _userManager.GetUsersInRoleAsync(role.Name);
                foreach (var user in userIds)
                {
                    var userCacheKey = CacheKeys.Permission.GetUserPermissionsKey(user.Id);
                    _cache.Remove(userCacheKey);
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing permission from role: {RoleId}, {Permission}", roleId, permission);
                return false;
            }
        }

        /// <summary>
        /// Adds a permission directly to a user
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="permission">The permission to add</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> AddPermissionToUserAsync(string userId, string permission)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null)
                {
                    _logger.LogWarning("User not found: {UserId}", userId);
                    return false;
                }

                // Check if the user already has this permission
                var userClaims = await _userManager.GetClaimsAsync(user);
                if (userClaims.Any(c => c.Type == PERMISSION_CLAIM_TYPE && c.Value == permission))
                {
                    // Permission already exists
                    return true;
                }

                // Add the permission claim
                var result = await _userManager.AddClaimAsync(user, new Claim(PERMISSION_CLAIM_TYPE, permission));
                if (!result.Succeeded)
                {
                    _logger.LogError("Failed to add permission {Permission} to user {UserId}: {Errors}",
                        permission, userId, string.Join(", ", result.Errors.Select(e => e.Description)));
                    return false;
                }

                // Clear cache
                var cacheKey = CacheKeys.Permission.GetUserPermissionsKey(userId);
                _cache.Remove(cacheKey);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding permission to user: {UserId}, {Permission}", userId, permission);
                return false;
            }
        }

        /// <summary>
        /// Removes a permission from a user
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="permission">The permission to remove</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> RemovePermissionFromUserAsync(string userId, string permission)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null)
                {
                    _logger.LogWarning("User not found: {UserId}", userId);
                    return false;
                }

                // Check if the user has this permission
                var userClaims = await _userManager.GetClaimsAsync(user);
                var claim = userClaims.FirstOrDefault(c => c.Type == PERMISSION_CLAIM_TYPE && c.Value == permission);
                if (claim == null)
                {
                    // Permission doesn't exist
                    return true;
                }

                // Remove the permission claim
                var result = await _userManager.RemoveClaimAsync(user, claim);
                if (!result.Succeeded)
                {
                    _logger.LogError("Failed to remove permission {Permission} from user {UserId}: {Errors}",
                        permission, userId, string.Join(", ", result.Errors.Select(e => e.Description)));
                    return false;
                }

                // Clear cache
                var cacheKey = CacheKeys.Permission.GetUserPermissionsKey(userId);
                _cache.Remove(cacheKey);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing permission from user: {UserId}, {Permission}", userId, permission);
                return false;
            }
        }
    }
}
