﻿using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Entity;
using Dolfin.Utility.Models;
using Microsoft.EntityFrameworkCore;
using Npgsql.TypeMapping;
using System;
using System.Linq.Expressions;
using System.Reflection;
using static Dolfin.Utility.Enum.Enums;

namespace Dolfin.Mobile.API.Helper
{
    public class SharedFunctionHelper
    {
        /// <summary>
        /// Allow to view all branches
        /// </summary>
        /// <param name="userTypeEnum"></param>
        /// <returns></returns>
        public static bool PermissionViewLimitCompanyOrBranchAll(UserTypeEnum userTypeEnum)
        {
            //search
            if ((int)userTypeEnum == (int)UserTypeEnum.Admin || (int)userTypeEnum == (int)UserTypeEnum.SuperAdmin)
                return true;

            return false;
        }

        /// <summary>
        /// allow admin allow to view only. Example deleted data
        /// </summary>
        /// <param name="userTypeEnum"></param>
        /// <returns></returns>
        public static bool PermissionViewAll(UserTypeEnum userTypeEnum)
        {
            //search
            if ((int)userTypeEnum == (int)UserTypeEnum.Admin)
                return true;

            return false;
        }

        public static PagedList<T> StandardPagination<T>(IEnumerable<T> source, Pagination? pagination)
        {
            List<T> response = new List<T>();
            int totalCount = source.ToList().Count;
            if (pagination != null && pagination.PageSize > 0)
            {
                if (!string.IsNullOrWhiteSpace(pagination.SortBy))
                {
                    // Get the property info for sorting
                    var propertyInfo = typeof(T).GetProperty(pagination.SortBy);

                    if (propertyInfo != null)
                    {
                        // Apply sorting based on SortType (ASC/DESC)
                        source = pagination.SortType.ToUpper() == "ASC"
                            ? source.OrderBy(x => propertyInfo.GetValue(x, null))
                            : source.OrderByDescending(x => propertyInfo.GetValue(x, null));
                    }
                }

                response = source.Skip((pagination.PageNumber - 1) * pagination.PageSize)
                            .Take(pagination.PageSize)
                            .ToList();
            }
            else
            {
                response = source.ToList();
                if (pagination == null)
                {
                    pagination = new Pagination();
                    pagination.PageNumber = 1;
                    pagination.PageSize = response.Count;
                }
            }

            return PagedList<T>.ToPagedList(response, totalCount, pagination.PageNumber, pagination.PageSize);
        }


        /// <summary>
        /// Updates an entity with non-null values from a DTO
        /// </summary>
        /// <typeparam name="TEntity">The entity type</typeparam>
        /// <typeparam name="TDto">The DTO type</typeparam>
        /// <param name="context">The database context</param>
        /// <param name="entity">The entity to update</param>
        /// <param name="dto">The DTO containing update values</param>
        /// <param name="excludeProperties">Optional array of property names to exclude from updates</param>
        /// <returns>The updated entity</returns>
        public static TEntity UpdateEntityFromDto<TEntity, TDto>(
            DbContext context,
            TEntity entity,
            TDto dto,
            params string[] excludeProperties)
            where TEntity : class
            where TDto : class
        {
            if (entity == null || dto == null)
                return entity;

            // Create a HashSet of excluded properties for faster lookup
            var excludedProps = new HashSet<string>(excludeProperties ?? Array.Empty<string>(), StringComparer.OrdinalIgnoreCase);

            // Get all public instance properties of the DTO
            var dtoProperties = dto.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance);

            foreach (var dtoProp in dtoProperties)
            {
                // Skip excluded properties
                if (excludedProps.Contains(dtoProp.Name))
                    continue;

                // Get the value from the DTO
                var value = dtoProp.GetValue(dto);

                // Only update if the value is not null
                if (value != null)
                {
                    try
                    {
                        // Try to find the matching property in the entity
                        var entityProp = entity.GetType().GetProperty(dtoProp.Name,
                            BindingFlags.Public | BindingFlags.Instance | BindingFlags.IgnoreCase);

                        if (entityProp != null && entityProp.CanWrite)
                        {
                            // Check if the property types are compatible
                            if (entityProp.PropertyType.IsAssignableFrom(dtoProp.PropertyType))
                            {
                                // Update the property value directly
                                entityProp.SetValue(entity, value);
                            }
                            else if (entityProp.PropertyType.IsEnum && value is string)
                            {
                                // Handle enum conversion from string
                                var enumValue = Enum.Parse(entityProp.PropertyType, value.ToString());
                                entityProp.SetValue(entity, enumValue);
                            }
                            else if (entityProp.PropertyType == typeof(Guid) && value is string)
                            {
                                // Handle Guid conversion from string
                                var guidValue = Guid.Parse(value.ToString());
                                entityProp.SetValue(entity, guidValue);
                            }
                            else if (entityProp.PropertyType == typeof(DateTime) && value is string)
                            {
                                // Handle DateTime conversion from string
                                var dateValue = DateTime.Parse(value.ToString());
                                entityProp.SetValue(entity, dateValue);
                            }
                            else
                            {
                                try
                                {
                                    // Try to convert the value if types are not directly compatible
                                    var convertedValue = Convert.ChangeType(value, entityProp.PropertyType);
                                    entityProp.SetValue(entity, convertedValue);
                                }
                                catch
                                {
                                    // If conversion fails, log or handle the error
                                    Console.WriteLine($"Failed to convert property {dtoProp.Name} from {value.GetType()} to {entityProp.PropertyType}");
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log the exception but continue with other properties
                        Console.WriteLine($"Error updating property {dtoProp.Name}: {ex.Message}");
                    }
                }
            }

            return entity;
        }

        #region Calculation formula
        #region Transaction
        public static (decimal salesTaxAmount, decimal serviceTaxAmount, decimal taxExclAmount, decimal taxInclAmount, decimal totalUnitAmountWOTax, decimal totalUnitAmount, decimal subTotalAmount) CalculateTax(decimal unitAmount, decimal quantity, decimal salesTaxRate, decimal serviceTaxRate, bool IsTaxExcl)
        {
            // Truncate inputs to 5 decimal places
            unitAmount = TruncateToFiveDecimalPlaces(unitAmount);
            quantity = TruncateToFiveDecimalPlaces(quantity);
            decimal salesTaxAmount = 0.00m;
            decimal serviceTaxAmount = 0.00m;
            decimal taxExclAmount = 0.00m;
            decimal taxInclAmount = 0.00m;
            decimal totalUnitAmountWOTax = 0.00m;
            decimal subTotalAmount = 0.00m;
            decimal taxAmount = 0.00m;

            // Calculate total amount before tax
            decimal totalUnitAmount = Math.Round(unitAmount * quantity, 2, MidpointRounding.AwayFromZero);

            // Calculate tax amount
            if (IsTaxExcl)
            {
                salesTaxAmount = Math.Round(totalUnitAmount * (salesTaxRate / 100), 2, MidpointRounding.AwayFromZero);
                serviceTaxAmount = Math.Round(totalUnitAmount * (serviceTaxRate / 100), 2, MidpointRounding.AwayFromZero);
                taxAmount = salesTaxAmount + serviceTaxAmount;
                taxExclAmount = taxAmount;
                subTotalAmount = totalUnitAmount + taxAmount;
            }
            else
            {
                salesTaxAmount = totalUnitAmount - Math.Round(totalUnitAmount / (1 + (salesTaxRate / 100)), 2, MidpointRounding.AwayFromZero);
                serviceTaxAmount = totalUnitAmount - Math.Round(totalUnitAmount / (1 + (serviceTaxRate / 100)), 2, MidpointRounding.AwayFromZero);
                taxAmount = salesTaxAmount + serviceTaxAmount;
                taxInclAmount = taxAmount;
                subTotalAmount = totalUnitAmount;
            }

            return (salesTaxAmount, serviceTaxAmount, taxExclAmount, taxInclAmount, totalUnitAmountWOTax, totalUnitAmount, subTotalAmount);
        }


        public static (decimal grandTotalAmount, decimal grandTotalAdjustmentAmount) CalculateRoundingAdjustment(decimal subTotalAmount)
        {
            decimal grandTotalAmount = Math.Round(subTotalAmount * 20, MidpointRounding.AwayFromZero) / 20;
            decimal grandTotalAdjustmentAmount = subTotalAmount - grandTotalAmount;
            return (grandTotalAmount, grandTotalAdjustmentAmount);
        }

        private static decimal TruncateToFiveDecimalPlaces(decimal value)
        {
            return Math.Truncate(value * 100000m) / 100000m;
        }

        #endregion
        #region Inventory
        public static int CalculationUnitQuantity(decimal fractionTotal, decimal stockQuantity)
        {
            return (int)(fractionTotal * stockQuantity);
        }
        #endregion
        #endregion
    }
}
