﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Dolfin.Utility.Enum.Enums;

namespace Dolfin.Framework.Data.Domains
{
    public partial class TraceLogIntegration : _BaseDomain
    {
        public TraceLogIntegration()
        {
        }
        public string? TransactionId { get; set; }
        public required string Url { get; set; }
        public required string Type { get; set; }
        public string? Request { get; set; }
		public string? ResponseCode { get; set; }
		public string? Response { get; set; }
        public DateTime StartTime { get; set; }
		public DateTime EndTime { get; set; }

	}
}
