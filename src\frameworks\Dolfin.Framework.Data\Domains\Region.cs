﻿using System;
using System.Collections.Generic;

namespace  Dolfin.Framework.Data.Domains
{
    public partial class Region : _BaseDomain
    {
        public Region()
        {
            Address = new HashSet<Address>();
            RegionState = new HashSet<RegionState>();
        }

        public required string Name { get; set; }
        public bool Published { get; set; }
        public int? DisplayOrder { get; set; }

        public virtual ICollection<Address> Address { get; set; }
        public virtual ICollection<RegionState> RegionState { get; set; }
    }
}
