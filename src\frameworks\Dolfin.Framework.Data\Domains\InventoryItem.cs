﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Framework.Data.Domains
{
    public partial class InventoryItem : _BaseDomain
    {
        public InventoryItem()
        {
            InventoryItemTransactionItem = new HashSet<InventoryItemTransactionItem>();
        }
        public DateTime? ExpireAt { get; set; }
        public DateTime? ManufacturingAt { get; set; }
        public int? ManufacturingYear { get; set; }
        public decimal Cost { get; set; }
        public decimal StockQuantity { get; set; }
        public decimal BalanceQuantity { get; set; }
        public decimal SafeQuantity { get; set; }
        public DateTime StockInAt { get; set; }
        public Guid ProductUOMId { get; set; }
        public Guid ProductId { get; set; }
        public Guid InventoryProductId { get; set; }
        public virtual ProductUOM? ProductUOM { get; set; }
        public virtual Product? Product { get; set; }
        public virtual InventoryProduct? InventoryProduct { get; set; }
        public virtual ICollection<InventoryItemTransactionItem> InventoryItemTransactionItem { get; }
    }
}
