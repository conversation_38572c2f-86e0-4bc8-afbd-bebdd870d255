﻿using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Domains.CustomIdentity;
using Dolfin.Mobile.API.Models;
using Dolfin.Mobile.API.Models.Request;
using Dolfin.Utility.Models;
using System.Security.Claims;

namespace Dolfin.Mobile.API.Services
{
    public interface IEinvoiceService
    {
        Task<BaseResponse<EInvoiceAuthResponse>> LoginTaxpayerSystem(EInvoiceAuthRequest? loginAuthSystem = null);
        Task<BaseResponse<EInvoiceSearchTinResponse>> SearchTin(EInvoiceSearchTinRequest reqBody);
        Task<BaseResponse<EInvoiceValidateTinResponse>> ValidateTin(EInvoiceValidateTinRequest reqBody);
        Task<BaseResponse<EInvoiceDocumentTypesResponse>> GetAllDocumentTypes();
        Task<BaseResponse<EInvoiceSubmitDocumentResponse>> SubmitDocument(EInvoiceSubmitDocumentRequest reqBody);
        Task<BaseResponse<EInvoiceCancelRejectResponse>> CancelDocument(EInvoiceCancelRequest reqBody);
        Task<BaseResponse<EInvoiceCancelRejectResponse>> RejectDocument(EInvoiceRejectRequest reqBody);
    }
}
