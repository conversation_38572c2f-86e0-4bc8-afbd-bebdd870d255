﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Dolfin.Mobile.API.Models
{
    public class EmailMessage
    {
        public List<EmailAddress> ToAddresses { get; set; }
        public List<EmailAddress> FromAddresses { get; set; }
        public List<EmailAddress> CCAddresses { get; set; }
        public List<EmailAddress> BCCAddresses { get; set; }


        public string Subject { get; set; }
        public string Content { get; set; }
        public DateTime Date { get; set; }
        public List<EmailAttachment> Attachments { get; set; }

        public EmailMessage()
        {
            ToAddresses = new List<EmailAddress>();
            FromAddresses = new List<EmailAddress>();
            CCAddresses = new List<EmailAddress>();
            BCCAddresses = new List<EmailAddress>();
            Attachments = new List<EmailAttachment>();
        }
    }
}
