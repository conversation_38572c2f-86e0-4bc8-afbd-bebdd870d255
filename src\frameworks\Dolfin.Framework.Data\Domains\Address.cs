﻿using System;
using System.Collections.Generic;
using System.Diagnostics.Metrics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Framework.Data.Domains
{
    public partial class Address : _BaseDomain
    {
        public Address()
        {
            Branch = new HashSet<Branch>();
            Customer = new HashSet<Customer>();
            TransactionShippingAddress = new HashSet<Transaction>();
            TransactionBillingAddress = new HashSet<Transaction>();
        }
        public required string FirstName { get; set; }
        public required string LastName { get; set; }
        public required string Email { get; set; }
        public string? Company { get; set; }
        public required string Address1 { get; set; }
        public string? Address2 { get; set; }
        public string? Address3 { get; set; }
        public required string PostalCode { get; set; }
        public required string PhoneNo { get; set; }
        public string? FaxNo { get; set; }
        public string? Coordinate { get; set; }
        public Guid CountryId { get; set; }
        public Guid StateProvinceId { get; set; }
        public Guid RegionId { get; set; }
        public virtual Country? Country { get; set; }
        public virtual State? State { get; set; }
        public virtual Region? Region { get; set; }
        public virtual ICollection<Branch> Branch { get; }
        public virtual ICollection<Customer> Customer { get; }
        public virtual ICollection<Transaction> TransactionShippingAddress { get; }
        public virtual ICollection<Transaction> TransactionBillingAddress { get; }

    }
}
