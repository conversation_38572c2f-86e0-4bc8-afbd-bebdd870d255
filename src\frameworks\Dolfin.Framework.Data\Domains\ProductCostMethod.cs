﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Framework.Data.Domains
{
    public partial class ProductCostMethod : _BaseDomain
    {
        public ProductCostMethod()
        {
            Product = new HashSet<Product>();
        }
        public required string Code { get; set; }
        public required string Name { get; set; }
        public string? Description { get; set; }
        public virtual ICollection<Product> Product { get; }

    }
}
